{"version": 3, "sources": ["../../../src/language/pie/tokenBuilder.ts", "../../../src/language/pie/valueConverter.ts", "../../../src/language/pie/module.ts"], "sourcesContent": ["import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class PieTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['pie', 'showData']);\n  }\n}\n", "import type { CstNode, GrammarAST, ValueType } from 'langium';\n\nimport { AbstractMermaidValueConverter } from '../common/index.js';\n\nexport class PieValueConverter extends AbstractMermaidValueConverter {\n  protected runCustomConverter(\n    rule: GrammarAST.AbstractRule,\n    input: string,\n    _cstNode: CstNode\n  ): ValueType | undefined {\n    if (rule.name !== 'PIE_SECTION_LABEL') {\n      return undefined;\n    }\n    return input.replace(/\"/g, '').trim();\n  }\n}\n", "import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject,\n} from 'langium';\n\nimport { MermaidGeneratedSharedModule, PieGeneratedModule } from '../generated/module.js';\nimport { PieTokenBuilder } from './tokenBuilder.js';\nimport { PieValueConverter } from './valueConverter.js';\n\n/**\n * Declaration of `Pie` services.\n */\ninterface PieAddedServices {\n  parser: {\n    TokenBuilder: PieTokenBuilder;\n    ValueConverter: PieValueConverter;\n  };\n}\n\n/**\n * Union of Langium default services and `Pie` services.\n */\nexport type PieServices = LangiumCoreServices & PieAddedServices;\n\n/**\n * Dependency injection module that overrides Langium default services and\n * contributes the declared `Pie` services.\n */\nexport const PieModule: Module<PieServices, PartialLangiumCoreServices & PieAddedServices> = {\n  parser: {\n    TokenBuilder: () => new PieTokenBuilder(),\n    ValueConverter: () => new PieValueConverter(),\n  },\n};\n\n/**\n * Create the full set of services required by Langium.\n *\n * First inject the shared services by merging two modules:\n *  - Langium default shared services\n *  - Services generated by langium-cli\n *\n * Then inject the language-specific services by merging three modules:\n *  - Langium default language-specific services\n *  - Services generated by langium-cli\n *  - Services specified in this file\n * @param context - Optional module context with the LSP connection\n * @returns An object wrapping the shared services and the language-specific services\n */\nexport function createPieServices(context: DefaultSharedCoreModuleContext = EmptyFileSystem): {\n  shared: LangiumSharedCoreServices;\n  Pie: PieServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Pie: PieServices = inject(\n    createDefaultCoreModule({ shared }),\n    PieGeneratedModule,\n    PieModule\n  );\n  shared.ServiceRegistry.register(Pie);\n  return { shared, Pie };\n}\n"], "mappings": ";;;;;;;;;;;;;;AAEO,IAAM,kBAAN,cAA8B,4BAA4B;AAAA,EAFjE,OAEiE;AAAA;AAAA;AAAA,EACxD,cAAc;AACnB,UAAM,CAAC,OAAO,UAAU,CAAC;AAAA,EAC3B;AACF;;;ACFO,IAAM,oBAAN,cAAgC,8BAA8B;AAAA,EAJrE,OAIqE;AAAA;AAAA;AAAA,EACzD,mBACR,MACA,OACA,UACuB;AACvB,QAAI,KAAK,SAAS,qBAAqB;AACrC,aAAO;AAAA,IACT;AACA,WAAO,MAAM,QAAQ,MAAM,EAAE,EAAE,KAAK;AAAA,EACtC;AACF;;;ACsBO,IAAM,YAAgF;AAAA,EAC3F,QAAQ;AAAA,IACN,cAAc,6BAAM,IAAI,gBAAgB,GAA1B;AAAA,IACd,gBAAgB,6BAAM,IAAI,kBAAkB,GAA5B;AAAA,EAClB;AACF;AAgBO,SAAS,kBAAkB,UAA0C,iBAG1E;AACA,QAAM,SAAoC;AAAA,IACxC,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,MAAmB;AAAA,IACvB,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,GAAG;AACnC,SAAO,EAAE,QAAQ,IAAI;AACvB;AAfgB;", "names": []}