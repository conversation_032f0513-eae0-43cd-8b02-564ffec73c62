{"version": 3, "sources": ["../../../src/language/info/tokenBuilder.ts", "../../../src/language/info/module.ts"], "sourcesContent": ["import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class InfoTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['info', 'showInfo']);\n  }\n}\n", "import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject,\n} from 'langium';\n\nimport { CommonValueConverter } from '../common/index.js';\nimport { InfoGeneratedModule, MermaidGeneratedSharedModule } from '../generated/module.js';\nimport { InfoTokenBuilder } from './tokenBuilder.js';\n\n/**\n * Declaration of `Info` services.\n */\ninterface InfoAddedServices {\n  parser: {\n    TokenBuilder: InfoTokenBuilder;\n    ValueConverter: CommonValueConverter;\n  };\n}\n\n/**\n * Union of Langium default services and `Info` services.\n */\nexport type InfoServices = LangiumCoreServices & InfoAddedServices;\n\n/**\n * Dependency injection module that overrides Langium default services and\n * contributes the declared `Info` services.\n */\nexport const InfoModule: Module<InfoServices, PartialLangiumCoreServices & InfoAddedServices> = {\n  parser: {\n    TokenBuilder: () => new InfoTokenBuilder(),\n    ValueConverter: () => new CommonValueConverter(),\n  },\n};\n\n/**\n * Create the full set of services required by Langium.\n *\n * First inject the shared services by merging two modules:\n *  - Langium default shared services\n *  - Services generated by langium-cli\n *\n * Then inject the language-specific services by merging three modules:\n *  - Langium default language-specific services\n *  - Services generated by langium-cli\n *  - Services specified in this file\n * @param context - Optional module context with the LSP connection\n * @returns An object wrapping the shared services and the language-specific services\n */\nexport function createInfoServices(context: DefaultSharedCoreModuleContext = EmptyFileSystem): {\n  shared: LangiumSharedCoreServices;\n  Info: InfoServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Info: InfoServices = inject(\n    createDefaultCoreModule({ shared }),\n    InfoGeneratedModule,\n    InfoModule\n  );\n  shared.ServiceRegistry.register(Info);\n  return { shared, Info };\n}\n"], "mappings": ";;;;;;;;;;;;;;AAEO,IAAM,mBAAN,cAA+B,4BAA4B;AAAA,EAFlE,OAEkE;AAAA;AAAA;AAAA,EACzD,cAAc;AACnB,UAAM,CAAC,QAAQ,UAAU,CAAC;AAAA,EAC5B;AACF;;;AC+BO,IAAM,aAAmF;AAAA,EAC9F,QAAQ;AAAA,IACN,cAAc,6BAAM,IAAI,iBAAiB,GAA3B;AAAA,IACd,gBAAgB,6BAAM,IAAI,qBAAqB,GAA/B;AAAA,EAClB;AACF;AAgBO,SAAS,mBAAmB,UAA0C,iBAG3E;AACA,QAAM,SAAoC;AAAA,IACxC,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,OAAqB;AAAA,IACzB,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,IAAI;AACpC,SAAO,EAAE,QAAQ,KAAK;AACxB;AAfgB;", "names": []}