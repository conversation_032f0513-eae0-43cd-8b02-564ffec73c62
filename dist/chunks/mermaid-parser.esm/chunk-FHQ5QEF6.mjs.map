{"version": 3, "sources": ["../../../src/language/gitGraph/tokenBuilder.ts", "../../../src/language/gitGraph/module.ts"], "sourcesContent": ["import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class GitGraphTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['gitGraph']);\n  }\n}\n", "import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  inject,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  EmptyFileSystem,\n} from 'langium';\nimport { CommonValueConverter } from '../common/valueConverter.js';\nimport { MermaidGeneratedSharedModule, GitGraphGeneratedModule } from '../generated/module.js';\nimport { GitGraphTokenBuilder } from './tokenBuilder.js';\n\ninterface GitGraphAddedServices {\n  parser: {\n    TokenBuilder: GitGraphTokenBuilder;\n    ValueConverter: CommonValueConverter;\n  };\n}\n\nexport type GitGraphServices = LangiumCoreServices & GitGraphAddedServices;\n\nexport const GitGraphModule: Module<\n  GitGraphServices,\n  PartialLangiumCoreServices & GitGraphAddedServices\n> = {\n  parser: {\n    TokenBuilder: () => new GitGraphTokenBuilder(),\n    ValueConverter: () => new CommonValueConverter(),\n  },\n};\n\nexport function createGitGraphServices(context: DefaultSharedCoreModuleContext = EmptyFileSystem): {\n  shared: LangiumSharedCoreServices;\n  GitGraph: GitGraphServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const GitGraph: GitGraphServices = inject(\n    createDefaultCoreModule({ shared }),\n    GitGraphGeneratedModule,\n    GitGraphModule\n  );\n  shared.ServiceRegistry.register(GitGraph);\n  return { shared, GitGraph };\n}\n"], "mappings": ";;;;;;;;;;;;;;AAEO,IAAM,uBAAN,cAAmC,4BAA4B;AAAA,EAFtE,OAEsE;AAAA;AAAA;AAAA,EAC7D,cAAc;AACnB,UAAM,CAAC,UAAU,CAAC;AAAA,EACpB;AACF;;;ACoBO,IAAM,iBAGT;AAAA,EACF,QAAQ;AAAA,IACN,cAAc,6BAAM,IAAI,qBAAqB,GAA/B;AAAA,IACd,gBAAgB,6BAAM,IAAI,qBAAqB,GAA/B;AAAA,EAClB;AACF;AAEO,SAAS,uBAAuB,UAA0C,iBAG/E;AACA,QAAM,SAAoC;AAAA,IACxC,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,WAA6B;AAAA,IACjC,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,QAAQ;AACxC,SAAO,EAAE,QAAQ,SAAS;AAC5B;AAfgB;", "names": []}