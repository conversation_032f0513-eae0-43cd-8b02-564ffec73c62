{"version": 3, "sources": ["../../../src/language/architecture/tokenBuilder.ts", "../../../src/language/architecture/valueConverter.ts", "../../../src/language/architecture/module.ts"], "sourcesContent": ["import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class ArchitectureTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['architecture']);\n  }\n}\n", "import type { CstNode, GrammarAST, ValueType } from 'langium';\n\nimport { AbstractMermaidValueConverter } from '../common/index.js';\n\nexport class ArchitectureValueConverter extends AbstractMermaidValueConverter {\n  protected runCustomConverter(\n    rule: GrammarAST.AbstractRule,\n    input: string,\n    _cstNode: CstNode\n  ): ValueType | undefined {\n    if (rule.name === 'ARCH_ICON') {\n      return input.replace(/[()]/g, '').trim();\n    } else if (rule.name === 'ARCH_TEXT_ICON') {\n      return input.replace(/[\"()]/g, '');\n    } else if (rule.name === 'ARCH_TITLE') {\n      return input.replace(/[[\\]]/g, '').trim();\n    }\n    return undefined;\n  }\n}\n", "import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject,\n} from 'langium';\n\nimport { MermaidGeneratedSharedModule, ArchitectureGeneratedModule } from '../generated/module.js';\nimport { ArchitectureTokenBuilder } from './tokenBuilder.js';\nimport { ArchitectureValueConverter } from './valueConverter.js';\n\n/**\n * Declaration of `Architecture` services.\n */\ninterface ArchitectureAddedServices {\n  parser: {\n    TokenBuilder: ArchitectureTokenBuilder;\n    ValueConverter: ArchitectureValueConverter;\n  };\n}\n\n/**\n * Union of Langium default services and `Architecture` services.\n */\nexport type ArchitectureServices = LangiumCoreServices & ArchitectureAddedServices;\n\n/**\n * Dependency injection module that overrides Langium default services and\n * contributes the declared `Architecture` services.\n */\nexport const ArchitectureModule: Module<\n  ArchitectureServices,\n  PartialLangiumCoreServices & ArchitectureAddedServices\n> = {\n  parser: {\n    TokenBuilder: () => new ArchitectureTokenBuilder(),\n    ValueConverter: () => new ArchitectureValueConverter(),\n  },\n};\n\n/**\n * Create the full set of services required by Langium.\n *\n * First inject the shared services by merging two modules:\n *  - Langium default shared services\n *  - Services generated by langium-cli\n *\n * Then inject the language-specific services by merging three modules:\n *  - Langium default language-specific services\n *  - Services generated by langium-cli\n *  - Services specified in this file\n * @param context - Optional module context with the LSP connection\n * @returns An object wrapping the shared services and the language-specific services\n */\nexport function createArchitectureServices(\n  context: DefaultSharedCoreModuleContext = EmptyFileSystem\n): {\n  shared: LangiumSharedCoreServices;\n  Architecture: ArchitectureServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Architecture: ArchitectureServices = inject(\n    createDefaultCoreModule({ shared }),\n    ArchitectureGeneratedModule,\n    ArchitectureModule\n  );\n  shared.ServiceRegistry.register(Architecture);\n  return { shared, Architecture };\n}\n"], "mappings": ";;;;;;;;;;;;;;AAEO,IAAM,2BAAN,cAAuC,4BAA4B;AAAA,EAF1E,OAE0E;AAAA;AAAA;AAAA,EACjE,cAAc;AACnB,UAAM,CAAC,cAAc,CAAC;AAAA,EACxB;AACF;;;ACFO,IAAM,6BAAN,cAAyC,8BAA8B;AAAA,EAJ9E,OAI8E;AAAA;AAAA;AAAA,EAClE,mBACR,MACA,OACA,UACuB;AACvB,QAAI,KAAK,SAAS,aAAa;AAC7B,aAAO,MAAM,QAAQ,SAAS,EAAE,EAAE,KAAK;AAAA,IACzC,WAAW,KAAK,SAAS,kBAAkB;AACzC,aAAO,MAAM,QAAQ,UAAU,EAAE;AAAA,IACnC,WAAW,KAAK,SAAS,cAAc;AACrC,aAAO,MAAM,QAAQ,UAAU,EAAE,EAAE,KAAK;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AACF;;;ACkBO,IAAM,qBAGT;AAAA,EACF,QAAQ;AAAA,IACN,cAAc,6BAAM,IAAI,yBAAyB,GAAnC;AAAA,IACd,gBAAgB,6BAAM,IAAI,2BAA2B,GAArC;AAAA,EAClB;AACF;AAgBO,SAAS,2BACd,UAA0C,iBAI1C;AACA,QAAM,SAAoC;AAAA,IACxC,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,eAAqC;AAAA,IACzC,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,YAAY;AAC5C,SAAO,EAAE,QAAQ,aAAa;AAChC;AAjBgB;", "names": []}