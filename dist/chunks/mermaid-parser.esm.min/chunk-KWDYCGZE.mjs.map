{"version": 3, "sources": ["../../../src/language/gitGraph/tokenBuilder.ts", "../../../src/language/gitGraph/module.ts"], "sourcesContent": ["import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class GitGraphTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['gitGraph']);\n  }\n}\n", "import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  inject,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  EmptyFileSystem,\n} from 'langium';\nimport { CommonValueConverter } from '../common/valueConverter.js';\nimport { MermaidGeneratedSharedModule, GitGraphGeneratedModule } from '../generated/module.js';\nimport { GitGraphTokenBuilder } from './tokenBuilder.js';\n\ninterface GitGraphAddedServices {\n  parser: {\n    TokenBuilder: GitGraphTokenBuilder;\n    ValueConverter: CommonValueConverter;\n  };\n}\n\nexport type GitGraphServices = LangiumCoreServices & GitGraphAddedServices;\n\nexport const GitGraphModule: Module<\n  GitGraphServices,\n  PartialLangiumCoreServices & GitGraphAddedServices\n> = {\n  parser: {\n    TokenBuilder: () => new GitGraphTokenBuilder(),\n    ValueConverter: () => new CommonValueConverter(),\n  },\n};\n\nexport function createGitGraphServices(context: DefaultSharedCoreModuleContext = EmptyFileSystem): {\n  shared: LangiumSharedCoreServices;\n  GitGraph: GitGraphServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const GitGraph: GitGraphServices = inject(\n    createDefaultCoreModule({ shared }),\n    GitGraphGeneratedModule,\n    GitGraphModule\n  );\n  shared.ServiceRegistry.register(GitGraph);\n  return { shared, GitGraph };\n}\n"], "mappings": "wGAEO,IAAMA,EAAN,cAAmCC,CAA4B,CAFtE,MAEsE,CAAAC,EAAA,6BAC7D,aAAc,CACnB,MAAM,CAAC,UAAU,CAAC,CACpB,CACF,ECoBO,IAAMC,EAGT,CACF,OAAQ,CACN,aAAcC,EAAA,IAAM,IAAIC,EAAV,gBACd,eAAgBD,EAAA,IAAM,IAAIE,EAAV,iBAClB,CACF,EAEO,SAASC,EAAuBC,EAA0CC,EAG/E,CACA,IAAMC,EAAoCC,EACxCC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAA6BH,EACjCI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAb,CACF,EACA,OAAAO,EAAO,gBAAgB,SAASI,CAAQ,EACjC,CAAE,OAAAJ,EAAQ,SAAAI,CAAS,CAC5B,CAfgBV,EAAAG,EAAA", "names": ["GitGraphTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "GitGraphModule", "__name", "GitGraphTokenBuilder", "CommonValueConverter", "createGitGraphServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "GitGraph", "createDefaultCoreModule", "GitGraphGeneratedModule"]}