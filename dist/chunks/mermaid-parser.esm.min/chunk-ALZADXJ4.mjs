import{C as s,G as m,J as l,L as p,a as e,b as u,c as d,d as a,e as n,f as C}from"./chunk-AWU6ROIC.mjs";var i=class extends p{static{e(this,"ArchitectureTokenBuilder")}constructor(){super(["architecture"])}};var c=class extends l{static{e(this,"ArchitectureValueConverter")}runCustomConverter(r,t,A){if(r.name==="ARCH_ICON")return t.replace(/[()]/g,"").trim();if(r.name==="ARCH_TEXT_ICON")return t.replace(/["()]/g,"");if(r.name==="ARCH_TITLE")return t.replace(/[[\]]/g,"").trim()}};var S={parser:{TokenBuilder:e(()=>new i,"TokenBuilder"),ValueConverter:e(()=>new c,"ValueConverter")}};function N(o=n){let r=a(d(o),s),t=a(u({shared:r}),m,S);return r.ServiceRegistry.register(t),{shared:r,Architecture:t}}e(N,"createArchitectureServices");export{S as a,N as b};
