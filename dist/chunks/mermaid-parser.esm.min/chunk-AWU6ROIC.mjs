var fx=Object.create;var Pa=Object.defineProperty;var dx=Object.getOwnPropertyDescriptor;var px=Object.getOwnPropertyNames;var mx=Object.getPrototypeOf,hx=Object.prototype.hasOwnProperty;var s=(t,e)=>Pa(t,"name",{value:e,configurable:!0});var Ma=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),$r=(t,e)=>{for(var r in e)Pa(t,r,{get:e[r],enumerable:!0})},ql=(t,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of px(e))!hx.call(t,i)&&i!==r&&Pa(t,i,{get:()=>e[i],enumerable:!(n=dx(e,i))||n.enumerable});return t},B=(t,e,r)=>(ql(t,e,"default"),r&&ql(r,e,"default")),Of=(t,e,r)=>(r=t!=null?fx(mx(t)):{},ql(e||!t||!t.__esModule?Pa(r,"default",{value:t,enumerable:!0}):r,t));var qc=Ma(zc=>{"use strict";Object.defineProperty(zc,"__esModule",{value:!0});var Hc;function Vc(){if(Hc===void 0)throw new Error("No runtime abstraction layer installed");return Hc}s(Vc,"RAL");(function(t){function e(r){if(r===void 0)throw new Error("No runtime abstraction layer provided");Hc=r}s(e,"install"),t.install=e})(Vc||(Vc={}));zc.default=Vc});var Ey=Ma(Be=>{"use strict";Object.defineProperty(Be,"__esModule",{value:!0});Be.stringArray=Be.array=Be.func=Be.error=Be.number=Be.string=Be.boolean=void 0;function bN(t){return t===!0||t===!1}s(bN,"boolean");Be.boolean=bN;function Ry(t){return typeof t=="string"||t instanceof String}s(Ry,"string");Be.string=Ry;function $N(t){return typeof t=="number"||t instanceof Number}s($N,"number");Be.number=$N;function ON(t){return t instanceof Error}s(ON,"error");Be.error=ON;function LN(t){return typeof t=="function"}s(LN,"func");Be.func=LN;function Ay(t){return Array.isArray(t)}s(Ay,"array");Be.array=Ay;function PN(t){return Ay(t)&&t.every(e=>Ry(e))}s(PN,"stringArray");Be.stringArray=PN});var Yc=Ma(Yi=>{"use strict";Object.defineProperty(Yi,"__esModule",{value:!0});Yi.Emitter=Yi.Event=void 0;var MN=qc(),vy;(function(t){let e={dispose(){}};t.None=function(){return e}})(vy||(Yi.Event=vy={}));var Xc=class{static{s(this,"CallbackList")}add(e,r=null,n){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(r),Array.isArray(n)&&n.push({dispose:s(()=>this.remove(e,r),"dispose")})}remove(e,r=null){if(!this._callbacks)return;let n=!1;for(let i=0,a=this._callbacks.length;i<a;i++)if(this._callbacks[i]===e)if(this._contexts[i]===r){this._callbacks.splice(i,1),this._contexts.splice(i,1);return}else n=!0;if(n)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];let r=[],n=this._callbacks.slice(0),i=this._contexts.slice(0);for(let a=0,o=n.length;a<o;a++)try{r.push(n[a].apply(i[a],e))}catch(l){(0,MN.default)().console.error(l)}return r}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}},Al=class t{static{s(this,"Emitter")}constructor(e){this._options=e}get event(){return this._event||(this._event=(e,r,n)=>{this._callbacks||(this._callbacks=new Xc),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,r);let i={dispose:s(()=>{this._callbacks&&(this._callbacks.remove(e,r),i.dispose=t._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))},"dispose")};return Array.isArray(n)&&n.push(i),i}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}};Yi.Emitter=Al;Al._noop=function(){}});var Iy=Ma(Ji=>{"use strict";Object.defineProperty(Ji,"__esModule",{value:!0});Ji.CancellationTokenSource=Ji.CancellationToken=void 0;var DN=qc(),FN=Ey(),Jc=Yc(),El;(function(t){t.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Jc.Event.None}),t.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Jc.Event.None});function e(r){let n=r;return n&&(n===t.None||n===t.Cancelled||FN.boolean(n.isCancellationRequested)&&!!n.onCancellationRequested)}s(e,"is"),t.is=e})(El||(Ji.CancellationToken=El={}));var GN=Object.freeze(function(t,e){let r=(0,DN.default)().timer.setTimeout(t.bind(e),0);return{dispose(){r.dispose()}}}),vl=class{static{s(this,"MutableToken")}constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?GN:(this._emitter||(this._emitter=new Jc.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}},Zc=class{static{s(this,"CancellationTokenSource")}get token(){return this._token||(this._token=new vl),this._token}cancel(){this._token?this._token.cancel():this._token=El.Cancelled}dispose(){this._token?this._token instanceof vl&&this._token.dispose():this._token=El.None}};Ji.CancellationTokenSource=Zc});var Re={};$r(Re,{AbstractAstReflection:()=>Or,AbstractCstNode:()=>Js,AbstractLangiumParser:()=>Zs,AbstractParserErrorMessageProvider:()=>Tl,AbstractThreadedAsyncParser:()=>ff,AstUtils:()=>qa,BiMap:()=>nn,Cancellation:()=>_,CompositeCstNodeImpl:()=>en,ContextCache:()=>sn,CstNodeBuilder:()=>Ys,CstUtils:()=>Ga,DEFAULT_TOKENIZE_OPTIONS:()=>wl,DONE_RESULT:()=>Me,DatatypeSymbol:()=>xl,DefaultAstNodeDescriptionProvider:()=>ha,DefaultAstNodeLocator:()=>ya,DefaultAsyncParser:()=>Ca,DefaultCommentProvider:()=>Sa,DefaultConfigurationProvider:()=>xa,DefaultDocumentBuilder:()=>Ta,DefaultDocumentValidator:()=>ma,DefaultHydrator:()=>wa,DefaultIndexManager:()=>Ra,DefaultJsonSerializer:()=>fa,DefaultLangiumDocumentFactory:()=>ra,DefaultLangiumDocuments:()=>na,DefaultLexer:()=>on,DefaultLexerErrorMessageProvider:()=>Ea,DefaultLinker:()=>ia,DefaultNameProvider:()=>sa,DefaultReferenceDescriptionProvider:()=>ga,DefaultReferences:()=>aa,DefaultScopeComputation:()=>oa,DefaultScopeProvider:()=>ca,DefaultServiceRegistry:()=>da,DefaultTokenBuilder:()=>sr,DefaultValueConverter:()=>rn,DefaultWorkspaceLock:()=>Na,DefaultWorkspaceManager:()=>Aa,Deferred:()=>Xe,Disposable:()=>wr,DisposableCache:()=>ts,DocumentCache:()=>Cl,DocumentState:()=>J,DocumentValidator:()=>pt,EMPTY_SCOPE:()=>BN,EMPTY_STREAM:()=>ss,EmptyFileSystem:()=>xf,EmptyFileSystemProvider:()=>Ml,ErrorWithLocation:()=>Dr,GrammarAST:()=>ms,GrammarUtils:()=>Qa,IndentationAwareLexer:()=>yf,IndentationAwareTokenBuilder:()=>Pl,JSDocDocumentationProvider:()=>ka,LangiumCompletionParser:()=>ea,LangiumParser:()=>Qs,LangiumParserErrorMessageProvider:()=>Xi,LeafCstNodeImpl:()=>Qr,LexingMode:()=>ln,MapScope:()=>la,Module:()=>hf,MultiMap:()=>Rt,OperationCancelled:()=>Gt,ParserWorker:()=>df,Reduction:()=>fn,RegExpUtils:()=>Ja,RootCstNodeImpl:()=>qi,SimpleCache:()=>ua,StreamImpl:()=>it,StreamScope:()=>es,TextDocument:()=>Zi,TreeStreamImpl:()=>At,URI:()=>Ye,UriUtils:()=>Je,ValidationCategory:()=>ns,ValidationRegistry:()=>pa,ValueConverter:()=>Ft,WorkspaceCache:()=>rs,assertUnreachable:()=>vt,createCompletionParser:()=>Wc,createDefaultCoreModule:()=>pf,createDefaultSharedCoreModule:()=>mf,createGrammarConfig:()=>Gu,createLangiumParser:()=>Kc,createParser:()=>ta,delayNextTick:()=>Qc,diagnosticData:()=>an,eagerLoad:()=>Ky,getDiagnosticRange:()=>Oy,indentationBuilderDefaultOptions:()=>gf,inject:()=>Ll,interruptAndCheck:()=>Te,isAstNode:()=>fe,isAstNodeDescription:()=>Xl,isAstNodeWithComment:()=>rf,isCompositeCstNode:()=>ht,isIMultiModeLexerDefinition:()=>sf,isJSDoc:()=>uf,isLeafCstNode:()=>ur,isLinkingError:()=>Lr,isNamed:()=>by,isOperationCancelled:()=>Ut,isReference:()=>be,isRootCstNode:()=>is,isTokenTypeArray:()=>_l,isTokenTypeDictionary:()=>nf,loadGrammarFromJson:()=>ar,parseJSDoc:()=>lf,prepareLangiumParser:()=>Ty,setInterruptionPeriod:()=>Sy,startCancelableOperation:()=>kl,stream:()=>H,toDiagnosticData:()=>Ly,toDiagnosticSeverity:()=>Nl});var Ga={};$r(Ga,{DefaultNameRegexp:()=>Fa,RangeComparison:()=>Et,compareRange:()=>Pf,findCommentNode:()=>Ql,findDeclarationNodeAtOffset:()=>xx,findLeafNodeAtOffset:()=>eu,findLeafNodeBeforeOffset:()=>Mf,flattenCst:()=>yx,getInteriorNodes:()=>Ax,getNextNode:()=>Tx,getPreviousNode:()=>Ff,getStartlineNode:()=>Rx,inRange:()=>Zl,isChildNode:()=>Jl,isCommentNode:()=>Yl,streamCst:()=>Pr,toDocumentSegment:()=>Mr,tokenToRange:()=>dn});function fe(t){return typeof t=="object"&&t!==null&&typeof t.$type=="string"}s(fe,"isAstNode");function be(t){return typeof t=="object"&&t!==null&&typeof t.$refText=="string"}s(be,"isReference");function Xl(t){return typeof t=="object"&&t!==null&&typeof t.name=="string"&&typeof t.type=="string"&&typeof t.path=="string"}s(Xl,"isAstNodeDescription");function Lr(t){return typeof t=="object"&&t!==null&&fe(t.container)&&be(t.reference)&&typeof t.message=="string"}s(Lr,"isLinkingError");var Or=class{static{s(this,"AbstractAstReflection")}constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,r){return fe(e)&&this.isSubtype(e.$type,r)}isSubtype(e,r){if(e===r)return!0;let n=this.subtypes[e];n||(n=this.subtypes[e]={});let i=n[r];if(i!==void 0)return i;{let a=this.computeIsSubtype(e,r);return n[r]=a,a}}getAllSubTypes(e){let r=this.allSubtypes[e];if(r)return r;{let n=this.getAllTypes(),i=[];for(let a of n)this.isSubtype(a,e)&&i.push(a);return this.allSubtypes[e]=i,i}}};function ht(t){return typeof t=="object"&&t!==null&&Array.isArray(t.content)}s(ht,"isCompositeCstNode");function ur(t){return typeof t=="object"&&t!==null&&typeof t.tokenType=="object"}s(ur,"isLeafCstNode");function is(t){return ht(t)&&typeof t.fullText=="string"}s(is,"isRootCstNode");var it=class t{static{s(this,"StreamImpl")}constructor(e,r){this.startFn=e,this.nextFn=r}iterator(){let e={state:this.startFn(),next:s(()=>this.nextFn(e.state),"next"),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){return!!this.iterator().next().done}count(){let e=this.iterator(),r=0,n=e.next();for(;!n.done;)r++,n=e.next();return r}toArray(){let e=[],r=this.iterator(),n;do n=r.next(),n.value!==void 0&&e.push(n.value);while(!n.done);return e}toSet(){return new Set(this)}toMap(e,r){let n=this.map(i=>[e?e(i):i,r?r(i):i]);return new Map(n)}toString(){return this.join()}concat(e){return new t(()=>({first:this.startFn(),firstDone:!1,iterator:e[Symbol.iterator]()}),r=>{let n;if(!r.firstDone){do if(n=this.nextFn(r.first),!n.done)return n;while(!n.done);r.firstDone=!0}do if(n=r.iterator.next(),!n.done)return n;while(!n.done);return Me})}join(e=","){let r=this.iterator(),n="",i,a=!1;do i=r.next(),i.done||(a&&(n+=e),n+=gx(i.value)),a=!0;while(!i.done);return n}indexOf(e,r=0){let n=this.iterator(),i=0,a=n.next();for(;!a.done;){if(i>=r&&a.value===e)return i;a=n.next(),i++}return-1}every(e){let r=this.iterator(),n=r.next();for(;!n.done;){if(!e(n.value))return!1;n=r.next()}return!0}some(e){let r=this.iterator(),n=r.next();for(;!n.done;){if(e(n.value))return!0;n=r.next()}return!1}forEach(e){let r=this.iterator(),n=0,i=r.next();for(;!i.done;)e(i.value,n),i=r.next(),n++}map(e){return new t(this.startFn,r=>{let{done:n,value:i}=this.nextFn(r);return n?Me:{done:!1,value:e(i)}})}filter(e){return new t(this.startFn,r=>{let n;do if(n=this.nextFn(r),!n.done&&e(n.value))return n;while(!n.done);return Me})}nonNullable(){return this.filter(e=>e!=null)}reduce(e,r){let n=this.iterator(),i=r,a=n.next();for(;!a.done;)i===void 0?i=a.value:i=e(i,a.value),a=n.next();return i}reduceRight(e,r){return this.recursiveReduce(this.iterator(),e,r)}recursiveReduce(e,r,n){let i=e.next();if(i.done)return n;let a=this.recursiveReduce(e,r,n);return a===void 0?i.value:r(a,i.value)}find(e){let r=this.iterator(),n=r.next();for(;!n.done;){if(e(n.value))return n.value;n=r.next()}}findIndex(e){let r=this.iterator(),n=0,i=r.next();for(;!i.done;){if(e(i.value))return n;i=r.next(),n++}return-1}includes(e){let r=this.iterator(),n=r.next();for(;!n.done;){if(n.value===e)return!0;n=r.next()}return!1}flatMap(e){return new t(()=>({this:this.startFn()}),r=>{do{if(r.iterator){let a=r.iterator.next();if(a.done)r.iterator=void 0;else return a}let{done:n,value:i}=this.nextFn(r.this);if(!n){let a=e(i);if(Da(a))r.iterator=a[Symbol.iterator]();else return{done:!1,value:a}}}while(r.iterator);return Me})}flat(e){if(e===void 0&&(e=1),e<=0)return this;let r=e>1?this.flat(e-1):this;return new t(()=>({this:r.startFn()}),n=>{do{if(n.iterator){let o=n.iterator.next();if(o.done)n.iterator=void 0;else return o}let{done:i,value:a}=r.nextFn(n.this);if(!i)if(Da(a))n.iterator=a[Symbol.iterator]();else return{done:!1,value:a}}while(n.iterator);return Me})}head(){let r=this.iterator().next();if(!r.done)return r.value}tail(e=1){return new t(()=>{let r=this.startFn();for(let n=0;n<e;n++)if(this.nextFn(r).done)return r;return r},this.nextFn)}limit(e){return new t(()=>({size:0,state:this.startFn()}),r=>(r.size++,r.size>e?Me:this.nextFn(r.state)))}distinct(e){return new t(()=>({set:new Set,internalState:this.startFn()}),r=>{let n;do if(n=this.nextFn(r.internalState),!n.done){let i=e?e(n.value):n.value;if(!r.set.has(i))return r.set.add(i),n}while(!n.done);return Me})}exclude(e,r){let n=new Set;for(let i of e){let a=r?r(i):i;n.add(a)}return this.filter(i=>{let a=r?r(i):i;return!n.has(a)})}};function gx(t){return typeof t=="string"?t:typeof t>"u"?"undefined":typeof t.toString=="function"?t.toString():Object.prototype.toString.call(t)}s(gx,"toString");function Da(t){return!!t&&typeof t[Symbol.iterator]=="function"}s(Da,"isIterable");var ss=new it(()=>{},()=>Me),Me=Object.freeze({done:!0,value:void 0});function H(...t){if(t.length===1){let e=t[0];if(e instanceof it)return e;if(Da(e))return new it(()=>e[Symbol.iterator](),r=>r.next());if(typeof e.length=="number")return new it(()=>({index:0}),r=>r.index<e.length?{done:!1,value:e[r.index++]}:Me)}return t.length>1?new it(()=>({collIndex:0,arrIndex:0}),e=>{do{if(e.iterator){let r=e.iterator.next();if(!r.done)return r;e.iterator=void 0}if(e.array){if(e.arrIndex<e.array.length)return{done:!1,value:e.array[e.arrIndex++]};e.array=void 0,e.arrIndex=0}if(e.collIndex<t.length){let r=t[e.collIndex++];Da(r)?e.iterator=r[Symbol.iterator]():r&&typeof r.length=="number"&&(e.array=r)}}while(e.iterator||e.array||e.collIndex<t.length);return Me}):ss}s(H,"stream");var At=class extends it{static{s(this,"TreeStreamImpl")}constructor(e,r,n){super(()=>({iterators:n?.includeRoot?[[e][Symbol.iterator]()]:[r(e)[Symbol.iterator]()],pruned:!1}),i=>{for(i.pruned&&(i.iterators.pop(),i.pruned=!1);i.iterators.length>0;){let o=i.iterators[i.iterators.length-1].next();if(o.done)i.iterators.pop();else return i.iterators.push(r(o.value)[Symbol.iterator]()),o}return Me})}iterator(){let e={state:this.startFn(),next:s(()=>this.nextFn(e.state),"next"),prune:s(()=>{e.state.pruned=!0},"prune"),[Symbol.iterator]:()=>e};return e}},fn;(function(t){function e(a){return a.reduce((o,l)=>o+l,0)}s(e,"sum"),t.sum=e;function r(a){return a.reduce((o,l)=>o*l,0)}s(r,"product"),t.product=r;function n(a){return a.reduce((o,l)=>Math.min(o,l))}s(n,"min"),t.min=n;function i(a){return a.reduce((o,l)=>Math.max(o,l))}s(i,"max"),t.max=i})(fn||(fn={}));function Pr(t){return new At(t,e=>ht(e)?e.content:[],{includeRoot:!0})}s(Pr,"streamCst");function yx(t){return Pr(t).filter(ur)}s(yx,"flattenCst");function Jl(t,e){for(;t.container;)if(t=t.container,t===e)return!0;return!1}s(Jl,"isChildNode");function dn(t){return{start:{character:t.startColumn-1,line:t.startLine-1},end:{character:t.endColumn,line:t.endLine-1}}}s(dn,"tokenToRange");function Mr(t){if(!t)return;let{offset:e,end:r,range:n}=t;return{range:n,offset:e,end:r,length:r-e}}s(Mr,"toDocumentSegment");var Et;(function(t){t[t.Before=0]="Before",t[t.After=1]="After",t[t.OverlapFront=2]="OverlapFront",t[t.OverlapBack=3]="OverlapBack",t[t.Inside=4]="Inside",t[t.Outside=5]="Outside"})(Et||(Et={}));function Pf(t,e){if(t.end.line<e.start.line||t.end.line===e.start.line&&t.end.character<=e.start.character)return Et.Before;if(t.start.line>e.end.line||t.start.line===e.end.line&&t.start.character>=e.end.character)return Et.After;let r=t.start.line>e.start.line||t.start.line===e.start.line&&t.start.character>=e.start.character,n=t.end.line<e.end.line||t.end.line===e.end.line&&t.end.character<=e.end.character;return r&&n?Et.Inside:r?Et.OverlapBack:n?Et.OverlapFront:Et.Outside}s(Pf,"compareRange");function Zl(t,e){return Pf(t,e)>Et.After}s(Zl,"inRange");var Fa=/^[\w\p{L}]$/u;function xx(t,e,r=Fa){if(t){if(e>0){let n=e-t.offset,i=t.text.charAt(n);r.test(i)||e--}return eu(t,e)}}s(xx,"findDeclarationNodeAtOffset");function Ql(t,e){if(t){let r=Ff(t,!0);if(r&&Yl(r,e))return r;if(is(t)){let n=t.content.findIndex(i=>!i.hidden);for(let i=n-1;i>=0;i--){let a=t.content[i];if(Yl(a,e))return a}}}}s(Ql,"findCommentNode");function Yl(t,e){return ur(t)&&e.includes(t.tokenType.name)}s(Yl,"isCommentNode");function eu(t,e){if(ur(t))return t;if(ht(t)){let r=Df(t,e,!1);if(r)return eu(r,e)}}s(eu,"findLeafNodeAtOffset");function Mf(t,e){if(ur(t))return t;if(ht(t)){let r=Df(t,e,!0);if(r)return Mf(r,e)}}s(Mf,"findLeafNodeBeforeOffset");function Df(t,e,r){let n=0,i=t.content.length-1,a;for(;n<=i;){let o=Math.floor((n+i)/2),l=t.content[o];if(l.offset<=e&&l.end>e)return l;l.end<=e?(a=r?l:void 0,n=o+1):i=o-1}return a}s(Df,"binarySearch");function Ff(t,e=!0){for(;t.container;){let r=t.container,n=r.content.indexOf(t);for(;n>0;){n--;let i=r.content[n];if(e||!i.hidden)return i}t=r}}s(Ff,"getPreviousNode");function Tx(t,e=!0){for(;t.container;){let r=t.container,n=r.content.indexOf(t),i=r.content.length-1;for(;n<i;){n++;let a=r.content[n];if(e||!a.hidden)return a}t=r}}s(Tx,"getNextNode");function Rx(t){if(t.range.start.character===0)return t;let e=t.range.start.line,r=t,n;for(;t.container;){let i=t.container,a=n??i.content.indexOf(t);if(a===0?(t=i,n=void 0):(n=a-1,t=i.content[n]),t.range.start.line!==e)break;r=t}return r}s(Rx,"getStartlineNode");function Ax(t,e){let r=Ex(t,e);return r?r.parent.content.slice(r.a+1,r.b):[]}s(Ax,"getInteriorNodes");function Ex(t,e){let r=Lf(t),n=Lf(e),i;for(let a=0;a<r.length&&a<n.length;a++){let o=r[a],l=n[a];if(o.parent===l.parent)i={parent:o.parent,a:o.index,b:l.index};else break}return i}s(Ex,"getCommonParent");function Lf(t){let e=[];for(;t.container;){let r=t.container,n=r.content.indexOf(t);e.push({parent:r,index:n}),t=r}return e.reverse()}s(Lf,"getParentChain");var Qa={};$r(Qa,{findAssignment:()=>Mu,findNameAssignment:()=>Za,findNodeForKeyword:()=>Lu,findNodeForProperty:()=>As,findNodesForKeyword:()=>Wx,findNodesForKeywordInternal:()=>Pu,findNodesForProperty:()=>$u,getActionAtElement:()=>qf,getActionType:()=>Yf,getAllReachableRules:()=>Rs,getCrossReferenceTerminal:()=>_u,getEntryRule:()=>Kf,getExplicitRuleType:()=>ti,getHiddenRules:()=>Hf,getRuleType:()=>Du,getRuleTypeName:()=>qx,getTypeName:()=>vs,isArrayCardinality:()=>Hx,isArrayOperator:()=>Vx,isCommentTerminal:()=>bu,isDataType:()=>zx,isDataTypeRule:()=>Es,isOptionalCardinality:()=>Kx,terminalRegex:()=>ri});var Dr=class extends Error{static{s(this,"ErrorWithLocation")}constructor(e,r){super(e?`${r} at ${e.range.start.line}:${e.range.start.character}`:r)}};function vt(t){throw new Error("Error! The input value was not handled.")}s(vt,"assertUnreachable");var ms={};$r(ms,{AbstractElement:()=>hn,AbstractRule:()=>pn,AbstractType:()=>mn,Action:()=>Ln,Alternatives:()=>Pn,ArrayLiteral:()=>gn,ArrayType:()=>yn,Assignment:()=>Mn,BooleanLiteral:()=>xn,CharacterRange:()=>Dn,Condition:()=>as,Conjunction:()=>Tn,CrossReference:()=>Fn,Disjunction:()=>Rn,EndOfFile:()=>Gn,Grammar:()=>An,GrammarImport:()=>ls,Group:()=>Un,InferredType:()=>En,Interface:()=>vn,Keyword:()=>Bn,LangiumGrammarAstReflection:()=>Jn,LangiumGrammarTerminals:()=>vx,NamedArgument:()=>us,NegatedToken:()=>jn,Negation:()=>In,NumberLiteral:()=>kn,Parameter:()=>Sn,ParameterReference:()=>Cn,ParserRule:()=>Nn,ReferenceType:()=>wn,RegexToken:()=>Wn,ReturnType:()=>cs,RuleCall:()=>Kn,SimpleType:()=>_n,StringLiteral:()=>bn,TerminalAlternatives:()=>Hn,TerminalGroup:()=>Vn,TerminalRule:()=>Fr,TerminalRuleCall:()=>zn,Type:()=>$n,TypeAttribute:()=>fs,TypeDefinition:()=>Ua,UnionType:()=>On,UnorderedGroup:()=>qn,UntilToken:()=>Xn,ValueLiteral:()=>os,Wildcard:()=>Yn,isAbstractElement:()=>ds,isAbstractRule:()=>Ix,isAbstractType:()=>kx,isAction:()=>Ht,isAlternatives:()=>Ka,isArrayLiteral:()=>_x,isArrayType:()=>tu,isAssignment:()=>gt,isBooleanLiteral:()=>ru,isCharacterRange:()=>cu,isCondition:()=>Sx,isConjunction:()=>nu,isCrossReference:()=>Gr,isDisjunction:()=>iu,isEndOfFile:()=>fu,isFeatureName:()=>Cx,isGrammar:()=>bx,isGrammarImport:()=>$x,isGroup:()=>cr,isInferredType:()=>Ba,isInterface:()=>ja,isKeyword:()=>ut,isNamedArgument:()=>Ox,isNegatedToken:()=>du,isNegation:()=>su,isNumberLiteral:()=>Lx,isParameter:()=>Px,isParameterReference:()=>au,isParserRule:()=>De,isPrimitiveType:()=>Gf,isReferenceType:()=>ou,isRegexToken:()=>pu,isReturnType:()=>lu,isRuleCall:()=>yt,isSimpleType:()=>Wa,isStringLiteral:()=>Mx,isTerminalAlternatives:()=>mu,isTerminalGroup:()=>hu,isTerminalRule:()=>st,isTerminalRuleCall:()=>Ha,isType:()=>ps,isTypeAttribute:()=>Dx,isTypeDefinition:()=>Nx,isUnionType:()=>uu,isUnorderedGroup:()=>Va,isUntilToken:()=>gu,isValueLiteral:()=>wx,isWildcard:()=>yu,reflection:()=>$});var vx={ID:/\^?[_a-zA-Z][\w_]*/,STRING:/"(\\.|[^"\\])*"|'(\\.|[^'\\])*'/,NUMBER:/NaN|-?((\d*\.\d+|\d+)([Ee][+-]?\d+)?|Infinity)/,RegexLiteral:/\/(?![*+?])(?:[^\r\n\[/\\]|\\.|\[(?:[^\r\n\]\\]|\\.)*\])+\/[a-z]*/,WS:/\s+/,ML_COMMENT:/\/\*[\s\S]*?\*\//,SL_COMMENT:/\/\/[^\n\r]*/},pn="AbstractRule";function Ix(t){return $.isInstance(t,pn)}s(Ix,"isAbstractRule");var mn="AbstractType";function kx(t){return $.isInstance(t,mn)}s(kx,"isAbstractType");var as="Condition";function Sx(t){return $.isInstance(t,as)}s(Sx,"isCondition");function Cx(t){return Gf(t)||t==="current"||t==="entry"||t==="extends"||t==="false"||t==="fragment"||t==="grammar"||t==="hidden"||t==="import"||t==="interface"||t==="returns"||t==="terminal"||t==="true"||t==="type"||t==="infer"||t==="infers"||t==="with"||typeof t=="string"&&/\^?[_a-zA-Z][\w_]*/.test(t)}s(Cx,"isFeatureName");function Gf(t){return t==="string"||t==="number"||t==="boolean"||t==="Date"||t==="bigint"}s(Gf,"isPrimitiveType");var Ua="TypeDefinition";function Nx(t){return $.isInstance(t,Ua)}s(Nx,"isTypeDefinition");var os="ValueLiteral";function wx(t){return $.isInstance(t,os)}s(wx,"isValueLiteral");var hn="AbstractElement";function ds(t){return $.isInstance(t,hn)}s(ds,"isAbstractElement");var gn="ArrayLiteral";function _x(t){return $.isInstance(t,gn)}s(_x,"isArrayLiteral");var yn="ArrayType";function tu(t){return $.isInstance(t,yn)}s(tu,"isArrayType");var xn="BooleanLiteral";function ru(t){return $.isInstance(t,xn)}s(ru,"isBooleanLiteral");var Tn="Conjunction";function nu(t){return $.isInstance(t,Tn)}s(nu,"isConjunction");var Rn="Disjunction";function iu(t){return $.isInstance(t,Rn)}s(iu,"isDisjunction");var An="Grammar";function bx(t){return $.isInstance(t,An)}s(bx,"isGrammar");var ls="GrammarImport";function $x(t){return $.isInstance(t,ls)}s($x,"isGrammarImport");var En="InferredType";function Ba(t){return $.isInstance(t,En)}s(Ba,"isInferredType");var vn="Interface";function ja(t){return $.isInstance(t,vn)}s(ja,"isInterface");var us="NamedArgument";function Ox(t){return $.isInstance(t,us)}s(Ox,"isNamedArgument");var In="Negation";function su(t){return $.isInstance(t,In)}s(su,"isNegation");var kn="NumberLiteral";function Lx(t){return $.isInstance(t,kn)}s(Lx,"isNumberLiteral");var Sn="Parameter";function Px(t){return $.isInstance(t,Sn)}s(Px,"isParameter");var Cn="ParameterReference";function au(t){return $.isInstance(t,Cn)}s(au,"isParameterReference");var Nn="ParserRule";function De(t){return $.isInstance(t,Nn)}s(De,"isParserRule");var wn="ReferenceType";function ou(t){return $.isInstance(t,wn)}s(ou,"isReferenceType");var cs="ReturnType";function lu(t){return $.isInstance(t,cs)}s(lu,"isReturnType");var _n="SimpleType";function Wa(t){return $.isInstance(t,_n)}s(Wa,"isSimpleType");var bn="StringLiteral";function Mx(t){return $.isInstance(t,bn)}s(Mx,"isStringLiteral");var Fr="TerminalRule";function st(t){return $.isInstance(t,Fr)}s(st,"isTerminalRule");var $n="Type";function ps(t){return $.isInstance(t,$n)}s(ps,"isType");var fs="TypeAttribute";function Dx(t){return $.isInstance(t,fs)}s(Dx,"isTypeAttribute");var On="UnionType";function uu(t){return $.isInstance(t,On)}s(uu,"isUnionType");var Ln="Action";function Ht(t){return $.isInstance(t,Ln)}s(Ht,"isAction");var Pn="Alternatives";function Ka(t){return $.isInstance(t,Pn)}s(Ka,"isAlternatives");var Mn="Assignment";function gt(t){return $.isInstance(t,Mn)}s(gt,"isAssignment");var Dn="CharacterRange";function cu(t){return $.isInstance(t,Dn)}s(cu,"isCharacterRange");var Fn="CrossReference";function Gr(t){return $.isInstance(t,Fn)}s(Gr,"isCrossReference");var Gn="EndOfFile";function fu(t){return $.isInstance(t,Gn)}s(fu,"isEndOfFile");var Un="Group";function cr(t){return $.isInstance(t,Un)}s(cr,"isGroup");var Bn="Keyword";function ut(t){return $.isInstance(t,Bn)}s(ut,"isKeyword");var jn="NegatedToken";function du(t){return $.isInstance(t,jn)}s(du,"isNegatedToken");var Wn="RegexToken";function pu(t){return $.isInstance(t,Wn)}s(pu,"isRegexToken");var Kn="RuleCall";function yt(t){return $.isInstance(t,Kn)}s(yt,"isRuleCall");var Hn="TerminalAlternatives";function mu(t){return $.isInstance(t,Hn)}s(mu,"isTerminalAlternatives");var Vn="TerminalGroup";function hu(t){return $.isInstance(t,Vn)}s(hu,"isTerminalGroup");var zn="TerminalRuleCall";function Ha(t){return $.isInstance(t,zn)}s(Ha,"isTerminalRuleCall");var qn="UnorderedGroup";function Va(t){return $.isInstance(t,qn)}s(Va,"isUnorderedGroup");var Xn="UntilToken";function gu(t){return $.isInstance(t,Xn)}s(gu,"isUntilToken");var Yn="Wildcard";function yu(t){return $.isInstance(t,Yn)}s(yu,"isWildcard");var Jn=class extends Or{static{s(this,"LangiumGrammarAstReflection")}getAllTypes(){return[hn,pn,mn,Ln,Pn,gn,yn,Mn,xn,Dn,as,Tn,Fn,Rn,Gn,An,ls,Un,En,vn,Bn,us,jn,In,kn,Sn,Cn,Nn,wn,Wn,cs,Kn,_n,bn,Hn,Vn,Fr,zn,$n,fs,Ua,On,qn,Xn,os,Yn]}computeIsSubtype(e,r){switch(e){case Ln:case Pn:case Mn:case Dn:case Fn:case Gn:case Un:case Bn:case jn:case Wn:case Kn:case Hn:case Vn:case zn:case qn:case Xn:case Yn:return this.isSubtype(hn,r);case gn:case kn:case bn:return this.isSubtype(os,r);case yn:case wn:case _n:case On:return this.isSubtype(Ua,r);case xn:return this.isSubtype(as,r)||this.isSubtype(os,r);case Tn:case Rn:case In:case Cn:return this.isSubtype(as,r);case En:case vn:case $n:return this.isSubtype(mn,r);case Nn:return this.isSubtype(pn,r)||this.isSubtype(mn,r);case Fr:return this.isSubtype(pn,r);default:return!1}}getReferenceType(e){let r=`${e.container.$type}:${e.property}`;switch(r){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return mn;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return pn;case"Grammar:usedGrammars":return An;case"NamedArgument:parameter":case"ParameterReference:parameter":return Sn;case"TerminalRuleCall:rule":return Fr;default:throw new Error(`${r} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case hn:return{name:hn,properties:[{name:"cardinality"},{name:"lookahead"}]};case gn:return{name:gn,properties:[{name:"elements",defaultValue:[]}]};case yn:return{name:yn,properties:[{name:"elementType"}]};case xn:return{name:xn,properties:[{name:"true",defaultValue:!1}]};case Tn:return{name:Tn,properties:[{name:"left"},{name:"right"}]};case Rn:return{name:Rn,properties:[{name:"left"},{name:"right"}]};case An:return{name:An,properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case ls:return{name:ls,properties:[{name:"path"}]};case En:return{name:En,properties:[{name:"name"}]};case vn:return{name:vn,properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case us:return{name:us,properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case In:return{name:In,properties:[{name:"value"}]};case kn:return{name:kn,properties:[{name:"value"}]};case Sn:return{name:Sn,properties:[{name:"name"}]};case Cn:return{name:Cn,properties:[{name:"parameter"}]};case Nn:return{name:Nn,properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case wn:return{name:wn,properties:[{name:"referenceType"}]};case cs:return{name:cs,properties:[{name:"name"}]};case _n:return{name:_n,properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case bn:return{name:bn,properties:[{name:"value"}]};case Fr:return{name:Fr,properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case $n:return{name:$n,properties:[{name:"name"},{name:"type"}]};case fs:return{name:fs,properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case On:return{name:On,properties:[{name:"types",defaultValue:[]}]};case Ln:return{name:Ln,properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case Pn:return{name:Pn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Mn:return{name:Mn,properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case Dn:return{name:Dn,properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case Fn:return{name:Fn,properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case Gn:return{name:Gn,properties:[{name:"cardinality"},{name:"lookahead"}]};case Un:return{name:Un,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case Bn:return{name:Bn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case jn:return{name:jn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case Wn:return{name:Wn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case Kn:return{name:Kn,properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case Hn:return{name:Hn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Vn:return{name:Vn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case zn:return{name:zn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case qn:return{name:qn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Xn:return{name:Xn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case Yn:return{name:Yn,properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}},$=new Jn;var qa={};$r(qa,{assignMandatoryProperties:()=>Ru,copyAstNode:()=>Tu,findLocalReferences:()=>Gx,findRootNode:()=>hs,getContainerOfType:()=>Ur,getDocument:()=>Fe,hasContainerOfType:()=>Fx,linkContentToContainer:()=>za,streamAllContents:()=>It,streamAst:()=>ct,streamContents:()=>gs,streamReferences:()=>Zn});function za(t){for(let[e,r]of Object.entries(t))e.startsWith("$")||(Array.isArray(r)?r.forEach((n,i)=>{fe(n)&&(n.$container=t,n.$containerProperty=e,n.$containerIndex=i)}):fe(r)&&(r.$container=t,r.$containerProperty=e))}s(za,"linkContentToContainer");function Ur(t,e){let r=t;for(;r;){if(e(r))return r;r=r.$container}}s(Ur,"getContainerOfType");function Fx(t,e){let r=t;for(;r;){if(e(r))return!0;r=r.$container}return!1}s(Fx,"hasContainerOfType");function Fe(t){let r=hs(t).$document;if(!r)throw new Error("AST node has no document.");return r}s(Fe,"getDocument");function hs(t){for(;t.$container;)t=t.$container;return t}s(hs,"findRootNode");function gs(t,e){if(!t)throw new Error("Node must be an AstNode.");let r=e?.range;return new it(()=>({keys:Object.keys(t),keyIndex:0,arrayIndex:0}),n=>{for(;n.keyIndex<n.keys.length;){let i=n.keys[n.keyIndex];if(!i.startsWith("$")){let a=t[i];if(fe(a)){if(n.keyIndex++,xu(a,r))return{done:!1,value:a}}else if(Array.isArray(a)){for(;n.arrayIndex<a.length;){let o=n.arrayIndex++,l=a[o];if(fe(l)&&xu(l,r))return{done:!1,value:l}}n.arrayIndex=0}}n.keyIndex++}return Me})}s(gs,"streamContents");function It(t,e){if(!t)throw new Error("Root node must be an AstNode.");return new At(t,r=>gs(r,e))}s(It,"streamAllContents");function ct(t,e){if(t){if(e?.range&&!xu(t,e.range))return new At(t,()=>[])}else throw new Error("Root node must be an AstNode.");return new At(t,r=>gs(r,e),{includeRoot:!0})}s(ct,"streamAst");function xu(t,e){var r;if(!e)return!0;let n=(r=t.$cstNode)===null||r===void 0?void 0:r.range;return n?Zl(n,e):!1}s(xu,"isAstNodeInRange");function Zn(t){return new it(()=>({keys:Object.keys(t),keyIndex:0,arrayIndex:0}),e=>{for(;e.keyIndex<e.keys.length;){let r=e.keys[e.keyIndex];if(!r.startsWith("$")){let n=t[r];if(be(n))return e.keyIndex++,{done:!1,value:{reference:n,container:t,property:r}};if(Array.isArray(n)){for(;e.arrayIndex<n.length;){let i=e.arrayIndex++,a=n[i];if(be(a))return{done:!1,value:{reference:a,container:t,property:r,index:i}}}e.arrayIndex=0}}e.keyIndex++}return Me})}s(Zn,"streamReferences");function Gx(t,e=Fe(t).parseResult.value){let r=[];return ct(e).forEach(n=>{Zn(n).forEach(i=>{i.reference.ref===t&&r.push(i.reference)})}),H(r)}s(Gx,"findLocalReferences");function Ru(t,e){let r=t.getTypeMetaData(e.$type),n=e;for(let i of r.properties)i.defaultValue!==void 0&&n[i.name]===void 0&&(n[i.name]=Uf(i.defaultValue))}s(Ru,"assignMandatoryProperties");function Uf(t){return Array.isArray(t)?[...t.map(Uf)]:t}s(Uf,"copyDefaultValue");function Tu(t,e){let r={$type:t.$type};for(let[n,i]of Object.entries(t))if(!n.startsWith("$"))if(fe(i))r[n]=Tu(i,e);else if(be(i))r[n]=e(r,n,i.$refNode,i.$refText);else if(Array.isArray(i)){let a=[];for(let o of i)fe(o)?a.push(Tu(o,e)):be(o)?a.push(e(r,n,o.$refNode,o.$refText)):a.push(o);r[n]=a}else r[n]=i;return za(r),r}s(Tu,"copyAstNode");var Ja={};$r(Ja,{NEWLINE_REGEXP:()=>Iu,escapeRegExp:()=>Kr,getCaseInsensitivePattern:()=>Su,getTerminalParts:()=>jx,isMultilineComment:()=>ku,isWhitespace:()=>ei,partialMatches:()=>Cu,partialRegExp:()=>Wf,whitespaceCharacters:()=>jf});function b(t){return t.charCodeAt(0)}s(b,"cc");function Xa(t,e){Array.isArray(t)?t.forEach(function(r){e.push(r)}):e.push(t)}s(Xa,"insertToSet");function Qn(t,e){if(t[e]===!0)throw"duplicate flag "+e;let r=t[e];t[e]=!0}s(Qn,"addFlag");function Br(t){if(t===void 0)throw Error("Internal Error - Should never get here!");return!0}s(Br,"ASSERT_EXISTS");function ys(){throw Error("Internal Error - Should never get here!")}s(ys,"ASSERT_NEVER_REACH_HERE");function Au(t){return t.type==="Character"}s(Au,"isCharacter");var xs=[];for(let t=b("0");t<=b("9");t++)xs.push(t);var Ts=[b("_")].concat(xs);for(let t=b("a");t<=b("z");t++)Ts.push(t);for(let t=b("A");t<=b("Z");t++)Ts.push(t);var Eu=[b(" "),b("\f"),b(`
`),b("\r"),b("	"),b("\v"),b("	"),b("\xA0"),b("\u1680"),b("\u2000"),b("\u2001"),b("\u2002"),b("\u2003"),b("\u2004"),b("\u2005"),b("\u2006"),b("\u2007"),b("\u2008"),b("\u2009"),b("\u200A"),b("\u2028"),b("\u2029"),b("\u202F"),b("\u205F"),b("\u3000"),b("\uFEFF")];var Ux=/[0-9a-fA-F]/,Ya=/[0-9]/,Bx=/[1-9]/,jr=class{static{s(this,"RegExpParser")}constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");let r=this.disjunction();this.consumeChar("/");let n={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":Qn(n,"global");break;case"i":Qn(n,"ignoreCase");break;case"m":Qn(n,"multiLine");break;case"u":Qn(n,"unicode");break;case"y":Qn(n,"sticky");break}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:n,value:r,loc:this.loc(0)}}disjunction(){let e=[],r=this.idx;for(e.push(this.alternative());this.peekChar()==="|";)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(r)}}alternative(){let e=[],r=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(r)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){let e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":this.consumeChar("?");let r;switch(this.popChar()){case"=":r="Lookahead";break;case"!":r="NegativeLookahead";break}Br(r);let n=this.disjunction();return this.consumeChar(")"),{type:r,value:n,loc:this.loc(e)}}return ys()}quantifier(e=!1){let r,n=this.idx;switch(this.popChar()){case"*":r={atLeast:0,atMost:1/0};break;case"+":r={atLeast:1,atMost:1/0};break;case"?":r={atLeast:0,atMost:1};break;case"{":let i=this.integerIncludingZero();switch(this.popChar()){case"}":r={atLeast:i,atMost:i};break;case",":let a;this.isDigit()?(a=this.integerIncludingZero(),r={atLeast:i,atMost:a}):r={atLeast:i,atMost:1/0},this.consumeChar("}");break}if(e===!0&&r===void 0)return;Br(r);break}if(!(e===!0&&r===void 0)&&Br(r))return this.peekChar(0)==="?"?(this.consumeChar("?"),r.greedy=!1):r.greedy=!0,r.type="Quantifier",r.loc=this.loc(n),r}atom(){let e,r=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group();break}return e===void 0&&this.isPatternCharacter()&&(e=this.patternCharacter()),Br(e)?(e.loc=this.loc(r),this.isQuantifier()&&(e.quantifier=this.quantifier()),e):ys()}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[b(`
`),b("\r"),b("\u2028"),b("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let e,r=!1;switch(this.popChar()){case"d":e=xs;break;case"D":e=xs,r=!0;break;case"s":e=Eu;break;case"S":e=Eu,r=!0;break;case"w":e=Ts;break;case"W":e=Ts,r=!0;break}return Br(e)?{type:"Set",value:e,complement:r}:ys()}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=b("\f");break;case"n":e=b(`
`);break;case"r":e=b("\r");break;case"t":e=b("	");break;case"v":e=b("\v");break}return Br(e)?{type:"Character",value:e}:ys()}controlLetterEscapeAtom(){this.consumeChar("c");let e=this.popChar();if(/[a-zA-Z]/.test(e)===!1)throw Error("Invalid ");return{type:"Character",value:e.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:b("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){let e=this.popChar();return{type:"Character",value:b(e)}}classPatternCharacterAtom(){switch(this.peekChar()){case`
`:case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:let e=this.popChar();return{type:"Character",value:b(e)}}}characterClass(){let e=[],r=!1;for(this.consumeChar("["),this.peekChar(0)==="^"&&(this.consumeChar("^"),r=!0);this.isClassAtom();){let n=this.classAtom(),i=n.type==="Character";if(Au(n)&&this.isRangeDash()){this.consumeChar("-");let a=this.classAtom(),o=a.type==="Character";if(Au(a)){if(a.value<n.value)throw Error("Range out of order in character class");e.push({from:n.value,to:a.value})}else Xa(n.value,e),e.push(b("-")),Xa(a.value,e)}else Xa(n.value,e)}return this.consumeChar("]"),{type:"Set",complement:r,value:e}}classAtom(){switch(this.peekChar()){case"]":case`
`:case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:b("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;switch(this.consumeChar("("),this.peekChar(0)){case"?":this.consumeChar("?"),this.consumeChar(":"),e=!1;break;default:this.groupIdx++;break}let r=this.disjunction();this.consumeChar(")");let n={type:"Group",capturing:e,value:r};return e&&(n.idx=this.groupIdx),n}positiveInteger(){let e=this.popChar();if(Bx.test(e)===!1)throw Error("Expecting a positive integer");for(;Ya.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(Ya.test(e)===!1)throw Error("Expecting an integer");for(;Ya.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){let e=this.popChar();switch(e){case`
`:case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:b(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return Ya.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return!1}}isQuantifier(){let e=this.saveState();try{return this.quantifier(!0)!==void 0}catch{return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let r="";for(let i=0;i<e;i++){let a=this.popChar();if(Ux.test(a)===!1)throw Error("Expecting a HexDecimal digits");r+=a}return{type:"Character",value:parseInt(r,16)}}peekChar(e=0){return this.input[this.idx+e]}popChar(){let e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(e!==void 0&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}};var kt=class{static{s(this,"BaseRegExpVisitor")}visitChildren(e){for(let r in e){let n=e[r];e.hasOwnProperty(r)&&(n.type!==void 0?this.visit(n):Array.isArray(n)&&n.forEach(i=>{this.visit(i)},this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e);break}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}};var Iu=/\r?\n/gm,Bf=new jr,vu=class extends kt{static{s(this,"TerminalRegExpVisitor")}constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=!1,this.regex=e,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(e){e.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(e){let r=String.fromCharCode(e.value);if(!this.multiline&&r===`
`&&(this.multiline=!0),e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{let n=Kr(r);this.endRegexpStack.push(n),this.isStarting&&(this.startRegexp+=n)}}visitSet(e){if(!this.multiline){let r=this.regex.substring(e.loc.begin,e.loc.end),n=new RegExp(r);this.multiline=!!`
`.match(n)}if(e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{let r=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(r),this.isStarting&&(this.startRegexp+=r)}}visitChildren(e){e.type==="Group"&&e.quantifier||super.visitChildren(e)}},Wr=new vu;function jx(t){try{typeof t!="string"&&(t=t.source),t=`/${t}/`;let e=Bf.pattern(t),r=[];for(let n of e.value.value)Wr.reset(t),Wr.visit(n),r.push({start:Wr.startRegexp,end:Wr.endRegex});return r}catch{return[]}}s(jx,"getTerminalParts");function ku(t){try{return typeof t=="string"&&(t=new RegExp(t)),t=t.toString(),Wr.reset(t),Wr.visit(Bf.pattern(t)),Wr.multiline}catch{return!1}}s(ku,"isMultilineComment");var jf=`\f
\r	\v \xA0\u1680\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF`.split("");function ei(t){let e=typeof t=="string"?new RegExp(t):t;return jf.some(r=>e.test(r))}s(ei,"isWhitespace");function Kr(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}s(Kr,"escapeRegExp");function Su(t){return Array.prototype.map.call(t,e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:Kr(e)).join("")}s(Su,"getCaseInsensitivePattern");function Cu(t,e){let r=Wf(t),n=e.match(r);return!!n&&n[0].length>0}s(Cu,"partialMatches");function Wf(t){typeof t=="string"&&(t=new RegExp(t));let e=t,r=t.source,n=0;function i(){let a="",o;function l(c){a+=r.substr(n,c),n+=c}s(l,"appendRaw");function u(c){a+="(?:"+r.substr(n,c)+"|$)",n+=c}for(s(u,"appendOptional");n<r.length;)switch(r[n]){case"\\":switch(r[n+1]){case"c":u(3);break;case"x":u(4);break;case"u":e.unicode?r[n+2]==="{"?u(r.indexOf("}",n)-n+1):u(6):u(2);break;case"p":case"P":e.unicode?u(r.indexOf("}",n)-n+1):u(2);break;case"k":u(r.indexOf(">",n)-n+1);break;default:u(2);break}break;case"[":o=/\[(?:\\.|.)*?\]/g,o.lastIndex=n,o=o.exec(r)||[],u(o[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":l(1);break;case"{":o=/\{\d+,?\d*\}/g,o.lastIndex=n,o=o.exec(r),o?l(o[0].length):u(1);break;case"(":if(r[n+1]==="?")switch(r[n+2]){case":":a+="(?:",n+=3,a+=i()+"|$)";break;case"=":a+="(?=",n+=3,a+=i()+")";break;case"!":o=n,n+=3,i(),a+=r.substr(o,n-o);break;case"<":switch(r[n+3]){case"=":case"!":o=n,n+=4,i(),a+=r.substr(o,n-o);break;default:l(r.indexOf(">",n)-n+1),a+=i()+"|$)";break}break}else l(1),a+=i()+"|$)";break;case")":return++n,a;default:u(1);break}return a}return s(i,"process"),new RegExp(i(),t.flags)}s(Wf,"partialRegExp");function Kf(t){return t.rules.find(e=>De(e)&&e.entry)}s(Kf,"getEntryRule");function Hf(t){return t.rules.filter(e=>st(e)&&e.hidden)}s(Hf,"getHiddenRules");function Rs(t,e){let r=new Set,n=Kf(t);if(!n)return new Set(t.rules);let i=[n].concat(Hf(t));for(let o of i)Vf(o,r,e);let a=new Set;for(let o of t.rules)(r.has(o.name)||st(o)&&o.hidden)&&a.add(o);return a}s(Rs,"getAllReachableRules");function Vf(t,e,r){e.add(t.name),It(t).forEach(n=>{if(yt(n)||r&&Ha(n)){let i=n.rule.ref;i&&!e.has(i.name)&&Vf(i,e,r)}})}s(Vf,"ruleDfs");function _u(t){if(t.terminal)return t.terminal;if(t.type.ref){let e=Za(t.type.ref);return e?.terminal}}s(_u,"getCrossReferenceTerminal");function bu(t){return t.hidden&&!ei(ri(t))}s(bu,"isCommentTerminal");function $u(t,e){return!t||!e?[]:Ou(t,e,t.astNode,!0)}s($u,"findNodesForProperty");function As(t,e,r){if(!t||!e)return;let n=Ou(t,e,t.astNode,!0);if(n.length!==0)return r!==void 0?r=Math.max(0,Math.min(r,n.length-1)):r=0,n[r]}s(As,"findNodeForProperty");function Ou(t,e,r,n){if(!n){let i=Ur(t.grammarSource,gt);if(i&&i.feature===e)return[t]}return ht(t)&&t.astNode===r?t.content.flatMap(i=>Ou(i,e,r,!1)):[]}s(Ou,"findNodesForPropertyInternal");function Wx(t,e){return t?Pu(t,e,t?.astNode):[]}s(Wx,"findNodesForKeyword");function Lu(t,e,r){if(!t)return;let n=Pu(t,e,t?.astNode);if(n.length!==0)return r!==void 0?r=Math.max(0,Math.min(r,n.length-1)):r=0,n[r]}s(Lu,"findNodeForKeyword");function Pu(t,e,r){if(t.astNode!==r)return[];if(ut(t.grammarSource)&&t.grammarSource.value===e)return[t];let n=Pr(t).iterator(),i,a=[];do if(i=n.next(),!i.done){let o=i.value;o.astNode===r?ut(o.grammarSource)&&o.grammarSource.value===e&&a.push(o):n.prune()}while(!i.done);return a}s(Pu,"findNodesForKeywordInternal");function Mu(t){var e;let r=t.astNode;for(;r===((e=t.container)===null||e===void 0?void 0:e.astNode);){let n=Ur(t.grammarSource,gt);if(n)return n;t=t.container}}s(Mu,"findAssignment");function Za(t){let e=t;return Ba(e)&&(Ht(e.$container)?e=e.$container.$container:De(e.$container)?e=e.$container:vt(e.$container)),zf(t,e,new Map)}s(Za,"findNameAssignment");function zf(t,e,r){var n;function i(a,o){let l;return Ur(a,gt)||(l=zf(o,o,r)),r.set(t,l),l}if(s(i,"go"),r.has(t))return r.get(t);r.set(t,void 0);for(let a of It(e)){if(gt(a)&&a.feature.toLowerCase()==="name")return r.set(t,a),a;if(yt(a)&&De(a.rule.ref))return i(a,a.rule.ref);if(Wa(a)&&(!((n=a.typeRef)===null||n===void 0)&&n.ref))return i(a,a.typeRef.ref)}}s(zf,"findNameAssignmentInternal");function qf(t){let e=t.$container;if(cr(e)){let r=e.elements,n=r.indexOf(t);for(let i=n-1;i>=0;i--){let a=r[i];if(Ht(a))return a;{let o=It(r[i]).find(Ht);if(o)return o}}}if(ds(e))return qf(e)}s(qf,"getActionAtElement");function Kx(t,e){return t==="?"||t==="*"||cr(e)&&!!e.guardCondition}s(Kx,"isOptionalCardinality");function Hx(t){return t==="*"||t==="+"}s(Hx,"isArrayCardinality");function Vx(t){return t==="+="}s(Vx,"isArrayOperator");function Es(t){return Xf(t,new Set)}s(Es,"isDataTypeRule");function Xf(t,e){if(e.has(t))return!0;e.add(t);for(let r of It(t))if(yt(r)){if(!r.rule.ref||De(r.rule.ref)&&!Xf(r.rule.ref,e))return!1}else{if(gt(r))return!1;if(Ht(r))return!1}return!!t.definition}s(Xf,"isDataTypeRuleInternal");function zx(t){return wu(t.type,new Set)}s(zx,"isDataType");function wu(t,e){if(e.has(t))return!0;if(e.add(t),tu(t))return!1;if(ou(t))return!1;if(uu(t))return t.types.every(r=>wu(r,e));if(Wa(t)){if(t.primitiveType!==void 0)return!0;if(t.stringType!==void 0)return!0;if(t.typeRef!==void 0){let r=t.typeRef.ref;return ps(r)?wu(r.type,e):!1}else return!1}else return!1}s(wu,"isDataTypeInternal");function ti(t){if(t.inferredType)return t.inferredType.name;if(t.dataType)return t.dataType;if(t.returnType){let e=t.returnType.ref;if(e){if(De(e))return e.name;if(ja(e)||ps(e))return e.name}}}s(ti,"getExplicitRuleType");function vs(t){var e;if(De(t))return Es(t)?t.name:(e=ti(t))!==null&&e!==void 0?e:t.name;if(ja(t)||ps(t)||lu(t))return t.name;if(Ht(t)){let r=Yf(t);if(r)return r}else if(Ba(t))return t.name;throw new Error("Cannot get name of Unknown Type")}s(vs,"getTypeName");function Yf(t){var e;if(t.inferredType)return t.inferredType.name;if(!((e=t.type)===null||e===void 0)&&e.ref)return vs(t.type.ref)}s(Yf,"getActionType");function qx(t){var e,r,n;return st(t)?(r=(e=t.type)===null||e===void 0?void 0:e.name)!==null&&r!==void 0?r:"string":Es(t)?t.name:(n=ti(t))!==null&&n!==void 0?n:t.name}s(qx,"getRuleTypeName");function Du(t){var e,r,n;return st(t)?(r=(e=t.type)===null||e===void 0?void 0:e.name)!==null&&r!==void 0?r:"string":(n=ti(t))!==null&&n!==void 0?n:t.name}s(Du,"getRuleType");function ri(t){let e={s:!1,i:!1,u:!1},r=ni(t.definition,e),n=Object.entries(e).filter(([,i])=>i).map(([i])=>i).join("");return new RegExp(r,n)}s(ri,"terminalRegex");var Fu=/[\s\S]/.source;function ni(t,e){if(mu(t))return Xx(t);if(hu(t))return Yx(t);if(cu(t))return Qx(t);if(Ha(t)){let r=t.rule.ref;if(!r)throw new Error("Missing rule reference.");return Vt(ni(r.definition),{cardinality:t.cardinality,lookahead:t.lookahead})}else{if(du(t))return Zx(t);if(gu(t))return Jx(t);if(pu(t)){let r=t.regex.lastIndexOf("/"),n=t.regex.substring(1,r),i=t.regex.substring(r+1);return e&&(e.i=i.includes("i"),e.s=i.includes("s"),e.u=i.includes("u")),Vt(n,{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1})}else{if(yu(t))return Vt(Fu,{cardinality:t.cardinality,lookahead:t.lookahead});throw new Error(`Invalid terminal element: ${t?.$type}`)}}}s(ni,"abstractElementToRegex");function Xx(t){return Vt(t.elements.map(e=>ni(e)).join("|"),{cardinality:t.cardinality,lookahead:t.lookahead})}s(Xx,"terminalAlternativesToRegex");function Yx(t){return Vt(t.elements.map(e=>ni(e)).join(""),{cardinality:t.cardinality,lookahead:t.lookahead})}s(Yx,"terminalGroupToRegex");function Jx(t){return Vt(`${Fu}*?${ni(t.terminal)}`,{cardinality:t.cardinality,lookahead:t.lookahead})}s(Jx,"untilTokenToRegex");function Zx(t){return Vt(`(?!${ni(t.terminal)})${Fu}*?`,{cardinality:t.cardinality,lookahead:t.lookahead})}s(Zx,"negateTokenToRegex");function Qx(t){return t.right?Vt(`[${Nu(t.left)}-${Nu(t.right)}]`,{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1}):Vt(Nu(t.left),{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1})}s(Qx,"characterRangeToRegex");function Nu(t){return Kr(t.value)}s(Nu,"keywordToRegex");function Vt(t,e){var r;return(e.wrap!==!1||e.lookahead)&&(t=`(${(r=e.lookahead)!==null&&r!==void 0?r:""}${t})`),e.cardinality?`${t}${e.cardinality}`:t}s(Vt,"withCardinality");function Gu(t){let e=[],r=t.Grammar;for(let n of r.rules)st(n)&&bu(n)&&ku(ri(n))&&e.push(n.name);return{multilineCommentRules:e,nameRegexp:Fa}}s(Gu,"createGrammarConfig");var eT=typeof global=="object"&&global&&global.Object===Object&&global,eo=eT;var tT=typeof self=="object"&&self&&self.Object===Object&&self,rT=eo||tT||Function("return this")(),Ae=rT;var nT=Ae.Symbol,Ce=nT;var Jf=Object.prototype,iT=Jf.hasOwnProperty,sT=Jf.toString,Is=Ce?Ce.toStringTag:void 0;function aT(t){var e=iT.call(t,Is),r=t[Is];try{t[Is]=void 0;var n=!0}catch{}var i=sT.call(t);return n&&(e?t[Is]=r:delete t[Is]),i}s(aT,"getRawTag");var Zf=aT;var oT=Object.prototype,lT=oT.toString;function uT(t){return lT.call(t)}s(uT,"objectToString");var Qf=uT;var cT="[object Null]",fT="[object Undefined]",ed=Ce?Ce.toStringTag:void 0;function dT(t){return t==null?t===void 0?fT:cT:ed&&ed in Object(t)?Zf(t):Qf(t)}s(dT,"baseGetTag");var Ke=dT;function pT(t){return t!=null&&typeof t=="object"}s(pT,"isObjectLike");var ge=pT;var mT="[object Symbol]";function hT(t){return typeof t=="symbol"||ge(t)&&Ke(t)==mT}s(hT,"isSymbol");var St=hT;function gT(t,e){for(var r=-1,n=t==null?0:t.length,i=Array(n);++r<n;)i[r]=e(t[r],r,t);return i}s(gT,"arrayMap");var Ct=gT;var yT=Array.isArray,N=yT;var xT=1/0,td=Ce?Ce.prototype:void 0,rd=td?td.toString:void 0;function nd(t){if(typeof t=="string")return t;if(N(t))return Ct(t,nd)+"";if(St(t))return rd?rd.call(t):"";var e=t+"";return e=="0"&&1/t==-xT?"-0":e}s(nd,"baseToString");var id=nd;var TT=/\s/;function RT(t){for(var e=t.length;e--&&TT.test(t.charAt(e)););return e}s(RT,"trimmedEndIndex");var sd=RT;var AT=/^\s+/;function ET(t){return t&&t.slice(0,sd(t)+1).replace(AT,"")}s(ET,"baseTrim");var ad=ET;function vT(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}s(vT,"isObject");var de=vT;var od=NaN,IT=/^[-+]0x[0-9a-f]+$/i,kT=/^0b[01]+$/i,ST=/^0o[0-7]+$/i,CT=parseInt;function NT(t){if(typeof t=="number")return t;if(St(t))return od;if(de(t)){var e=typeof t.valueOf=="function"?t.valueOf():t;t=de(e)?e+"":e}if(typeof t!="string")return t===0?t:+t;t=ad(t);var r=kT.test(t);return r||ST.test(t)?CT(t.slice(2),r?2:8):IT.test(t)?od:+t}s(NT,"toNumber");var ld=NT;var ud=1/0,wT=17976931348623157e292;function _T(t){if(!t)return t===0?t:0;if(t=ld(t),t===ud||t===-ud){var e=t<0?-1:1;return e*wT}return t===t?t:0}s(_T,"toFinite");var cd=_T;function bT(t){var e=cd(t),r=e%1;return e===e?r?e-r:e:0}s(bT,"toInteger");var Nt=bT;function $T(t){return t}s($T,"identity");var Ze=$T;var OT="[object AsyncFunction]",LT="[object Function]",PT="[object GeneratorFunction]",MT="[object Proxy]";function DT(t){if(!de(t))return!1;var e=Ke(t);return e==LT||e==PT||e==OT||e==MT}s(DT,"isFunction");var He=DT;var FT=Ae["__core-js_shared__"],to=FT;var fd=function(){var t=/[^.]+$/.exec(to&&to.keys&&to.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function GT(t){return!!fd&&fd in t}s(GT,"isMasked");var dd=GT;var UT=Function.prototype,BT=UT.toString;function jT(t){if(t!=null){try{return BT.call(t)}catch{}try{return t+""}catch{}}return""}s(jT,"toSource");var zt=jT;var WT=/[\\^$.*+?()[\]{}|]/g,KT=/^\[object .+?Constructor\]$/,HT=Function.prototype,VT=Object.prototype,zT=HT.toString,qT=VT.hasOwnProperty,XT=RegExp("^"+zT.call(qT).replace(WT,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function YT(t){if(!de(t)||dd(t))return!1;var e=He(t)?XT:KT;return e.test(zt(t))}s(YT,"baseIsNative");var pd=YT;function JT(t,e){return t?.[e]}s(JT,"getValue");var md=JT;function ZT(t,e){var r=md(t,e);return pd(r)?r:void 0}s(ZT,"getNative");var Qe=ZT;var QT=Qe(Ae,"WeakMap"),ro=QT;var hd=Object.create,eR=function(){function t(){}return s(t,"object"),function(e){if(!de(e))return{};if(hd)return hd(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}(),gd=eR;function tR(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}s(tR,"apply");var yd=tR;function rR(){}s(rR,"noop");var pe=rR;function nR(t,e){var r=-1,n=t.length;for(e||(e=Array(n));++r<n;)e[r]=t[r];return e}s(nR,"copyArray");var xd=nR;var iR=800,sR=16,aR=Date.now;function oR(t){var e=0,r=0;return function(){var n=aR(),i=sR-(n-r);if(r=n,i>0){if(++e>=iR)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}s(oR,"shortOut");var Td=oR;function lR(t){return function(){return t}}s(lR,"constant");var Rd=lR;var uR=function(){try{var t=Qe(Object,"defineProperty");return t({},"",{}),t}catch{}}(),ii=uR;var cR=ii?function(t,e){return ii(t,"toString",{configurable:!0,enumerable:!1,value:Rd(e),writable:!0})}:Ze,Ad=cR;var fR=Td(Ad),Ed=fR;function dR(t,e){for(var r=-1,n=t==null?0:t.length;++r<n&&e(t[r],r,t)!==!1;);return t}s(dR,"arrayEach");var no=dR;function pR(t,e,r,n){for(var i=t.length,a=r+(n?1:-1);n?a--:++a<i;)if(e(t[a],a,t))return a;return-1}s(pR,"baseFindIndex");var io=pR;function mR(t){return t!==t}s(mR,"baseIsNaN");var vd=mR;function hR(t,e,r){for(var n=r-1,i=t.length;++n<i;)if(t[n]===e)return n;return-1}s(hR,"strictIndexOf");var Id=hR;function gR(t,e,r){return e===e?Id(t,e,r):io(t,vd,r)}s(gR,"baseIndexOf");var si=gR;function yR(t,e){var r=t==null?0:t.length;return!!r&&si(t,e,0)>-1}s(yR,"arrayIncludes");var so=yR;var xR=9007199254740991,TR=/^(?:0|[1-9]\d*)$/;function RR(t,e){var r=typeof t;return e=e??xR,!!e&&(r=="number"||r!="symbol"&&TR.test(t))&&t>-1&&t%1==0&&t<e}s(RR,"isIndex");var fr=RR;function AR(t,e,r){e=="__proto__"&&ii?ii(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}s(AR,"baseAssignValue");var ai=AR;function ER(t,e){return t===e||t!==t&&e!==e}s(ER,"eq");var wt=ER;var vR=Object.prototype,IR=vR.hasOwnProperty;function kR(t,e,r){var n=t[e];(!(IR.call(t,e)&&wt(n,r))||r===void 0&&!(e in t))&&ai(t,e,r)}s(kR,"assignValue");var dr=kR;function SR(t,e,r,n){var i=!r;r||(r={});for(var a=-1,o=e.length;++a<o;){var l=e[a],u=n?n(r[l],t[l],l,r,t):void 0;u===void 0&&(u=t[l]),i?ai(r,l,u):dr(r,l,u)}return r}s(SR,"copyObject");var _t=SR;var kd=Math.max;function CR(t,e,r){return e=kd(e===void 0?t.length-1:e,0),function(){for(var n=arguments,i=-1,a=kd(n.length-e,0),o=Array(a);++i<a;)o[i]=n[e+i];i=-1;for(var l=Array(e+1);++i<e;)l[i]=n[i];return l[e]=r(o),yd(t,this,l)}}s(CR,"overRest");var Sd=CR;function NR(t,e){return Ed(Sd(t,e,Ze),t+"")}s(NR,"baseRest");var oi=NR;var wR=9007199254740991;function _R(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=wR}s(_R,"isLength");var li=_R;function bR(t){return t!=null&&li(t.length)&&!He(t)}s(bR,"isArrayLike");var Ee=bR;function $R(t,e,r){if(!de(r))return!1;var n=typeof e;return(n=="number"?Ee(r)&&fr(e,r.length):n=="string"&&e in r)?wt(r[e],t):!1}s($R,"isIterateeCall");var pr=$R;function OR(t){return oi(function(e,r){var n=-1,i=r.length,a=i>1?r[i-1]:void 0,o=i>2?r[2]:void 0;for(a=t.length>3&&typeof a=="function"?(i--,a):void 0,o&&pr(r[0],r[1],o)&&(a=i<3?void 0:a,i=1),e=Object(e);++n<i;){var l=r[n];l&&t(e,l,n,a)}return e})}s(OR,"createAssigner");var Cd=OR;var LR=Object.prototype;function PR(t){var e=t&&t.constructor,r=typeof e=="function"&&e.prototype||LR;return t===r}s(PR,"isPrototype");var bt=PR;function MR(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}s(MR,"baseTimes");var Nd=MR;var DR="[object Arguments]";function FR(t){return ge(t)&&Ke(t)==DR}s(FR,"baseIsArguments");var Uu=FR;var wd=Object.prototype,GR=wd.hasOwnProperty,UR=wd.propertyIsEnumerable,BR=Uu(function(){return arguments}())?Uu:function(t){return ge(t)&&GR.call(t,"callee")&&!UR.call(t,"callee")},mr=BR;function jR(){return!1}s(jR,"stubFalse");var _d=jR;var Od=typeof exports=="object"&&exports&&!exports.nodeType&&exports,bd=Od&&typeof module=="object"&&module&&!module.nodeType&&module,WR=bd&&bd.exports===Od,$d=WR?Ae.Buffer:void 0,KR=$d?$d.isBuffer:void 0,HR=KR||_d,qt=HR;var VR="[object Arguments]",zR="[object Array]",qR="[object Boolean]",XR="[object Date]",YR="[object Error]",JR="[object Function]",ZR="[object Map]",QR="[object Number]",eA="[object Object]",tA="[object RegExp]",rA="[object Set]",nA="[object String]",iA="[object WeakMap]",sA="[object ArrayBuffer]",aA="[object DataView]",oA="[object Float32Array]",lA="[object Float64Array]",uA="[object Int8Array]",cA="[object Int16Array]",fA="[object Int32Array]",dA="[object Uint8Array]",pA="[object Uint8ClampedArray]",mA="[object Uint16Array]",hA="[object Uint32Array]",se={};se[oA]=se[lA]=se[uA]=se[cA]=se[fA]=se[dA]=se[pA]=se[mA]=se[hA]=!0;se[VR]=se[zR]=se[sA]=se[qR]=se[aA]=se[XR]=se[YR]=se[JR]=se[ZR]=se[QR]=se[eA]=se[tA]=se[rA]=se[nA]=se[iA]=!1;function gA(t){return ge(t)&&li(t.length)&&!!se[Ke(t)]}s(gA,"baseIsTypedArray");var Ld=gA;function yA(t){return function(e){return t(e)}}s(yA,"baseUnary");var $t=yA;var Pd=typeof exports=="object"&&exports&&!exports.nodeType&&exports,ks=Pd&&typeof module=="object"&&module&&!module.nodeType&&module,xA=ks&&ks.exports===Pd,Bu=xA&&eo.process,TA=function(){try{var t=ks&&ks.require&&ks.require("util").types;return t||Bu&&Bu.binding&&Bu.binding("util")}catch{}}(),ft=TA;var Md=ft&&ft.isTypedArray,RA=Md?$t(Md):Ld,ui=RA;var AA=Object.prototype,EA=AA.hasOwnProperty;function vA(t,e){var r=N(t),n=!r&&mr(t),i=!r&&!n&&qt(t),a=!r&&!n&&!i&&ui(t),o=r||n||i||a,l=o?Nd(t.length,String):[],u=l.length;for(var c in t)(e||EA.call(t,c))&&!(o&&(c=="length"||i&&(c=="offset"||c=="parent")||a&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||fr(c,u)))&&l.push(c);return l}s(vA,"arrayLikeKeys");var ao=vA;function IA(t,e){return function(r){return t(e(r))}}s(IA,"overArg");var oo=IA;var kA=oo(Object.keys,Object),Dd=kA;var SA=Object.prototype,CA=SA.hasOwnProperty;function NA(t){if(!bt(t))return Dd(t);var e=[];for(var r in Object(t))CA.call(t,r)&&r!="constructor"&&e.push(r);return e}s(NA,"baseKeys");var lo=NA;function wA(t){return Ee(t)?ao(t):lo(t)}s(wA,"keys");var Z=wA;var _A=Object.prototype,bA=_A.hasOwnProperty,$A=Cd(function(t,e){if(bt(e)||Ee(e)){_t(e,Z(e),t);return}for(var r in e)bA.call(e,r)&&dr(t,r,e[r])}),$e=$A;function OA(t){var e=[];if(t!=null)for(var r in Object(t))e.push(r);return e}s(OA,"nativeKeysIn");var Fd=OA;var LA=Object.prototype,PA=LA.hasOwnProperty;function MA(t){if(!de(t))return Fd(t);var e=bt(t),r=[];for(var n in t)n=="constructor"&&(e||!PA.call(t,n))||r.push(n);return r}s(MA,"baseKeysIn");var Gd=MA;function DA(t){return Ee(t)?ao(t,!0):Gd(t)}s(DA,"keysIn");var hr=DA;var FA=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,GA=/^\w*$/;function UA(t,e){if(N(t))return!1;var r=typeof t;return r=="number"||r=="symbol"||r=="boolean"||t==null||St(t)?!0:GA.test(t)||!FA.test(t)||e!=null&&t in Object(e)}s(UA,"isKey");var ci=UA;var BA=Qe(Object,"create"),Xt=BA;function jA(){this.__data__=Xt?Xt(null):{},this.size=0}s(jA,"hashClear");var Ud=jA;function WA(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}s(WA,"hashDelete");var Bd=WA;var KA="__lodash_hash_undefined__",HA=Object.prototype,VA=HA.hasOwnProperty;function zA(t){var e=this.__data__;if(Xt){var r=e[t];return r===KA?void 0:r}return VA.call(e,t)?e[t]:void 0}s(zA,"hashGet");var jd=zA;var qA=Object.prototype,XA=qA.hasOwnProperty;function YA(t){var e=this.__data__;return Xt?e[t]!==void 0:XA.call(e,t)}s(YA,"hashHas");var Wd=YA;var JA="__lodash_hash_undefined__";function ZA(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=Xt&&e===void 0?JA:e,this}s(ZA,"hashSet");var Kd=ZA;function fi(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}s(fi,"Hash");fi.prototype.clear=Ud;fi.prototype.delete=Bd;fi.prototype.get=jd;fi.prototype.has=Wd;fi.prototype.set=Kd;var ju=fi;function QA(){this.__data__=[],this.size=0}s(QA,"listCacheClear");var Hd=QA;function eE(t,e){for(var r=t.length;r--;)if(wt(t[r][0],e))return r;return-1}s(eE,"assocIndexOf");var gr=eE;var tE=Array.prototype,rE=tE.splice;function nE(t){var e=this.__data__,r=gr(e,t);if(r<0)return!1;var n=e.length-1;return r==n?e.pop():rE.call(e,r,1),--this.size,!0}s(nE,"listCacheDelete");var Vd=nE;function iE(t){var e=this.__data__,r=gr(e,t);return r<0?void 0:e[r][1]}s(iE,"listCacheGet");var zd=iE;function sE(t){return gr(this.__data__,t)>-1}s(sE,"listCacheHas");var qd=sE;function aE(t,e){var r=this.__data__,n=gr(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this}s(aE,"listCacheSet");var Xd=aE;function di(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}s(di,"ListCache");di.prototype.clear=Hd;di.prototype.delete=Vd;di.prototype.get=zd;di.prototype.has=qd;di.prototype.set=Xd;var yr=di;var oE=Qe(Ae,"Map"),xr=oE;function lE(){this.size=0,this.__data__={hash:new ju,map:new(xr||yr),string:new ju}}s(lE,"mapCacheClear");var Yd=lE;function uE(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}s(uE,"isKeyable");var Jd=uE;function cE(t,e){var r=t.__data__;return Jd(e)?r[typeof e=="string"?"string":"hash"]:r.map}s(cE,"getMapData");var Tr=cE;function fE(t){var e=Tr(this,t).delete(t);return this.size-=e?1:0,e}s(fE,"mapCacheDelete");var Zd=fE;function dE(t){return Tr(this,t).get(t)}s(dE,"mapCacheGet");var Qd=dE;function pE(t){return Tr(this,t).has(t)}s(pE,"mapCacheHas");var ep=pE;function mE(t,e){var r=Tr(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this}s(mE,"mapCacheSet");var tp=mE;function pi(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}s(pi,"MapCache");pi.prototype.clear=Yd;pi.prototype.delete=Zd;pi.prototype.get=Qd;pi.prototype.has=ep;pi.prototype.set=tp;var Hr=pi;var hE="Expected a function";function Wu(t,e){if(typeof t!="function"||e!=null&&typeof e!="function")throw new TypeError(hE);var r=s(function(){var n=arguments,i=e?e.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=t.apply(this,n);return r.cache=a.set(i,o)||a,o},"memoized");return r.cache=new(Wu.Cache||Hr),r}s(Wu,"memoize");Wu.Cache=Hr;var rp=Wu;var gE=500;function yE(t){var e=rp(t,function(n){return r.size===gE&&r.clear(),n}),r=e.cache;return e}s(yE,"memoizeCapped");var np=yE;var xE=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,TE=/\\(\\)?/g,RE=np(function(t){var e=[];return t.charCodeAt(0)===46&&e.push(""),t.replace(xE,function(r,n,i,a){e.push(i?a.replace(TE,"$1"):n||r)}),e}),ip=RE;function AE(t){return t==null?"":id(t)}s(AE,"toString");var sp=AE;function EE(t,e){return N(t)?t:ci(t,e)?[t]:ip(sp(t))}s(EE,"castPath");var Rr=EE;var vE=1/0;function IE(t){if(typeof t=="string"||St(t))return t;var e=t+"";return e=="0"&&1/t==-vE?"-0":e}s(IE,"toKey");var Ot=IE;function kE(t,e){e=Rr(e,t);for(var r=0,n=e.length;t!=null&&r<n;)t=t[Ot(e[r++])];return r&&r==n?t:void 0}s(kE,"baseGet");var mi=kE;function SE(t,e,r){var n=t==null?void 0:mi(t,e);return n===void 0?r:n}s(SE,"get");var ap=SE;function CE(t,e){for(var r=-1,n=e.length,i=t.length;++r<n;)t[i+r]=e[r];return t}s(CE,"arrayPush");var hi=CE;var op=Ce?Ce.isConcatSpreadable:void 0;function NE(t){return N(t)||mr(t)||!!(op&&t&&t[op])}s(NE,"isFlattenable");var lp=NE;function up(t,e,r,n,i){var a=-1,o=t.length;for(r||(r=lp),i||(i=[]);++a<o;){var l=t[a];e>0&&r(l)?e>1?up(l,e-1,r,n,i):hi(i,l):n||(i[i.length]=l)}return i}s(up,"baseFlatten");var gi=up;function wE(t){var e=t==null?0:t.length;return e?gi(t,1):[]}s(wE,"flatten");var ye=wE;var _E=oo(Object.getPrototypeOf,Object),uo=_E;function bE(t,e,r){var n=-1,i=t.length;e<0&&(e=-e>i?0:i+e),r=r>i?i:r,r<0&&(r+=i),i=e>r?0:r-e>>>0,e>>>=0;for(var a=Array(i);++n<i;)a[n]=t[n+e];return a}s(bE,"baseSlice");var co=bE;function $E(t,e,r,n){var i=-1,a=t==null?0:t.length;for(n&&a&&(r=t[++i]);++i<a;)r=e(r,t[i],i,t);return r}s($E,"arrayReduce");var cp=$E;function OE(){this.__data__=new yr,this.size=0}s(OE,"stackClear");var fp=OE;function LE(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}s(LE,"stackDelete");var dp=LE;function PE(t){return this.__data__.get(t)}s(PE,"stackGet");var pp=PE;function ME(t){return this.__data__.has(t)}s(ME,"stackHas");var mp=ME;var DE=200;function FE(t,e){var r=this.__data__;if(r instanceof yr){var n=r.__data__;if(!xr||n.length<DE-1)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new Hr(n)}return r.set(t,e),this.size=r.size,this}s(FE,"stackSet");var hp=FE;function yi(t){var e=this.__data__=new yr(t);this.size=e.size}s(yi,"Stack");yi.prototype.clear=fp;yi.prototype.delete=dp;yi.prototype.get=pp;yi.prototype.has=mp;yi.prototype.set=hp;var Ar=yi;function GE(t,e){return t&&_t(e,Z(e),t)}s(GE,"baseAssign");var gp=GE;function UE(t,e){return t&&_t(e,hr(e),t)}s(UE,"baseAssignIn");var yp=UE;var Ap=typeof exports=="object"&&exports&&!exports.nodeType&&exports,xp=Ap&&typeof module=="object"&&module&&!module.nodeType&&module,BE=xp&&xp.exports===Ap,Tp=BE?Ae.Buffer:void 0,Rp=Tp?Tp.allocUnsafe:void 0;function jE(t,e){if(e)return t.slice();var r=t.length,n=Rp?Rp(r):new t.constructor(r);return t.copy(n),n}s(jE,"cloneBuffer");var Ep=jE;function WE(t,e){for(var r=-1,n=t==null?0:t.length,i=0,a=[];++r<n;){var o=t[r];e(o,r,t)&&(a[i++]=o)}return a}s(WE,"arrayFilter");var xi=WE;function KE(){return[]}s(KE,"stubArray");var fo=KE;var HE=Object.prototype,VE=HE.propertyIsEnumerable,vp=Object.getOwnPropertySymbols,zE=vp?function(t){return t==null?[]:(t=Object(t),xi(vp(t),function(e){return VE.call(t,e)}))}:fo,Ti=zE;function qE(t,e){return _t(t,Ti(t),e)}s(qE,"copySymbols");var Ip=qE;var XE=Object.getOwnPropertySymbols,YE=XE?function(t){for(var e=[];t;)hi(e,Ti(t)),t=uo(t);return e}:fo,po=YE;function JE(t,e){return _t(t,po(t),e)}s(JE,"copySymbolsIn");var kp=JE;function ZE(t,e,r){var n=e(t);return N(t)?n:hi(n,r(t))}s(ZE,"baseGetAllKeys");var mo=ZE;function QE(t){return mo(t,Z,Ti)}s(QE,"getAllKeys");var Ss=QE;function ev(t){return mo(t,hr,po)}s(ev,"getAllKeysIn");var ho=ev;var tv=Qe(Ae,"DataView"),go=tv;var rv=Qe(Ae,"Promise"),yo=rv;var nv=Qe(Ae,"Set"),Er=nv;var Sp="[object Map]",iv="[object Object]",Cp="[object Promise]",Np="[object Set]",wp="[object WeakMap]",_p="[object DataView]",sv=zt(go),av=zt(xr),ov=zt(yo),lv=zt(Er),uv=zt(ro),Vr=Ke;(go&&Vr(new go(new ArrayBuffer(1)))!=_p||xr&&Vr(new xr)!=Sp||yo&&Vr(yo.resolve())!=Cp||Er&&Vr(new Er)!=Np||ro&&Vr(new ro)!=wp)&&(Vr=s(function(t){var e=Ke(t),r=e==iv?t.constructor:void 0,n=r?zt(r):"";if(n)switch(n){case sv:return _p;case av:return Sp;case ov:return Cp;case lv:return Np;case uv:return wp}return e},"getTag"));var xt=Vr;var cv=Object.prototype,fv=cv.hasOwnProperty;function dv(t){var e=t.length,r=new t.constructor(e);return e&&typeof t[0]=="string"&&fv.call(t,"index")&&(r.index=t.index,r.input=t.input),r}s(dv,"initCloneArray");var bp=dv;var pv=Ae.Uint8Array,Ri=pv;function mv(t){var e=new t.constructor(t.byteLength);return new Ri(e).set(new Ri(t)),e}s(mv,"cloneArrayBuffer");var Ai=mv;function hv(t,e){var r=e?Ai(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)}s(hv,"cloneDataView");var $p=hv;var gv=/\w*$/;function yv(t){var e=new t.constructor(t.source,gv.exec(t));return e.lastIndex=t.lastIndex,e}s(yv,"cloneRegExp");var Op=yv;var Lp=Ce?Ce.prototype:void 0,Pp=Lp?Lp.valueOf:void 0;function xv(t){return Pp?Object(Pp.call(t)):{}}s(xv,"cloneSymbol");var Mp=xv;function Tv(t,e){var r=e?Ai(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}s(Tv,"cloneTypedArray");var Dp=Tv;var Rv="[object Boolean]",Av="[object Date]",Ev="[object Map]",vv="[object Number]",Iv="[object RegExp]",kv="[object Set]",Sv="[object String]",Cv="[object Symbol]",Nv="[object ArrayBuffer]",wv="[object DataView]",_v="[object Float32Array]",bv="[object Float64Array]",$v="[object Int8Array]",Ov="[object Int16Array]",Lv="[object Int32Array]",Pv="[object Uint8Array]",Mv="[object Uint8ClampedArray]",Dv="[object Uint16Array]",Fv="[object Uint32Array]";function Gv(t,e,r){var n=t.constructor;switch(e){case Nv:return Ai(t);case Rv:case Av:return new n(+t);case wv:return $p(t,r);case _v:case bv:case $v:case Ov:case Lv:case Pv:case Mv:case Dv:case Fv:return Dp(t,r);case Ev:return new n;case vv:case Sv:return new n(t);case Iv:return Op(t);case kv:return new n;case Cv:return Mp(t)}}s(Gv,"initCloneByTag");var Fp=Gv;function Uv(t){return typeof t.constructor=="function"&&!bt(t)?gd(uo(t)):{}}s(Uv,"initCloneObject");var Gp=Uv;var Bv="[object Map]";function jv(t){return ge(t)&&xt(t)==Bv}s(jv,"baseIsMap");var Up=jv;var Bp=ft&&ft.isMap,Wv=Bp?$t(Bp):Up,jp=Wv;var Kv="[object Set]";function Hv(t){return ge(t)&&xt(t)==Kv}s(Hv,"baseIsSet");var Wp=Hv;var Kp=ft&&ft.isSet,Vv=Kp?$t(Kp):Wp,Hp=Vv;var zv=1,qv=2,Xv=4,Vp="[object Arguments]",Yv="[object Array]",Jv="[object Boolean]",Zv="[object Date]",Qv="[object Error]",zp="[object Function]",eI="[object GeneratorFunction]",tI="[object Map]",rI="[object Number]",qp="[object Object]",nI="[object RegExp]",iI="[object Set]",sI="[object String]",aI="[object Symbol]",oI="[object WeakMap]",lI="[object ArrayBuffer]",uI="[object DataView]",cI="[object Float32Array]",fI="[object Float64Array]",dI="[object Int8Array]",pI="[object Int16Array]",mI="[object Int32Array]",hI="[object Uint8Array]",gI="[object Uint8ClampedArray]",yI="[object Uint16Array]",xI="[object Uint32Array]",Q={};Q[Vp]=Q[Yv]=Q[lI]=Q[uI]=Q[Jv]=Q[Zv]=Q[cI]=Q[fI]=Q[dI]=Q[pI]=Q[mI]=Q[tI]=Q[rI]=Q[qp]=Q[nI]=Q[iI]=Q[sI]=Q[aI]=Q[hI]=Q[gI]=Q[yI]=Q[xI]=!0;Q[Qv]=Q[zp]=Q[oI]=!1;function xo(t,e,r,n,i,a){var o,l=e&zv,u=e&qv,c=e&Xv;if(r&&(o=i?r(t,n,i,a):r(t)),o!==void 0)return o;if(!de(t))return t;var f=N(t);if(f){if(o=bp(t),!l)return xd(t,o)}else{var d=xt(t),p=d==zp||d==eI;if(qt(t))return Ep(t,l);if(d==qp||d==Vp||p&&!i){if(o=u||p?{}:Gp(t),!l)return u?kp(t,yp(o,t)):Ip(t,gp(o,t))}else{if(!Q[d])return i?t:{};o=Fp(t,d,l)}}a||(a=new Ar);var m=a.get(t);if(m)return m;a.set(t,o),Hp(t)?t.forEach(function(A){o.add(xo(A,e,r,A,t,a))}):jp(t)&&t.forEach(function(A,T){o.set(T,xo(A,e,r,T,t,a))});var g=c?u?ho:Ss:u?hr:Z,y=f?void 0:g(t);return no(y||t,function(A,T){y&&(T=A,A=t[T]),dr(o,T,xo(A,e,r,T,t,a))}),o}s(xo,"baseClone");var Xp=xo;var TI=4;function RI(t){return Xp(t,TI)}s(RI,"clone");var ee=RI;function AI(t){for(var e=-1,r=t==null?0:t.length,n=0,i=[];++e<r;){var a=t[e];a&&(i[n++]=a)}return i}s(AI,"compact");var Lt=AI;var EI="__lodash_hash_undefined__";function vI(t){return this.__data__.set(t,EI),this}s(vI,"setCacheAdd");var Yp=vI;function II(t){return this.__data__.has(t)}s(II,"setCacheHas");var Jp=II;function To(t){var e=-1,r=t==null?0:t.length;for(this.__data__=new Hr;++e<r;)this.add(t[e])}s(To,"SetCache");To.prototype.add=To.prototype.push=Yp;To.prototype.has=Jp;var Ei=To;function kI(t,e){for(var r=-1,n=t==null?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}s(kI,"arraySome");var Ro=kI;function SI(t,e){return t.has(e)}s(SI,"cacheHas");var vi=SI;var CI=1,NI=2;function wI(t,e,r,n,i,a){var o=r&CI,l=t.length,u=e.length;if(l!=u&&!(o&&u>l))return!1;var c=a.get(t),f=a.get(e);if(c&&f)return c==e&&f==t;var d=-1,p=!0,m=r&NI?new Ei:void 0;for(a.set(t,e),a.set(e,t);++d<l;){var g=t[d],y=e[d];if(n)var A=o?n(y,g,d,e,t,a):n(g,y,d,t,e,a);if(A!==void 0){if(A)continue;p=!1;break}if(m){if(!Ro(e,function(T,E){if(!vi(m,E)&&(g===T||i(g,T,r,n,a)))return m.push(E)})){p=!1;break}}else if(!(g===y||i(g,y,r,n,a))){p=!1;break}}return a.delete(t),a.delete(e),p}s(wI,"equalArrays");var Ao=wI;function _I(t){var e=-1,r=Array(t.size);return t.forEach(function(n,i){r[++e]=[i,n]}),r}s(_I,"mapToArray");var Zp=_I;function bI(t){var e=-1,r=Array(t.size);return t.forEach(function(n){r[++e]=n}),r}s(bI,"setToArray");var Ii=bI;var $I=1,OI=2,LI="[object Boolean]",PI="[object Date]",MI="[object Error]",DI="[object Map]",FI="[object Number]",GI="[object RegExp]",UI="[object Set]",BI="[object String]",jI="[object Symbol]",WI="[object ArrayBuffer]",KI="[object DataView]",Qp=Ce?Ce.prototype:void 0,Ku=Qp?Qp.valueOf:void 0;function HI(t,e,r,n,i,a,o){switch(r){case KI:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case WI:return!(t.byteLength!=e.byteLength||!a(new Ri(t),new Ri(e)));case LI:case PI:case FI:return wt(+t,+e);case MI:return t.name==e.name&&t.message==e.message;case GI:case BI:return t==e+"";case DI:var l=Zp;case UI:var u=n&$I;if(l||(l=Ii),t.size!=e.size&&!u)return!1;var c=o.get(t);if(c)return c==e;n|=OI,o.set(t,e);var f=Ao(l(t),l(e),n,i,a,o);return o.delete(t),f;case jI:if(Ku)return Ku.call(t)==Ku.call(e)}return!1}s(HI,"equalByTag");var em=HI;var VI=1,zI=Object.prototype,qI=zI.hasOwnProperty;function XI(t,e,r,n,i,a){var o=r&VI,l=Ss(t),u=l.length,c=Ss(e),f=c.length;if(u!=f&&!o)return!1;for(var d=u;d--;){var p=l[d];if(!(o?p in e:qI.call(e,p)))return!1}var m=a.get(t),g=a.get(e);if(m&&g)return m==e&&g==t;var y=!0;a.set(t,e),a.set(e,t);for(var A=o;++d<u;){p=l[d];var T=t[p],E=e[p];if(n)var R=o?n(E,T,p,e,t,a):n(T,E,p,t,e,a);if(!(R===void 0?T===E||i(T,E,r,n,a):R)){y=!1;break}A||(A=p=="constructor")}if(y&&!A){var O=t.constructor,L=e.constructor;O!=L&&"constructor"in t&&"constructor"in e&&!(typeof O=="function"&&O instanceof O&&typeof L=="function"&&L instanceof L)&&(y=!1)}return a.delete(t),a.delete(e),y}s(XI,"equalObjects");var tm=XI;var YI=1,rm="[object Arguments]",nm="[object Array]",Eo="[object Object]",JI=Object.prototype,im=JI.hasOwnProperty;function ZI(t,e,r,n,i,a){var o=N(t),l=N(e),u=o?nm:xt(t),c=l?nm:xt(e);u=u==rm?Eo:u,c=c==rm?Eo:c;var f=u==Eo,d=c==Eo,p=u==c;if(p&&qt(t)){if(!qt(e))return!1;o=!0,f=!1}if(p&&!f)return a||(a=new Ar),o||ui(t)?Ao(t,e,r,n,i,a):em(t,e,u,r,n,i,a);if(!(r&YI)){var m=f&&im.call(t,"__wrapped__"),g=d&&im.call(e,"__wrapped__");if(m||g){var y=m?t.value():t,A=g?e.value():e;return a||(a=new Ar),i(y,A,r,n,a)}}return p?(a||(a=new Ar),tm(t,e,r,n,i,a)):!1}s(ZI,"baseIsEqualDeep");var sm=ZI;function am(t,e,r,n,i){return t===e?!0:t==null||e==null||!ge(t)&&!ge(e)?t!==t&&e!==e:sm(t,e,r,n,am,i)}s(am,"baseIsEqual");var vo=am;var QI=1,ek=2;function tk(t,e,r,n){var i=r.length,a=i,o=!n;if(t==null)return!a;for(t=Object(t);i--;){var l=r[i];if(o&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++i<a;){l=r[i];var u=l[0],c=t[u],f=l[1];if(o&&l[2]){if(c===void 0&&!(u in t))return!1}else{var d=new Ar;if(n)var p=n(c,f,u,t,e,d);if(!(p===void 0?vo(f,c,QI|ek,n,d):p))return!1}}return!0}s(tk,"baseIsMatch");var om=tk;function rk(t){return t===t&&!de(t)}s(rk,"isStrictComparable");var Io=rk;function nk(t){for(var e=Z(t),r=e.length;r--;){var n=e[r],i=t[n];e[r]=[n,i,Io(i)]}return e}s(nk,"getMatchData");var lm=nk;function ik(t,e){return function(r){return r==null?!1:r[t]===e&&(e!==void 0||t in Object(r))}}s(ik,"matchesStrictComparable");var ko=ik;function sk(t){var e=lm(t);return e.length==1&&e[0][2]?ko(e[0][0],e[0][1]):function(r){return r===t||om(r,t,e)}}s(sk,"baseMatches");var um=sk;function ak(t,e){return t!=null&&e in Object(t)}s(ak,"baseHasIn");var cm=ak;function ok(t,e,r){e=Rr(e,t);for(var n=-1,i=e.length,a=!1;++n<i;){var o=Ot(e[n]);if(!(a=t!=null&&r(t,o)))break;t=t[o]}return a||++n!=i?a:(i=t==null?0:t.length,!!i&&li(i)&&fr(o,i)&&(N(t)||mr(t)))}s(ok,"hasPath");var So=ok;function lk(t,e){return t!=null&&So(t,e,cm)}s(lk,"hasIn");var fm=lk;var uk=1,ck=2;function fk(t,e){return ci(t)&&Io(e)?ko(Ot(t),e):function(r){var n=ap(r,t);return n===void 0&&n===e?fm(r,t):vo(e,n,uk|ck)}}s(fk,"baseMatchesProperty");var dm=fk;function dk(t){return function(e){return e?.[t]}}s(dk,"baseProperty");var pm=dk;function pk(t){return function(e){return mi(e,t)}}s(pk,"basePropertyDeep");var mm=pk;function mk(t){return ci(t)?pm(Ot(t)):mm(t)}s(mk,"property");var hm=mk;function hk(t){return typeof t=="function"?t:t==null?Ze:typeof t=="object"?N(t)?dm(t[0],t[1]):um(t):hm(t)}s(hk,"baseIteratee");var he=hk;function gk(t,e,r,n){for(var i=-1,a=t==null?0:t.length;++i<a;){var o=t[i];e(n,o,r(o),t)}return n}s(gk,"arrayAggregator");var gm=gk;function yk(t){return function(e,r,n){for(var i=-1,a=Object(e),o=n(e),l=o.length;l--;){var u=o[t?l:++i];if(r(a[u],u,a)===!1)break}return e}}s(yk,"createBaseFor");var ym=yk;var xk=ym(),xm=xk;function Tk(t,e){return t&&xm(t,e,Z)}s(Tk,"baseForOwn");var Tm=Tk;function Rk(t,e){return function(r,n){if(r==null)return r;if(!Ee(r))return t(r,n);for(var i=r.length,a=e?i:-1,o=Object(r);(e?a--:++a<i)&&n(o[a],a,o)!==!1;);return r}}s(Rk,"createBaseEach");var Rm=Rk;var Ak=Rm(Tm),et=Ak;function Ek(t,e,r,n){return et(t,function(i,a,o){e(n,i,r(i),o)}),n}s(Ek,"baseAggregator");var Am=Ek;function vk(t,e){return function(r,n){var i=N(r)?gm:Am,a=e?e():{};return i(r,t,he(n,2),a)}}s(vk,"createAggregator");var Em=vk;var vm=Object.prototype,Ik=vm.hasOwnProperty,kk=oi(function(t,e){t=Object(t);var r=-1,n=e.length,i=n>2?e[2]:void 0;for(i&&pr(e[0],e[1],i)&&(n=1);++r<n;)for(var a=e[r],o=hr(a),l=-1,u=o.length;++l<u;){var c=o[l],f=t[c];(f===void 0||wt(f,vm[c])&&!Ik.call(t,c))&&(t[c]=a[c])}return t}),ki=kk;function Sk(t){return ge(t)&&Ee(t)}s(Sk,"isArrayLikeObject");var Hu=Sk;function Ck(t,e,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}s(Ck,"arrayIncludesWith");var Co=Ck;var Nk=200;function wk(t,e,r,n){var i=-1,a=so,o=!0,l=t.length,u=[],c=e.length;if(!l)return u;r&&(e=Ct(e,$t(r))),n?(a=Co,o=!1):e.length>=Nk&&(a=vi,o=!1,e=new Ei(e));e:for(;++i<l;){var f=t[i],d=r==null?f:r(f);if(f=n||f!==0?f:0,o&&d===d){for(var p=c;p--;)if(e[p]===d)continue e;u.push(f)}else a(e,d,n)||u.push(f)}return u}s(wk,"baseDifference");var Im=wk;var _k=oi(function(t,e){return Hu(t)?Im(t,gi(e,1,Hu,!0)):[]}),vr=_k;function bk(t){var e=t==null?0:t.length;return e?t[e-1]:void 0}s(bk,"last");var Pt=bk;function $k(t,e,r){var n=t==null?0:t.length;return n?(e=r||e===void 0?1:Nt(e),co(t,e<0?0:e,n)):[]}s($k,"drop");var xe=$k;function Ok(t,e,r){var n=t==null?0:t.length;return n?(e=r||e===void 0?1:Nt(e),e=n-e,co(t,0,e<0?0:e)):[]}s(Ok,"dropRight");var Yt=Ok;function Lk(t){return typeof t=="function"?t:Ze}s(Lk,"castFunction");var km=Lk;function Pk(t,e){var r=N(t)?no:et;return r(t,km(e))}s(Pk,"forEach");var k=Pk;function Mk(t,e){for(var r=-1,n=t==null?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}s(Mk,"arrayEvery");var Sm=Mk;function Dk(t,e){var r=!0;return et(t,function(n,i,a){return r=!!e(n,i,a),r}),r}s(Dk,"baseEvery");var Cm=Dk;function Fk(t,e,r){var n=N(t)?Sm:Cm;return r&&pr(t,e,r)&&(e=void 0),n(t,he(e,3))}s(Fk,"every");var Ge=Fk;function Gk(t,e){var r=[];return et(t,function(n,i,a){e(n,i,a)&&r.push(n)}),r}s(Gk,"baseFilter");var No=Gk;function Uk(t,e){var r=N(t)?xi:No;return r(t,he(e,3))}s(Uk,"filter");var Ne=Uk;function Bk(t){return function(e,r,n){var i=Object(e);if(!Ee(e)){var a=he(r,3);e=Z(e),r=s(function(l){return a(i[l],l,i)},"predicate")}var o=t(e,r,n);return o>-1?i[a?e[o]:o]:void 0}}s(Bk,"createFind");var Nm=Bk;var jk=Math.max;function Wk(t,e,r){var n=t==null?0:t.length;if(!n)return-1;var i=r==null?0:Nt(r);return i<0&&(i=jk(n+i,0)),io(t,he(e,3),i)}s(Wk,"findIndex");var wm=Wk;var Kk=Nm(wm),Mt=Kk;function Hk(t){return t&&t.length?t[0]:void 0}s(Hk,"head");var we=Hk;function Vk(t,e){var r=-1,n=Ee(t)?Array(t.length):[];return et(t,function(i,a,o){n[++r]=e(i,a,o)}),n}s(Vk,"baseMap");var _m=Vk;function zk(t,e){var r=N(t)?Ct:_m;return r(t,he(e,3))}s(zk,"map");var v=zk;function qk(t,e){return gi(v(t,e),1)}s(qk,"flatMap");var Oe=qk;var Xk=Object.prototype,Yk=Xk.hasOwnProperty,Jk=Em(function(t,e,r){Yk.call(t,r)?t[r].push(e):ai(t,r,[e])}),Vu=Jk;var Zk=Object.prototype,Qk=Zk.hasOwnProperty;function eS(t,e){return t!=null&&Qk.call(t,e)}s(eS,"baseHas");var bm=eS;function tS(t,e){return t!=null&&So(t,e,bm)}s(tS,"has");var S=tS;var rS="[object String]";function nS(t){return typeof t=="string"||!N(t)&&ge(t)&&Ke(t)==rS}s(nS,"isString");var Ie=nS;function iS(t,e){return Ct(e,function(r){return t[r]})}s(iS,"baseValues");var $m=iS;function sS(t){return t==null?[]:$m(t,Z(t))}s(sS,"values");var q=sS;var aS=Math.max;function oS(t,e,r,n){t=Ee(t)?t:q(t),r=r&&!n?Nt(r):0;var i=t.length;return r<0&&(r=aS(i+r,0)),Ie(t)?r<=i&&t.indexOf(e,r)>-1:!!i&&si(t,e,r)>-1}s(oS,"includes");var oe=oS;var lS=Math.max;function uS(t,e,r){var n=t==null?0:t.length;if(!n)return-1;var i=r==null?0:Nt(r);return i<0&&(i=lS(n+i,0)),si(t,e,i)}s(uS,"indexOf");var wo=uS;var cS="[object Map]",fS="[object Set]",dS=Object.prototype,pS=dS.hasOwnProperty;function mS(t){if(t==null)return!0;if(Ee(t)&&(N(t)||typeof t=="string"||typeof t.splice=="function"||qt(t)||ui(t)||mr(t)))return!t.length;var e=xt(t);if(e==cS||e==fS)return!t.size;if(bt(t))return!lo(t).length;for(var r in t)if(pS.call(t,r))return!1;return!0}s(mS,"isEmpty");var D=mS;var hS="[object RegExp]";function gS(t){return ge(t)&&Ke(t)==hS}s(gS,"baseIsRegExp");var Om=gS;var Lm=ft&&ft.isRegExp,yS=Lm?$t(Lm):Om,dt=yS;function xS(t){return t===void 0}s(xS,"isUndefined");var Ue=xS;function TS(t,e){return t<e}s(TS,"baseLt");var Pm=TS;function RS(t,e,r){for(var n=-1,i=t.length;++n<i;){var a=t[n],o=e(a);if(o!=null&&(l===void 0?o===o&&!St(o):r(o,l)))var l=o,u=a}return u}s(RS,"baseExtremum");var Mm=RS;function AS(t){return t&&t.length?Mm(t,Ze,Pm):void 0}s(AS,"min");var Dm=AS;var ES="Expected a function";function vS(t){if(typeof t!="function")throw new TypeError(ES);return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}s(vS,"negate");var Fm=vS;function IS(t,e,r,n){if(!de(t))return t;e=Rr(e,t);for(var i=-1,a=e.length,o=a-1,l=t;l!=null&&++i<a;){var u=Ot(e[i]),c=r;if(u==="__proto__"||u==="constructor"||u==="prototype")return t;if(i!=o){var f=l[u];c=n?n(f,u,l):void 0,c===void 0&&(c=de(f)?f:fr(e[i+1])?[]:{})}dr(l,u,c),l=l[u]}return t}s(IS,"baseSet");var Gm=IS;function kS(t,e,r){for(var n=-1,i=e.length,a={};++n<i;){var o=e[n],l=mi(t,o);r(l,o)&&Gm(a,Rr(o,t),l)}return a}s(kS,"basePickBy");var Um=kS;function SS(t,e){if(t==null)return{};var r=Ct(ho(t),function(n){return[n]});return e=he(e),Um(t,r,function(n,i){return e(n,i[0])})}s(SS,"pickBy");var tt=SS;function CS(t,e,r,n,i){return i(t,function(a,o,l){r=n?(n=!1,a):e(r,a,o,l)}),r}s(CS,"baseReduce");var Bm=CS;function NS(t,e,r){var n=N(t)?cp:Bm,i=arguments.length<3;return n(t,he(e,4),r,i,et)}s(NS,"reduce");var me=NS;function wS(t,e){var r=N(t)?xi:No;return r(t,Fm(he(e,3)))}s(wS,"reject");var Ir=wS;function _S(t,e){var r;return et(t,function(n,i,a){return r=e(n,i,a),!r}),!!r}s(_S,"baseSome");var jm=_S;function bS(t,e,r){var n=N(t)?Ro:jm;return r&&pr(t,e,r)&&(e=void 0),n(t,he(e,3))}s(bS,"some");var Cs=bS;var $S=1/0,OS=Er&&1/Ii(new Er([,-0]))[1]==$S?function(t){return new Er(t)}:pe,Wm=OS;var LS=200;function PS(t,e,r){var n=-1,i=so,a=t.length,o=!0,l=[],u=l;if(r)o=!1,i=Co;else if(a>=LS){var c=e?null:Wm(t);if(c)return Ii(c);o=!1,i=vi,u=new Ei}else u=e?[]:l;e:for(;++n<a;){var f=t[n],d=e?e(f):f;if(f=r||f!==0?f:0,o&&d===d){for(var p=u.length;p--;)if(u[p]===d)continue e;e&&u.push(d),l.push(f)}else i(u,d,r)||(u!==l&&u.push(d),l.push(f))}return l}s(PS,"baseUniq");var _o=PS;function MS(t){return t&&t.length?_o(t):[]}s(MS,"uniq");var Si=MS;function DS(t,e){return t&&t.length?_o(t,he(e,2)):[]}s(DS,"uniqBy");var Km=DS;function Ci(t){console&&console.error&&console.error(`Error: ${t}`)}s(Ci,"PRINT_ERROR");function Ns(t){console&&console.warn&&console.warn(`Warning: ${t}`)}s(Ns,"PRINT_WARNING");function ws(t){let e=new Date().getTime(),r=t();return{time:new Date().getTime()-e,value:r}}s(ws,"timer");function _s(t){function e(){}s(e,"FakeConstructor"),e.prototype=t;let r=new e;function n(){return typeof r.bar}return s(n,"fakeAccess"),n(),n(),t;(0,eval)(t)}s(_s,"toFastProperties");function FS(t){return GS(t)?t.LABEL:t.name}s(FS,"tokenLabel");function GS(t){return Ie(t.LABEL)&&t.LABEL!==""}s(GS,"hasTokenLabel");var at=class{static{s(this,"AbstractProduction")}get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),k(this.definition,r=>{r.accept(e)})}},V=class extends at{static{s(this,"NonTerminal")}constructor(e){super([]),this.idx=1,$e(this,tt(e,r=>r!==void 0))}set definition(e){}get definition(){return this.referencedRule!==void 0?this.referencedRule.definition:[]}accept(e){e.visit(this)}},Ve=class extends at{static{s(this,"Rule")}constructor(e){super(e.definition),this.orgText="",$e(this,tt(e,r=>r!==void 0))}},te=class extends at{static{s(this,"Alternative")}constructor(e){super(e.definition),this.ignoreAmbiguities=!1,$e(this,tt(e,r=>r!==void 0))}},z=class extends at{static{s(this,"Option")}constructor(e){super(e.definition),this.idx=1,$e(this,tt(e,r=>r!==void 0))}},re=class extends at{static{s(this,"RepetitionMandatory")}constructor(e){super(e.definition),this.idx=1,$e(this,tt(e,r=>r!==void 0))}},ne=class extends at{static{s(this,"RepetitionMandatoryWithSeparator")}constructor(e){super(e.definition),this.idx=1,$e(this,tt(e,r=>r!==void 0))}},G=class extends at{static{s(this,"Repetition")}constructor(e){super(e.definition),this.idx=1,$e(this,tt(e,r=>r!==void 0))}},X=class extends at{static{s(this,"RepetitionWithSeparator")}constructor(e){super(e.definition),this.idx=1,$e(this,tt(e,r=>r!==void 0))}},Y=class extends at{static{s(this,"Alternation")}get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,$e(this,tt(e,r=>r!==void 0))}},F=class{static{s(this,"Terminal")}constructor(e){this.idx=1,$e(this,tt(e,r=>r!==void 0))}accept(e){e.visit(this)}};function bo(t){return v(t,Ni)}s(bo,"serializeGrammar");function Ni(t){function e(r){return v(r,Ni)}if(s(e,"convertDefinition"),t instanceof V){let r={type:"NonTerminal",name:t.nonTerminalName,idx:t.idx};return Ie(t.label)&&(r.label=t.label),r}else{if(t instanceof te)return{type:"Alternative",definition:e(t.definition)};if(t instanceof z)return{type:"Option",idx:t.idx,definition:e(t.definition)};if(t instanceof re)return{type:"RepetitionMandatory",idx:t.idx,definition:e(t.definition)};if(t instanceof ne)return{type:"RepetitionMandatoryWithSeparator",idx:t.idx,separator:Ni(new F({terminalType:t.separator})),definition:e(t.definition)};if(t instanceof X)return{type:"RepetitionWithSeparator",idx:t.idx,separator:Ni(new F({terminalType:t.separator})),definition:e(t.definition)};if(t instanceof G)return{type:"Repetition",idx:t.idx,definition:e(t.definition)};if(t instanceof Y)return{type:"Alternation",idx:t.idx,definition:e(t.definition)};if(t instanceof F){let r={type:"Terminal",name:t.terminalType.name,label:FS(t.terminalType),idx:t.idx};Ie(t.label)&&(r.terminalLabel=t.label);let n=t.terminalType.PATTERN;return t.terminalType.PATTERN&&(r.pattern=dt(n)?n.source:n),r}else{if(t instanceof Ve)return{type:"Rule",name:t.name,orgText:t.orgText,definition:e(t.definition)};throw Error("non exhaustive match")}}}s(Ni,"serializeProduction");var ze=class{static{s(this,"GAstVisitor")}visit(e){let r=e;switch(r.constructor){case V:return this.visitNonTerminal(r);case te:return this.visitAlternative(r);case z:return this.visitOption(r);case re:return this.visitRepetitionMandatory(r);case ne:return this.visitRepetitionMandatoryWithSeparator(r);case X:return this.visitRepetitionWithSeparator(r);case G:return this.visitRepetition(r);case Y:return this.visitAlternation(r);case F:return this.visitTerminal(r);case Ve:return this.visitRule(r);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}};function zu(t){return t instanceof te||t instanceof z||t instanceof G||t instanceof re||t instanceof ne||t instanceof X||t instanceof F||t instanceof Ve}s(zu,"isSequenceProd");function zr(t,e=[]){return t instanceof z||t instanceof G||t instanceof X?!0:t instanceof Y?Cs(t.definition,n=>zr(n,e)):t instanceof V&&oe(e,t)?!1:t instanceof at?(t instanceof V&&e.push(t),Ge(t.definition,n=>zr(n,e))):!1}s(zr,"isOptionalProd");function qu(t){return t instanceof Y}s(qu,"isBranchingProd");function rt(t){if(t instanceof V)return"SUBRULE";if(t instanceof z)return"OPTION";if(t instanceof Y)return"OR";if(t instanceof re)return"AT_LEAST_ONE";if(t instanceof ne)return"AT_LEAST_ONE_SEP";if(t instanceof X)return"MANY_SEP";if(t instanceof G)return"MANY";if(t instanceof F)return"CONSUME";throw Error("non exhaustive match")}s(rt,"getProductionDslName");var Jt=class{static{s(this,"RestWalker")}walk(e,r=[]){k(e.definition,(n,i)=>{let a=xe(e.definition,i+1);if(n instanceof V)this.walkProdRef(n,a,r);else if(n instanceof F)this.walkTerminal(n,a,r);else if(n instanceof te)this.walkFlat(n,a,r);else if(n instanceof z)this.walkOption(n,a,r);else if(n instanceof re)this.walkAtLeastOne(n,a,r);else if(n instanceof ne)this.walkAtLeastOneSep(n,a,r);else if(n instanceof X)this.walkManySep(n,a,r);else if(n instanceof G)this.walkMany(n,a,r);else if(n instanceof Y)this.walkOr(n,a,r);else throw Error("non exhaustive match")})}walkTerminal(e,r,n){}walkProdRef(e,r,n){}walkFlat(e,r,n){let i=r.concat(n);this.walk(e,i)}walkOption(e,r,n){let i=r.concat(n);this.walk(e,i)}walkAtLeastOne(e,r,n){let i=[new z({definition:e.definition})].concat(r,n);this.walk(e,i)}walkAtLeastOneSep(e,r,n){let i=Hm(e,r,n);this.walk(e,i)}walkMany(e,r,n){let i=[new z({definition:e.definition})].concat(r,n);this.walk(e,i)}walkManySep(e,r,n){let i=Hm(e,r,n);this.walk(e,i)}walkOr(e,r,n){let i=r.concat(n);k(e.definition,a=>{let o=new te({definition:[a]});this.walk(o,i)})}};function Hm(t,e,r){return[new z({definition:[new F({terminalType:t.separator})].concat(t.definition)})].concat(e,r)}s(Hm,"restForRepetitionWithSeparator");function qr(t){if(t instanceof V)return qr(t.referencedRule);if(t instanceof F)return jS(t);if(zu(t))return US(t);if(qu(t))return BS(t);throw Error("non exhaustive match")}s(qr,"first");function US(t){let e=[],r=t.definition,n=0,i=r.length>n,a,o=!0;for(;i&&o;)a=r[n],o=zr(a),e=e.concat(qr(a)),n=n+1,i=r.length>n;return Si(e)}s(US,"firstForSequence");function BS(t){let e=v(t.definition,r=>qr(r));return Si(ye(e))}s(BS,"firstForBranching");function jS(t){return[t.terminalType]}s(jS,"firstForTerminal");var $o="_~IN~_";var Xu=class extends Jt{static{s(this,"ResyncFollowsWalker")}constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,r,n){}walkProdRef(e,r,n){let i=WS(e.referencedRule,e.idx)+this.topProd.name,a=r.concat(n),o=new te({definition:a}),l=qr(o);this.follows[i]=l}};function Vm(t){let e={};return k(t,r=>{let n=new Xu(r).startWalking();$e(e,n)}),e}s(Vm,"computeAllProdsFollows");function WS(t,e){return t.name+e+$o}s(WS,"buildBetweenProdsFollowPrefix");var Oo={},KS=new jr;function wi(t){let e=t.toString();if(Oo.hasOwnProperty(e))return Oo[e];{let r=KS.pattern(e);return Oo[e]=r,r}}s(wi,"getRegExpAst");function zm(){Oo={}}s(zm,"clearRegExpParserCache");var Xm="Complement Sets are not supported for first char optimization",bs=`Unable to use "first char" lexer optimizations:
`;function Ym(t,e=!1){try{let r=wi(t);return Yu(r.value,{},r.flags.ignoreCase)}catch(r){if(r.message===Xm)e&&Ns(`${bs}	Unable to optimize: < ${t.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let n="";e&&(n=`
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.`),Ci(`${bs}
	Failed parsing: < ${t.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+n)}}return[]}s(Ym,"getOptimizedStartCodesIndices");function Yu(t,e,r){switch(t.type){case"Disjunction":for(let i=0;i<t.value.length;i++)Yu(t.value[i],e,r);break;case"Alternative":let n=t.value;for(let i=0;i<n.length;i++){let a=n[i];switch(a.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}let o=a;switch(o.type){case"Character":Lo(o.value,e,r);break;case"Set":if(o.complement===!0)throw Error(Xm);k(o.value,u=>{if(typeof u=="number")Lo(u,e,r);else{let c=u;if(r===!0)for(let f=c.from;f<=c.to;f++)Lo(f,e,r);else{for(let f=c.from;f<=c.to&&f<_i;f++)Lo(f,e,r);if(c.to>=_i){let f=c.from>=_i?c.from:_i,d=c.to,p=Dt(f),m=Dt(d);for(let g=p;g<=m;g++)e[g]=g}}}});break;case"Group":Yu(o.value,e,r);break;default:throw Error("Non Exhaustive Match")}let l=o.quantifier!==void 0&&o.quantifier.atLeast===0;if(o.type==="Group"&&Ju(o)===!1||o.type!=="Group"&&l===!1)break}break;default:throw Error("non exhaustive match!")}return q(e)}s(Yu,"firstCharOptimizedIndices");function Lo(t,e,r){let n=Dt(t);e[n]=n,r===!0&&HS(t,e)}s(Lo,"addOptimizedIdxToResult");function HS(t,e){let r=String.fromCharCode(t),n=r.toUpperCase();if(n!==r){let i=Dt(n.charCodeAt(0));e[i]=i}else{let i=r.toLowerCase();if(i!==r){let a=Dt(i.charCodeAt(0));e[a]=a}}}s(HS,"handleIgnoreCase");function qm(t,e){return Mt(t.value,r=>{if(typeof r=="number")return oe(e,r);{let n=r;return Mt(e,i=>n.from<=i&&i<=n.to)!==void 0}})}s(qm,"findCode");function Ju(t){let e=t.quantifier;return e&&e.atLeast===0?!0:t.value?N(t.value)?Ge(t.value,Ju):Ju(t.value):!1}s(Ju,"isWholeOptional");var Zu=class extends kt{static{s(this,"CharCodeFinder")}constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(this.found!==!0){switch(e.type){case"Lookahead":this.visitLookahead(e);return;case"NegativeLookahead":this.visitNegativeLookahead(e);return}super.visitChildren(e)}}visitCharacter(e){oe(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?qm(e,this.targetCharCodes)===void 0&&(this.found=!0):qm(e,this.targetCharCodes)!==void 0&&(this.found=!0)}};function Po(t,e){if(e instanceof RegExp){let r=wi(e),n=new Zu(t);return n.visit(r),n.found}else return Mt(e,r=>oe(t,r.charCodeAt(0)))!==void 0}s(Po,"canMatchCharCode");var Xr="PATTERN",bi="defaultMode",Mo="modes",ec=typeof new RegExp("(?:)").sticky=="boolean";function Qm(t,e){e=ki(e,{useSticky:ec,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r",`
`],tracer:s((E,R)=>R(),"tracer")});let r=e.tracer;r("initCharCodeToOptimizedIndexMap",()=>{uC()});let n;r("Reject Lexer.NA",()=>{n=Ir(t,E=>E[Xr]===ue.NA)});let i=!1,a;r("Transform Patterns",()=>{i=!1,a=v(n,E=>{let R=E[Xr];if(dt(R)){let O=R.source;return O.length===1&&O!=="^"&&O!=="$"&&O!=="."&&!R.ignoreCase?O:O.length===2&&O[0]==="\\"&&!oe(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],O[1])?O[1]:e.useSticky?Zm(R):Jm(R)}else{if(He(R))return i=!0,{exec:R};if(typeof R=="object")return i=!0,R;if(typeof R=="string"){if(R.length===1)return R;{let O=R.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),L=new RegExp(O);return e.useSticky?Zm(L):Jm(L)}}else throw Error("non exhaustive match")}})});let o,l,u,c,f;r("misc mapping",()=>{o=v(n,E=>E.tokenTypeIdx),l=v(n,E=>{let R=E.GROUP;if(R!==ue.SKIPPED){if(Ie(R))return R;if(Ue(R))return!1;throw Error("non exhaustive match")}}),u=v(n,E=>{let R=E.LONGER_ALT;if(R)return N(R)?v(R,L=>wo(n,L)):[wo(n,R)]}),c=v(n,E=>E.PUSH_MODE),f=v(n,E=>S(E,"POP_MODE"))});let d;r("Line Terminator Handling",()=>{let E=oh(e.lineTerminatorCharacters);d=v(n,R=>!1),e.positionTracking!=="onlyOffset"&&(d=v(n,R=>S(R,"LINE_BREAKS")?!!R.LINE_BREAKS:ah(R,E)===!1&&Po(E,R.PATTERN)))});let p,m,g,y;r("Misc Mapping #2",()=>{p=v(n,ih),m=v(a,oC),g=me(n,(E,R)=>{let O=R.GROUP;return Ie(O)&&O!==ue.SKIPPED&&(E[O]=[]),E},{}),y=v(a,(E,R)=>({pattern:a[R],longerAlt:u[R],canLineTerminator:d[R],isCustom:p[R],short:m[R],group:l[R],push:c[R],pop:f[R],tokenTypeIdx:o[R],tokenType:n[R]}))});let A=!0,T=[];return e.safeMode||r("First Char Optimization",()=>{T=me(n,(E,R,O)=>{if(typeof R.PATTERN=="string"){let L=R.PATTERN.charCodeAt(0),Se=Dt(L);Qu(E,Se,y[O])}else if(N(R.START_CHARS_HINT)){let L;k(R.START_CHARS_HINT,Se=>{let un=typeof Se=="string"?Se.charCodeAt(0):Se,Le=Dt(un);L!==Le&&(L=Le,Qu(E,Le,y[O]))})}else if(dt(R.PATTERN))if(R.PATTERN.unicode)A=!1,e.ensureOptimizations&&Ci(`${bs}	Unable to analyze < ${R.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{let L=Ym(R.PATTERN,e.ensureOptimizations);D(L)&&(A=!1),k(L,Se=>{Qu(E,Se,y[O])})}else e.ensureOptimizations&&Ci(`${bs}	TokenType: <${R.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),A=!1;return E},[])}),{emptyGroups:g,patternIdxToConfig:y,charCodeToPatternIdxToConfig:T,hasCustom:i,canBeOptimized:A}}s(Qm,"analyzeTokenTypes");function eh(t,e){let r=[],n=zS(t);r=r.concat(n.errors);let i=qS(n.valid),a=i.valid;return r=r.concat(i.errors),r=r.concat(VS(a)),r=r.concat(rC(a)),r=r.concat(nC(a,e)),r=r.concat(iC(a)),r}s(eh,"validatePatterns");function VS(t){let e=[],r=Ne(t,n=>dt(n[Xr]));return e=e.concat(YS(r)),e=e.concat(QS(r)),e=e.concat(eC(r)),e=e.concat(tC(r)),e=e.concat(JS(r)),e}s(VS,"validateRegExpPattern");function zS(t){let e=Ne(t,i=>!S(i,Xr)),r=v(e,i=>({message:"Token Type: ->"+i.name+"<- missing static 'PATTERN' property",type:le.MISSING_PATTERN,tokenTypes:[i]})),n=vr(t,e);return{errors:r,valid:n}}s(zS,"findMissingPatterns");function qS(t){let e=Ne(t,i=>{let a=i[Xr];return!dt(a)&&!He(a)&&!S(a,"exec")&&!Ie(a)}),r=v(e,i=>({message:"Token Type: ->"+i.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:le.INVALID_PATTERN,tokenTypes:[i]})),n=vr(t,e);return{errors:r,valid:n}}s(qS,"findInvalidPatterns");var XS=/[^\\][$]/;function YS(t){class e extends kt{static{s(this,"EndAnchorFinder")}constructor(){super(...arguments),this.found=!1}visitEndAnchor(a){this.found=!0}}let r=Ne(t,i=>{let a=i.PATTERN;try{let o=wi(a),l=new e;return l.visit(o),l.found}catch{return XS.test(a.source)}});return v(r,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain end of input anchor '$'
	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:le.EOI_ANCHOR_FOUND,tokenTypes:[i]}))}s(YS,"findEndOfInputAnchor");function JS(t){let e=Ne(t,n=>n.PATTERN.test(""));return v(e,n=>({message:"Token Type: ->"+n.name+"<- static 'PATTERN' must not match an empty string",type:le.EMPTY_MATCH_PATTERN,tokenTypes:[n]}))}s(JS,"findEmptyMatchRegExps");var ZS=/[^\\[][\^]|^\^/;function QS(t){class e extends kt{static{s(this,"StartAnchorFinder")}constructor(){super(...arguments),this.found=!1}visitStartAnchor(a){this.found=!0}}let r=Ne(t,i=>{let a=i.PATTERN;try{let o=wi(a),l=new e;return l.visit(o),l.found}catch{return ZS.test(a.source)}});return v(r,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain start of input anchor '^'
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:le.SOI_ANCHOR_FOUND,tokenTypes:[i]}))}s(QS,"findStartOfInputAnchor");function eC(t){let e=Ne(t,n=>{let i=n[Xr];return i instanceof RegExp&&(i.multiline||i.global)});return v(e,n=>({message:"Token Type: ->"+n.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:le.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[n]}))}s(eC,"findUnsupportedFlags");function tC(t){let e=[],r=v(t,a=>me(t,(o,l)=>(a.PATTERN.source===l.PATTERN.source&&!oe(e,l)&&l.PATTERN!==ue.NA&&(e.push(l),o.push(l)),o),[]));r=Lt(r);let n=Ne(r,a=>a.length>1);return v(n,a=>{let o=v(a,u=>u.name);return{message:`The same RegExp pattern ->${we(a).PATTERN}<-has been used in all of the following Token Types: ${o.join(", ")} <-`,type:le.DUPLICATE_PATTERNS_FOUND,tokenTypes:a}})}s(tC,"findDuplicatePatterns");function rC(t){let e=Ne(t,n=>{if(!S(n,"GROUP"))return!1;let i=n.GROUP;return i!==ue.SKIPPED&&i!==ue.NA&&!Ie(i)});return v(e,n=>({message:"Token Type: ->"+n.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:le.INVALID_GROUP_TYPE_FOUND,tokenTypes:[n]}))}s(rC,"findInvalidGroupType");function nC(t,e){let r=Ne(t,i=>i.PUSH_MODE!==void 0&&!oe(e,i.PUSH_MODE));return v(r,i=>({message:`Token Type: ->${i.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${i.PUSH_MODE}<-which does not exist`,type:le.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[i]}))}s(nC,"findModesThatDoNotExist");function iC(t){let e=[],r=me(t,(n,i,a)=>{let o=i.PATTERN;return o===ue.NA||(Ie(o)?n.push({str:o,idx:a,tokenType:i}):dt(o)&&aC(o)&&n.push({str:o.source,idx:a,tokenType:i})),n},[]);return k(t,(n,i)=>{k(r,({str:a,idx:o,tokenType:l})=>{if(i<o&&sC(a,n.PATTERN)){let u=`Token: ->${l.name}<- can never be matched.
Because it appears AFTER the Token Type ->${n.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;e.push({message:u,type:le.UNREACHABLE_PATTERN,tokenTypes:[n,l]})}})}),e}s(iC,"findUnreachablePatterns");function sC(t,e){if(dt(e)){let r=e.exec(t);return r!==null&&r.index===0}else{if(He(e))return e(t,0,[],{});if(S(e,"exec"))return e.exec(t,0,[],{});if(typeof e=="string")return e===t;throw Error("non exhaustive match")}}s(sC,"testTokenType");function aC(t){return Mt([".","\\","[","]","|","^","$","(",")","?","*","+","{"],r=>t.source.indexOf(r)!==-1)===void 0}s(aC,"noMetaChar");function Jm(t){let e=t.ignoreCase?"i":"";return new RegExp(`^(?:${t.source})`,e)}s(Jm,"addStartOfInput");function Zm(t){let e=t.ignoreCase?"iy":"y";return new RegExp(`${t.source}`,e)}s(Zm,"addStickyFlag");function th(t,e,r){let n=[];return S(t,bi)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+bi+`> property in its definition
`,type:le.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),S(t,Mo)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+Mo+`> property in its definition
`,type:le.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),S(t,Mo)&&S(t,bi)&&!S(t.modes,t.defaultMode)&&n.push({message:`A MultiMode Lexer cannot be initialized with a ${bi}: <${t.defaultMode}>which does not exist
`,type:le.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),S(t,Mo)&&k(t.modes,(i,a)=>{k(i,(o,l)=>{if(Ue(o))n.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${a}> at index: <${l}>
`,type:le.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if(S(o,"LONGER_ALT")){let u=N(o.LONGER_ALT)?o.LONGER_ALT:[o.LONGER_ALT];k(u,c=>{!Ue(c)&&!oe(i,c)&&n.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${c.name}> on token <${o.name}> outside of mode <${a}>
`,type:le.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),n}s(th,"performRuntimeChecks");function rh(t,e,r){let n=[],i=!1,a=Lt(ye(q(t.modes))),o=Ir(a,u=>u[Xr]===ue.NA),l=oh(r);return e&&k(o,u=>{let c=ah(u,l);if(c!==!1){let d={message:lC(u,c),type:c.issue,tokenType:u};n.push(d)}else S(u,"LINE_BREAKS")?u.LINE_BREAKS===!0&&(i=!0):Po(l,u.PATTERN)&&(i=!0)}),e&&!i&&n.push({message:`Warning: No LINE_BREAKS Found.
	This Lexer has been defined to track line and column information,
	But none of the Token Types can be identified as matching a line terminator.
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS 
	for details.`,type:le.NO_LINE_BREAKS_FLAGS}),n}s(rh,"performWarningRuntimeChecks");function nh(t){let e={},r=Z(t);return k(r,n=>{let i=t[n];if(N(i))e[n]=[];else throw Error("non exhaustive match")}),e}s(nh,"cloneEmptyGroups");function ih(t){let e=t.PATTERN;if(dt(e))return!1;if(He(e))return!0;if(S(e,"exec"))return!0;if(Ie(e))return!1;throw Error("non exhaustive match")}s(ih,"isCustomPattern");function oC(t){return Ie(t)&&t.length===1?t.charCodeAt(0):!1}s(oC,"isShortPattern");var sh={test:s(function(t){let e=t.length;for(let r=this.lastIndex;r<e;r++){let n=t.charCodeAt(r);if(n===10)return this.lastIndex=r+1,!0;if(n===13)return t.charCodeAt(r+1)===10?this.lastIndex=r+2:this.lastIndex=r+1,!0}return!1},"test"),lastIndex:0};function ah(t,e){if(S(t,"LINE_BREAKS"))return!1;if(dt(t.PATTERN)){try{Po(e,t.PATTERN)}catch(r){return{issue:le.IDENTIFY_TERMINATOR,errMsg:r.message}}return!1}else{if(Ie(t.PATTERN))return!1;if(ih(t))return{issue:le.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}}s(ah,"checkLineBreaksIssues");function lC(t,e){if(e.issue===le.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${t.name}> Token Type
	 Root cause: ${e.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(e.issue===le.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${t.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}s(lC,"buildLineBreakIssueMessage");function oh(t){return v(t,r=>Ie(r)?r.charCodeAt(0):r)}s(oh,"getCharCodes");function Qu(t,e,r){t[e]===void 0?t[e]=[r]:t[e].push(r)}s(Qu,"addToMapOfArrays");var _i=256,Do=[];function Dt(t){return t<_i?t:Do[t]}s(Dt,"charCodeToOptimizedIndex");function uC(){if(D(Do)){Do=new Array(65536);for(let t=0;t<65536;t++)Do[t]=t>255?255+~~(t/255):t}}s(uC,"initCharCodeToOptimizedIndexMap");function Zt(t,e){let r=t.tokenTypeIdx;return r===e.tokenTypeIdx?!0:e.isParent===!0&&e.categoryMatchesMap[r]===!0}s(Zt,"tokenStructuredMatcher");function $i(t,e){return t.tokenTypeIdx===e.tokenTypeIdx}s($i,"tokenStructuredMatcherNoCategories");var lh=1,ch={};function Qt(t){let e=cC(t);fC(e),pC(e),dC(e),k(e,r=>{r.isParent=r.categoryMatches.length>0})}s(Qt,"augmentTokenTypes");function cC(t){let e=ee(t),r=t,n=!0;for(;n;){r=Lt(ye(v(r,a=>a.CATEGORIES)));let i=vr(r,e);e=e.concat(i),D(i)?n=!1:r=i}return e}s(cC,"expandCategories");function fC(t){k(t,e=>{tc(e)||(ch[lh]=e,e.tokenTypeIdx=lh++),uh(e)&&!N(e.CATEGORIES)&&(e.CATEGORIES=[e.CATEGORIES]),uh(e)||(e.CATEGORIES=[]),mC(e)||(e.categoryMatches=[]),hC(e)||(e.categoryMatchesMap={})})}s(fC,"assignTokenDefaultProps");function dC(t){k(t,e=>{e.categoryMatches=[],k(e.categoryMatchesMap,(r,n)=>{e.categoryMatches.push(ch[n].tokenTypeIdx)})})}s(dC,"assignCategoriesTokensProp");function pC(t){k(t,e=>{fh([],e)})}s(pC,"assignCategoriesMapProp");function fh(t,e){k(t,r=>{e.categoryMatchesMap[r.tokenTypeIdx]=!0}),k(e.CATEGORIES,r=>{let n=t.concat(e);oe(n,r)||fh(n,r)})}s(fh,"singleAssignCategoriesToksMap");function tc(t){return S(t,"tokenTypeIdx")}s(tc,"hasShortKeyProperty");function uh(t){return S(t,"CATEGORIES")}s(uh,"hasCategoriesProperty");function mC(t){return S(t,"categoryMatches")}s(mC,"hasExtendingTokensTypesProperty");function hC(t){return S(t,"categoryMatchesMap")}s(hC,"hasExtendingTokensTypesMapProperty");function dh(t){return S(t,"tokenTypeIdx")}s(dh,"isTokenType");var Oi={buildUnableToPopLexerModeMessage(t){return`Unable to pop Lexer Mode after encountering Token ->${t.image}<- The Mode Stack is empty`},buildUnexpectedCharactersMessage(t,e,r,n,i){return`unexpected character: ->${t.charAt(e)}<- at offset: ${e}, skipped ${r} characters.`}};var le;(function(t){t[t.MISSING_PATTERN=0]="MISSING_PATTERN",t[t.INVALID_PATTERN=1]="INVALID_PATTERN",t[t.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",t[t.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",t[t.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",t[t.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",t[t.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",t[t.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",t[t.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",t[t.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",t[t.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",t[t.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",t[t.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",t[t.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",t[t.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",t[t.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",t[t.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",t[t.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(le||(le={}));var $s={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:[`
`,"\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:Oi,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze($s);var ue=class{static{s(this,"Lexer")}constructor(e,r=$s){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(i,a)=>{if(this.traceInitPerf===!0){this.traceInitIndent++;let o=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${o}--> <${i}>`);let{time:l,value:u}=ws(a),c=l>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&c(`${o}<-- <${i}> time: ${l}ms`),this.traceInitIndent--,u}else return a()},typeof r=="boolean")throw Error(`The second argument to the Lexer constructor is now an ILexerConfig Object.
a boolean 2nd argument is no longer supported`);this.config=$e({},$s,r);let n=this.config.traceInitPerf;n===!0?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):typeof n=="number"&&(this.traceInitMaxIdent=n,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let i,a=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===$s.lineTerminatorsPattern)this.config.lineTerminatorsPattern=sh;else if(this.config.lineTerminatorCharacters===$s.lineTerminatorCharacters)throw Error(`Error: Missing <lineTerminatorCharacters> property on the Lexer config.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS`);if(r.safeMode&&r.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),N(e)?i={modes:{defaultMode:ee(e)},defaultMode:bi}:(a=!1,i=ee(e))}),this.config.skipValidations===!1&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(th(i,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(rh(i,this.trackStartLines,this.config.lineTerminatorCharacters))})),i.modes=i.modes?i.modes:{},k(i.modes,(l,u)=>{i.modes[u]=Ir(l,c=>Ue(c))});let o=Z(i.modes);if(k(i.modes,(l,u)=>{this.TRACE_INIT(`Mode: <${u}> processing`,()=>{if(this.modes.push(u),this.config.skipValidations===!1&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(eh(l,o))}),D(this.lexerDefinitionErrors)){Qt(l);let c;this.TRACE_INIT("analyzeTokenTypes",()=>{c=Qm(l,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:r.positionTracking,ensureOptimizations:r.ensureOptimizations,safeMode:r.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[u]=c.patternIdxToConfig,this.charCodeToPatternIdxToConfig[u]=c.charCodeToPatternIdxToConfig,this.emptyGroups=$e({},this.emptyGroups,c.emptyGroups),this.hasCustom=c.hasCustom||this.hasCustom,this.canModeBeOptimized[u]=c.canBeOptimized}})}),this.defaultMode=i.defaultMode,!D(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){let u=v(this.lexerDefinitionErrors,c=>c.message).join(`-----------------------
`);throw new Error(`Errors detected in definition of Lexer:
`+u)}k(this.lexerDefinitionWarning,l=>{Ns(l.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(ec?(this.chopInput=Ze,this.match=this.matchWithTest):(this.updateLastIndex=pe,this.match=this.matchWithExec),a&&(this.handleModes=pe),this.trackStartLines===!1&&(this.computeNewColumn=Ze),this.trackEndLines===!1&&(this.updateTokenEndLineColumnLocation=pe),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{let l=me(this.canModeBeOptimized,(u,c,f)=>(c===!1&&u.push(f),u),[]);if(r.ensureOptimizations&&!D(l))throw Error(`Lexer Modes: < ${l.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{zm()}),this.TRACE_INIT("toFastProperties",()=>{_s(this)})})}tokenize(e,r=this.defaultMode){if(!D(this.lexerDefinitionErrors)){let i=v(this.lexerDefinitionErrors,a=>a.message).join(`-----------------------
`);throw new Error(`Unable to Tokenize because Errors detected in definition of Lexer:
`+i)}return this.tokenizeInternal(e,r)}tokenizeInternal(e,r){let n,i,a,o,l,u,c,f,d,p,m,g,y,A,T,E,R=e,O=R.length,L=0,Se=0,un=this.hasCustom?0:Math.floor(e.length/10),Le=new Array(un),or=[],jt=this.trackStartLines?1:void 0,I=this.trackStartLines?1:void 0,x=nh(this.emptyGroups),w=this.trackStartLines,C=this.config.lineTerminatorsPattern,K=0,M=[],P=[],je=[],We=[];Object.freeze(We);let ie;function _r(){return M}s(_r,"getPossiblePatternsSlow");function wf(Pe){let lt=Dt(Pe),cn=P[lt];return cn===void 0?We:cn}s(wf,"getPossiblePatternsOptimized");let cx=s(Pe=>{if(je.length===1&&Pe.tokenType.PUSH_MODE===void 0){let lt=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(Pe);or.push({offset:Pe.startOffset,line:Pe.startLine,column:Pe.startColumn,length:Pe.image.length,message:lt})}else{je.pop();let lt=Pt(je);M=this.patternIdxToConfig[lt],P=this.charCodeToPatternIdxToConfig[lt],K=M.length;let cn=this.canModeBeOptimized[lt]&&this.config.safeMode===!1;P&&cn?ie=wf:ie=_r}},"pop_mode");function _f(Pe){je.push(Pe),P=this.charCodeToPatternIdxToConfig[Pe],M=this.patternIdxToConfig[Pe],K=M.length,K=M.length;let lt=this.canModeBeOptimized[Pe]&&this.config.safeMode===!1;P&&lt?ie=wf:ie=_r}s(_f,"push_mode"),_f.call(this,r);let mt,bf=this.config.recoveryEnabled;for(;L<O;){u=null;let Pe=R.charCodeAt(L),lt=ie(Pe),cn=lt.length;for(n=0;n<cn;n++){mt=lt[n];let nt=mt.pattern;c=null;let Wt=mt.short;if(Wt!==!1?Pe===Wt&&(u=nt):mt.isCustom===!0?(E=nt.exec(R,L,Le,x),E!==null?(u=E[0],E.payload!==void 0&&(c=E.payload)):u=null):(this.updateLastIndex(nt,L),u=this.match(nt,e,L)),u!==null){if(l=mt.longerAlt,l!==void 0){let lr=l.length;for(a=0;a<lr;a++){let Kt=M[l[a]],br=Kt.pattern;if(f=null,Kt.isCustom===!0?(E=br.exec(R,L,Le,x),E!==null?(o=E[0],E.payload!==void 0&&(f=E.payload)):o=null):(this.updateLastIndex(br,L),o=this.match(br,e,L)),o&&o.length>u.length){u=o,c=f,mt=Kt;break}}}break}}if(u!==null){if(d=u.length,p=mt.group,p!==void 0&&(m=mt.tokenTypeIdx,g=this.createTokenInstance(u,L,m,mt.tokenType,jt,I,d),this.handlePayload(g,c),p===!1?Se=this.addToken(Le,Se,g):x[p].push(g)),e=this.chopInput(e,d),L=L+d,I=this.computeNewColumn(I,d),w===!0&&mt.canLineTerminator===!0){let nt=0,Wt,lr;C.lastIndex=0;do Wt=C.test(u),Wt===!0&&(lr=C.lastIndex-1,nt++);while(Wt===!0);nt!==0&&(jt=jt+nt,I=d-lr,this.updateTokenEndLineColumnLocation(g,p,lr,nt,jt,I,d))}this.handleModes(mt,cx,_f,g)}else{let nt=L,Wt=jt,lr=I,Kt=bf===!1;for(;Kt===!1&&L<O;)for(e=this.chopInput(e,1),L++,i=0;i<K;i++){let br=M[i],zl=br.pattern,$f=br.short;if($f!==!1?R.charCodeAt(L)===$f&&(Kt=!0):br.isCustom===!0?Kt=zl.exec(R,L,Le,x)!==null:(this.updateLastIndex(zl,L),Kt=zl.exec(e)!==null),Kt===!0)break}if(y=L-nt,I=this.computeNewColumn(I,y),T=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(R,nt,y,Wt,lr),or.push({offset:nt,line:Wt,column:lr,length:y,message:T}),bf===!1)break}}return this.hasCustom||(Le.length=Se),{tokens:Le,groups:x,errors:or}}handleModes(e,r,n,i){if(e.pop===!0){let a=e.push;r(i),a!==void 0&&n.call(this,a)}else e.push!==void 0&&n.call(this,e.push)}chopInput(e,r){return e.substring(r)}updateLastIndex(e,r){e.lastIndex=r}updateTokenEndLineColumnLocation(e,r,n,i,a,o,l){let u,c;r!==void 0&&(u=n===l-1,c=u?-1:0,i===1&&u===!0||(e.endLine=a+c,e.endColumn=o-1+-c))}computeNewColumn(e,r){return e+r}createOffsetOnlyToken(e,r,n,i){return{image:e,startOffset:r,tokenTypeIdx:n,tokenType:i}}createStartOnlyToken(e,r,n,i,a,o){return{image:e,startOffset:r,startLine:a,startColumn:o,tokenTypeIdx:n,tokenType:i}}createFullToken(e,r,n,i,a,o,l){return{image:e,startOffset:r,endOffset:r+l-1,startLine:a,endLine:a,startColumn:o,endColumn:o+l-1,tokenTypeIdx:n,tokenType:i}}addTokenUsingPush(e,r,n){return e.push(n),r}addTokenUsingMemberAccess(e,r,n){return e[r]=n,r++,r}handlePayloadNoCustom(e,r){}handlePayloadWithCustom(e,r){r!==null&&(e.payload=r)}matchWithTest(e,r,n){return e.test(r)===!0?r.substring(n,e.lastIndex):null}matchWithExec(e,r){let n=e.exec(r);return n!==null?n[0]:null}};ue.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.";ue.NA=/NOT_APPLICABLE/;function er(t){return rc(t)?t.LABEL:t.name}s(er,"tokenLabel");function rc(t){return Ie(t.LABEL)&&t.LABEL!==""}s(rc,"hasTokenLabel");var gC="parent",ph="categories",mh="label",hh="group",gh="push_mode",yh="pop_mode",xh="longer_alt",Th="line_breaks",Rh="start_chars_hint";function kr(t){return yC(t)}s(kr,"createToken");function yC(t){let e=t.pattern,r={};if(r.name=t.name,Ue(e)||(r.PATTERN=e),S(t,gC))throw`The parent property is no longer supported.
See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.`;return S(t,ph)&&(r.CATEGORIES=t[ph]),Qt([r]),S(t,mh)&&(r.LABEL=t[mh]),S(t,hh)&&(r.GROUP=t[hh]),S(t,yh)&&(r.POP_MODE=t[yh]),S(t,gh)&&(r.PUSH_MODE=t[gh]),S(t,xh)&&(r.LONGER_ALT=t[xh]),S(t,Th)&&(r.LINE_BREAKS=t[Th]),S(t,Rh)&&(r.START_CHARS_HINT=t[Rh]),r}s(yC,"createTokenInternal");var ot=kr({name:"EOF",pattern:ue.NA});Qt([ot]);function tr(t,e,r,n,i,a,o,l){return{image:e,startOffset:r,endOffset:n,startLine:i,endLine:a,startColumn:o,endColumn:l,tokenTypeIdx:t.tokenTypeIdx,tokenType:t}}s(tr,"createTokenInstance");function Os(t,e){return Zt(t,e)}s(Os,"tokenMatcher");var rr={buildMismatchTokenMessage({expected:t,actual:e,previous:r,ruleName:n}){return`Expecting ${rc(t)?`--> ${er(t)} <--`:`token of type --> ${t.name} <--`} but found --> '${e.image}' <--`},buildNotAllInputParsedMessage({firstRedundant:t,ruleName:e}){return"Redundant input, expecting EOF but found: "+t.image},buildNoViableAltMessage({expectedPathsPerAlt:t,actual:e,previous:r,customUserDescription:n,ruleName:i}){let a="Expecting: ",l=`
but found: '`+we(e).image+"'";if(n)return a+n+l;{let u=me(t,(p,m)=>p.concat(m),[]),c=v(u,p=>`[${v(p,m=>er(m)).join(", ")}]`),d=`one of these possible Token sequences:
${v(c,(p,m)=>`  ${m+1}. ${p}`).join(`
`)}`;return a+d+l}},buildEarlyExitMessage({expectedIterationPaths:t,actual:e,customUserDescription:r,ruleName:n}){let i="Expecting: ",o=`
but found: '`+we(e).image+"'";if(r)return i+r+o;{let u=`expecting at least one iteration which starts with one of these possible Token sequences::
  <${v(t,c=>`[${v(c,f=>er(f)).join(",")}]`).join(" ,")}>`;return i+u+o}}};Object.freeze(rr);var Ah={buildRuleNotFoundError(t,e){return"Invalid grammar, reference to a rule which is not defined: ->"+e.nonTerminalName+`<-
inside top level rule: ->`+t.name+"<-"}},Tt={buildDuplicateFoundError(t,e){function r(f){return f instanceof F?f.terminalType.name:f instanceof V?f.nonTerminalName:""}s(r,"getExtraProductionArgument");let n=t.name,i=we(e),a=i.idx,o=rt(i),l=r(i),u=a>0,c=`->${o}${u?a:""}<- ${l?`with argument: ->${l}<-`:""}
                  appears more than once (${e.length} times) in the top level rule: ->${n}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return c=c.replace(/[ \t]+/g," "),c=c.replace(/\s\s+/g,`
`),c},buildNamespaceConflictError(t){return`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${t.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`},buildAlternationPrefixAmbiguityError(t){let e=v(t.prefixPath,i=>er(i)).join(", "),r=t.alternation.idx===0?"":t.alternation.idx;return`Ambiguous alternatives: <${t.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${r}> inside <${t.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(t){let e=v(t.prefixPath,i=>er(i)).join(", "),r=t.alternation.idx===0?"":t.alternation.idx,n=`Ambiguous Alternatives Detected: <${t.ambiguityIndices.join(" ,")}> in <OR${r}> inside <${t.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return n=n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,n},buildEmptyRepetitionError(t){let e=rt(t.repetition);return t.repetition.idx!==0&&(e+=t.repetition.idx),`The repetition <${e}> within Rule <${t.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError(t){return"deprecated"},buildEmptyAlternationError(t){return`Ambiguous empty alternative: <${t.emptyChoiceIdx+1}> in <OR${t.alternation.idx}> inside <${t.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`},buildTooManyAlternativesError(t){return`An Alternation cannot have more than 256 alternatives:
<OR${t.alternation.idx}> inside <${t.topLevelRule.name}> Rule.
 has ${t.alternation.definition.length+1} alternatives.`},buildLeftRecursionError(t){let e=t.topLevelRule.name,r=v(t.leftRecursionPath,a=>a.name),n=`${e} --> ${r.concat([e]).join(" --> ")}`;return`Left Recursion found in grammar.
rule: <${e}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${n}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError(t){return"deprecated"},buildDuplicateRuleNameError(t){let e;return t.topLevelRule instanceof Ve?e=t.topLevelRule.name:e=t.topLevelRule,`Duplicate definition, rule: ->${e}<- is already defined in the grammar: ->${t.grammarName}<-`}};function Eh(t,e){let r=new nc(t,e);return r.resolveRefs(),r.errors}s(Eh,"resolveGrammar");var nc=class extends ze{static{s(this,"GastRefResolverVisitor")}constructor(e,r){super(),this.nameToTopRule=e,this.errMsgProvider=r,this.errors=[]}resolveRefs(){k(q(this.nameToTopRule),e=>{this.currTopLevel=e,e.accept(this)})}visitNonTerminal(e){let r=this.nameToTopRule[e.nonTerminalName];if(r)e.referencedRule=r;else{let n=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:n,type:ke.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}};var ic=class extends Jt{static{s(this,"AbstractNextPossibleTokensWalker")}constructor(e,r){super(),this.topProd=e,this.path=r,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=ee(this.path.ruleStack).reverse(),this.occurrenceStack=ee(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,r=[]){this.found||super.walk(e,r)}walkProdRef(e,r,n){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){let i=r.concat(n);this.updateExpectedNext(),this.walk(e.referencedRule,i)}}updateExpectedNext(){D(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}},Fo=class extends ic{static{s(this,"NextAfterTokenWalker")}constructor(e,r){super(e,r),this.path=r,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,r,n){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){let i=r.concat(n),a=new te({definition:i});this.possibleTokTypes=qr(a),this.found=!0}}},Li=class extends Jt{static{s(this,"AbstractNextTerminalAfterProductionWalker")}constructor(e,r){super(),this.topRule=e,this.occurrence=r,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}},Go=class extends Li{static{s(this,"NextTerminalAfterManyWalker")}walkMany(e,r,n){if(e.idx===this.occurrence){let i=we(r.concat(n));this.result.isEndOfRule=i===void 0,i instanceof F&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkMany(e,r,n)}},Ls=class extends Li{static{s(this,"NextTerminalAfterManySepWalker")}walkManySep(e,r,n){if(e.idx===this.occurrence){let i=we(r.concat(n));this.result.isEndOfRule=i===void 0,i instanceof F&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkManySep(e,r,n)}},Uo=class extends Li{static{s(this,"NextTerminalAfterAtLeastOneWalker")}walkAtLeastOne(e,r,n){if(e.idx===this.occurrence){let i=we(r.concat(n));this.result.isEndOfRule=i===void 0,i instanceof F&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOne(e,r,n)}},Ps=class extends Li{static{s(this,"NextTerminalAfterAtLeastOneSepWalker")}walkAtLeastOneSep(e,r,n){if(e.idx===this.occurrence){let i=we(r.concat(n));this.result.isEndOfRule=i===void 0,i instanceof F&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOneSep(e,r,n)}};function Bo(t,e,r=[]){r=ee(r);let n=[],i=0;function a(l){return l.concat(xe(t,i+1))}s(a,"remainingPathWith");function o(l){let u=Bo(a(l),e,r);return n.concat(u)}for(s(o,"getAlternativesForProd");r.length<e&&i<t.length;){let l=t[i];if(l instanceof te)return o(l.definition);if(l instanceof V)return o(l.definition);if(l instanceof z)n=o(l.definition);else if(l instanceof re){let u=l.definition.concat([new G({definition:l.definition})]);return o(u)}else if(l instanceof ne){let u=[new te({definition:l.definition}),new G({definition:[new F({terminalType:l.separator})].concat(l.definition)})];return o(u)}else if(l instanceof X){let u=l.definition.concat([new G({definition:[new F({terminalType:l.separator})].concat(l.definition)})]);n=o(u)}else if(l instanceof G){let u=l.definition.concat([new G({definition:l.definition})]);n=o(u)}else{if(l instanceof Y)return k(l.definition,u=>{D(u.definition)===!1&&(n=o(u.definition))}),n;if(l instanceof F)r.push(l.terminalType);else throw Error("non exhaustive match")}i++}return n.push({partialPath:r,suffixDef:xe(t,i)}),n}s(Bo,"possiblePathsFrom");function jo(t,e,r,n){let i="EXIT_NONE_TERMINAL",a=[i],o="EXIT_ALTERNATIVE",l=!1,u=e.length,c=u-n-1,f=[],d=[];for(d.push({idx:-1,def:t,ruleStack:[],occurrenceStack:[]});!D(d);){let p=d.pop();if(p===o){l&&Pt(d).idx<=c&&d.pop();continue}let m=p.def,g=p.idx,y=p.ruleStack,A=p.occurrenceStack;if(D(m))continue;let T=m[0];if(T===i){let E={idx:g,def:xe(m),ruleStack:Yt(y),occurrenceStack:Yt(A)};d.push(E)}else if(T instanceof F)if(g<u-1){let E=g+1,R=e[E];if(r(R,T.terminalType)){let O={idx:E,def:xe(m),ruleStack:y,occurrenceStack:A};d.push(O)}}else if(g===u-1)f.push({nextTokenType:T.terminalType,nextTokenOccurrence:T.idx,ruleStack:y,occurrenceStack:A}),l=!0;else throw Error("non exhaustive match");else if(T instanceof V){let E=ee(y);E.push(T.nonTerminalName);let R=ee(A);R.push(T.idx);let O={idx:g,def:T.definition.concat(a,xe(m)),ruleStack:E,occurrenceStack:R};d.push(O)}else if(T instanceof z){let E={idx:g,def:xe(m),ruleStack:y,occurrenceStack:A};d.push(E),d.push(o);let R={idx:g,def:T.definition.concat(xe(m)),ruleStack:y,occurrenceStack:A};d.push(R)}else if(T instanceof re){let E=new G({definition:T.definition,idx:T.idx}),R=T.definition.concat([E],xe(m)),O={idx:g,def:R,ruleStack:y,occurrenceStack:A};d.push(O)}else if(T instanceof ne){let E=new F({terminalType:T.separator}),R=new G({definition:[E].concat(T.definition),idx:T.idx}),O=T.definition.concat([R],xe(m)),L={idx:g,def:O,ruleStack:y,occurrenceStack:A};d.push(L)}else if(T instanceof X){let E={idx:g,def:xe(m),ruleStack:y,occurrenceStack:A};d.push(E),d.push(o);let R=new F({terminalType:T.separator}),O=new G({definition:[R].concat(T.definition),idx:T.idx}),L=T.definition.concat([O],xe(m)),Se={idx:g,def:L,ruleStack:y,occurrenceStack:A};d.push(Se)}else if(T instanceof G){let E={idx:g,def:xe(m),ruleStack:y,occurrenceStack:A};d.push(E),d.push(o);let R=new G({definition:T.definition,idx:T.idx}),O=T.definition.concat([R],xe(m)),L={idx:g,def:O,ruleStack:y,occurrenceStack:A};d.push(L)}else if(T instanceof Y)for(let E=T.definition.length-1;E>=0;E--){let R=T.definition[E],O={idx:g,def:R.definition.concat(xe(m)),ruleStack:y,occurrenceStack:A};d.push(O),d.push(o)}else if(T instanceof te)d.push({idx:g,def:T.definition.concat(xe(m)),ruleStack:y,occurrenceStack:A});else if(T instanceof Ve)d.push(xC(T,g,y,A));else throw Error("non exhaustive match")}return f}s(jo,"nextPossibleTokensAfter");function xC(t,e,r,n){let i=ee(r);i.push(t.name);let a=ee(n);return a.push(1),{idx:e,def:t.definition,ruleStack:i,occurrenceStack:a}}s(xC,"expandTopLevelRule");var ce;(function(t){t[t.OPTION=0]="OPTION",t[t.REPETITION=1]="REPETITION",t[t.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",t[t.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",t[t.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",t[t.ALTERNATION=5]="ALTERNATION"})(ce||(ce={}));function Ms(t){if(t instanceof z||t==="Option")return ce.OPTION;if(t instanceof G||t==="Repetition")return ce.REPETITION;if(t instanceof re||t==="RepetitionMandatory")return ce.REPETITION_MANDATORY;if(t instanceof ne||t==="RepetitionMandatoryWithSeparator")return ce.REPETITION_MANDATORY_WITH_SEPARATOR;if(t instanceof X||t==="RepetitionWithSeparator")return ce.REPETITION_WITH_SEPARATOR;if(t instanceof Y||t==="Alternation")return ce.ALTERNATION;throw Error("non exhaustive match")}s(Ms,"getProdType");function Ko(t){let{occurrence:e,rule:r,prodType:n,maxLookahead:i}=t,a=Ms(n);return a===ce.ALTERNATION?Pi(e,r,i):Mi(e,r,a,i)}s(Ko,"getLookaheadPaths");function Ih(t,e,r,n,i,a){let o=Pi(t,e,r),l=_h(o)?$i:Zt;return a(o,n,l,i)}s(Ih,"buildLookaheadFuncForOr");function kh(t,e,r,n,i,a){let o=Mi(t,e,i,r),l=_h(o)?$i:Zt;return a(o[0],l,n)}s(kh,"buildLookaheadFuncForOptionalProd");function Sh(t,e,r,n){let i=t.length,a=Ge(t,o=>Ge(o,l=>l.length===1));if(e)return function(o){let l=v(o,u=>u.GATE);for(let u=0;u<i;u++){let c=t[u],f=c.length,d=l[u];if(!(d!==void 0&&d.call(this)===!1))e:for(let p=0;p<f;p++){let m=c[p],g=m.length;for(let y=0;y<g;y++){let A=this.LA(y+1);if(r(A,m[y])===!1)continue e}return u}}};if(a&&!n){let o=v(t,u=>ye(u)),l=me(o,(u,c,f)=>(k(c,d=>{S(u,d.tokenTypeIdx)||(u[d.tokenTypeIdx]=f),k(d.categoryMatches,p=>{S(u,p)||(u[p]=f)})}),u),{});return function(){let u=this.LA(1);return l[u.tokenTypeIdx]}}else return function(){for(let o=0;o<i;o++){let l=t[o],u=l.length;e:for(let c=0;c<u;c++){let f=l[c],d=f.length;for(let p=0;p<d;p++){let m=this.LA(p+1);if(r(m,f[p])===!1)continue e}return o}}}}s(Sh,"buildAlternativesLookAheadFunc");function Ch(t,e,r){let n=Ge(t,a=>a.length===1),i=t.length;if(n&&!r){let a=ye(t);if(a.length===1&&D(a[0].categoryMatches)){let l=a[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===l}}else{let o=me(a,(l,u,c)=>(l[u.tokenTypeIdx]=!0,k(u.categoryMatches,f=>{l[f]=!0}),l),[]);return function(){let l=this.LA(1);return o[l.tokenTypeIdx]===!0}}}else return function(){e:for(let a=0;a<i;a++){let o=t[a],l=o.length;for(let u=0;u<l;u++){let c=this.LA(u+1);if(e(c,o[u])===!1)continue e}return!0}return!1}}s(Ch,"buildSingleAlternativeLookaheadFunction");var ac=class extends Jt{static{s(this,"RestDefinitionFinderWalker")}constructor(e,r,n){super(),this.topProd=e,this.targetOccurrence=r,this.targetProdType=n}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,r,n,i){return e.idx===this.targetOccurrence&&this.targetProdType===r?(this.restDef=n.concat(i),!0):!1}walkOption(e,r,n){this.checkIsTarget(e,ce.OPTION,r,n)||super.walkOption(e,r,n)}walkAtLeastOne(e,r,n){this.checkIsTarget(e,ce.REPETITION_MANDATORY,r,n)||super.walkOption(e,r,n)}walkAtLeastOneSep(e,r,n){this.checkIsTarget(e,ce.REPETITION_MANDATORY_WITH_SEPARATOR,r,n)||super.walkOption(e,r,n)}walkMany(e,r,n){this.checkIsTarget(e,ce.REPETITION,r,n)||super.walkOption(e,r,n)}walkManySep(e,r,n){this.checkIsTarget(e,ce.REPETITION_WITH_SEPARATOR,r,n)||super.walkOption(e,r,n)}},Wo=class extends ze{static{s(this,"InsideDefinitionFinderVisitor")}constructor(e,r,n){super(),this.targetOccurrence=e,this.targetProdType=r,this.targetRef=n,this.result=[]}checkIsTarget(e,r){e.idx===this.targetOccurrence&&this.targetProdType===r&&(this.targetRef===void 0||e===this.targetRef)&&(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,ce.OPTION)}visitRepetition(e){this.checkIsTarget(e,ce.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,ce.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,ce.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,ce.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,ce.ALTERNATION)}};function vh(t){let e=new Array(t);for(let r=0;r<t;r++)e[r]=[];return e}s(vh,"initializeArrayOfArrays");function sc(t){let e=[""];for(let r=0;r<t.length;r++){let n=t[r],i=[];for(let a=0;a<e.length;a++){let o=e[a];i.push(o+"_"+n.tokenTypeIdx);for(let l=0;l<n.categoryMatches.length;l++){let u="_"+n.categoryMatches[l];i.push(o+u)}}e=i}return e}s(sc,"pathToHashKeys");function TC(t,e,r){for(let n=0;n<t.length;n++){if(n===r)continue;let i=t[n];for(let a=0;a<e.length;a++){let o=e[a];if(i[o]===!0)return!1}}return!0}s(TC,"isUniquePrefixHash");function Nh(t,e){let r=v(t,o=>Bo([o],1)),n=vh(r.length),i=v(r,o=>{let l={};return k(o,u=>{let c=sc(u.partialPath);k(c,f=>{l[f]=!0})}),l}),a=r;for(let o=1;o<=e;o++){let l=a;a=vh(l.length);for(let u=0;u<l.length;u++){let c=l[u];for(let f=0;f<c.length;f++){let d=c[f].partialPath,p=c[f].suffixDef,m=sc(d);if(TC(i,m,u)||D(p)||d.length===e){let y=n[u];if(Ho(y,d)===!1){y.push(d);for(let A=0;A<m.length;A++){let T=m[A];i[u][T]=!0}}}else{let y=Bo(p,o+1,d);a[u]=a[u].concat(y),k(y,A=>{let T=sc(A.partialPath);k(T,E=>{i[u][E]=!0})})}}}}return n}s(Nh,"lookAheadSequenceFromAlternatives");function Pi(t,e,r,n){let i=new Wo(t,ce.ALTERNATION,n);return e.accept(i),Nh(i.result,r)}s(Pi,"getLookaheadPathsForOr");function Mi(t,e,r,n){let i=new Wo(t,r);e.accept(i);let a=i.result,l=new ac(e,t,r).startWalking(),u=new te({definition:a}),c=new te({definition:l});return Nh([u,c],n)}s(Mi,"getLookaheadPathsForOptionalProd");function Ho(t,e){e:for(let r=0;r<t.length;r++){let n=t[r];if(n.length===e.length){for(let i=0;i<n.length;i++){let a=e[i],o=n[i];if((a===o||o.categoryMatchesMap[a.tokenTypeIdx]!==void 0)===!1)continue e}return!0}}return!1}s(Ho,"containsPath");function wh(t,e){return t.length<e.length&&Ge(t,(r,n)=>{let i=e[n];return r===i||i.categoryMatchesMap[r.tokenTypeIdx]})}s(wh,"isStrictPrefixOfPath");function _h(t){return Ge(t,e=>Ge(e,r=>Ge(r,n=>D(n.categoryMatches))))}s(_h,"areTokenCategoriesNotUsed");function bh(t){let e=t.lookaheadStrategy.validate({rules:t.rules,tokenTypes:t.tokenTypes,grammarName:t.grammarName});return v(e,r=>Object.assign({type:ke.CUSTOM_LOOKAHEAD_VALIDATION},r))}s(bh,"validateLookahead");function $h(t,e,r,n){let i=Oe(t,u=>RC(u,r)),a=SC(t,e,r),o=Oe(t,u=>vC(u,r)),l=Oe(t,u=>EC(u,t,n,r));return i.concat(a,o,l)}s($h,"validateGrammar");function RC(t,e){let r=new oc;t.accept(r);let n=r.allProductions,i=Vu(n,AC),a=tt(i,l=>l.length>1);return v(q(a),l=>{let u=we(l),c=e.buildDuplicateFoundError(t,l),f=rt(u),d={message:c,type:ke.DUPLICATE_PRODUCTIONS,ruleName:t.name,dslName:f,occurrence:u.idx},p=Oh(u);return p&&(d.parameter=p),d})}s(RC,"validateDuplicateProductions");function AC(t){return`${rt(t)}_#_${t.idx}_#_${Oh(t)}`}s(AC,"identifyProductionForDuplicates");function Oh(t){return t instanceof F?t.terminalType.name:t instanceof V?t.nonTerminalName:""}s(Oh,"getExtraProductionArgument");var oc=class extends ze{static{s(this,"OccurrenceValidationCollector")}constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}};function EC(t,e,r,n){let i=[];if(me(e,(o,l)=>l.name===t.name?o+1:o,0)>1){let o=n.buildDuplicateRuleNameError({topLevelRule:t,grammarName:r});i.push({message:o,type:ke.DUPLICATE_RULE_NAME,ruleName:t.name})}return i}s(EC,"validateRuleDoesNotAlreadyExist");function Lh(t,e,r){let n=[],i;return oe(e,t)||(i=`Invalid rule override, rule: ->${t}<- cannot be overridden in the grammar: ->${r}<-as it is not defined in any of the super grammars `,n.push({message:i,type:ke.INVALID_RULE_OVERRIDE,ruleName:t})),n}s(Lh,"validateRuleIsOverridden");function uc(t,e,r,n=[]){let i=[],a=Vo(e.definition);if(D(a))return[];{let o=t.name;oe(a,t)&&i.push({message:r.buildLeftRecursionError({topLevelRule:t,leftRecursionPath:n}),type:ke.LEFT_RECURSION,ruleName:o});let u=vr(a,n.concat([t])),c=Oe(u,f=>{let d=ee(n);return d.push(f),uc(t,f,r,d)});return i.concat(c)}}s(uc,"validateNoLeftRecursion");function Vo(t){let e=[];if(D(t))return e;let r=we(t);if(r instanceof V)e.push(r.referencedRule);else if(r instanceof te||r instanceof z||r instanceof re||r instanceof ne||r instanceof X||r instanceof G)e=e.concat(Vo(r.definition));else if(r instanceof Y)e=ye(v(r.definition,a=>Vo(a.definition)));else if(!(r instanceof F))throw Error("non exhaustive match");let n=zr(r),i=t.length>1;if(n&&i){let a=xe(t);return e.concat(Vo(a))}else return e}s(Vo,"getFirstNoneTerminal");var Ds=class extends ze{static{s(this,"OrCollector")}constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}};function Ph(t,e){let r=new Ds;t.accept(r);let n=r.alternations;return Oe(n,a=>{let o=Yt(a.definition);return Oe(o,(l,u)=>{let c=jo([l],[],Zt,1);return D(c)?[{message:e.buildEmptyAlternationError({topLevelRule:t,alternation:a,emptyChoiceIdx:u}),type:ke.NONE_LAST_EMPTY_ALT,ruleName:t.name,occurrence:a.idx,alternative:u+1}]:[]})})}s(Ph,"validateEmptyOrAlternative");function Mh(t,e,r){let n=new Ds;t.accept(n);let i=n.alternations;return i=Ir(i,o=>o.ignoreAmbiguities===!0),Oe(i,o=>{let l=o.idx,u=o.maxLookahead||e,c=Pi(l,t,u,o),f=IC(c,o,t,r),d=kC(c,o,t,r);return f.concat(d)})}s(Mh,"validateAmbiguousAlternationAlternatives");var lc=class extends ze{static{s(this,"RepetitionCollector")}constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}};function vC(t,e){let r=new Ds;t.accept(r);let n=r.alternations;return Oe(n,a=>a.definition.length>255?[{message:e.buildTooManyAlternativesError({topLevelRule:t,alternation:a}),type:ke.TOO_MANY_ALTS,ruleName:t.name,occurrence:a.idx}]:[])}s(vC,"validateTooManyAlts");function Dh(t,e,r){let n=[];return k(t,i=>{let a=new lc;i.accept(a);let o=a.allProductions;k(o,l=>{let u=Ms(l),c=l.maxLookahead||e,f=l.idx,p=Mi(f,i,u,c)[0];if(D(ye(p))){let m=r.buildEmptyRepetitionError({topLevelRule:i,repetition:l});n.push({message:m,type:ke.NO_NON_EMPTY_LOOKAHEAD,ruleName:i.name})}})}),n}s(Dh,"validateSomeNonEmptyLookaheadPath");function IC(t,e,r,n){let i=[],a=me(t,(l,u,c)=>(e.definition[c].ignoreAmbiguities===!0||k(u,f=>{let d=[c];k(t,(p,m)=>{c!==m&&Ho(p,f)&&e.definition[m].ignoreAmbiguities!==!0&&d.push(m)}),d.length>1&&!Ho(i,f)&&(i.push(f),l.push({alts:d,path:f}))}),l),[]);return v(a,l=>{let u=v(l.alts,f=>f+1);return{message:n.buildAlternationAmbiguityError({topLevelRule:r,alternation:e,ambiguityIndices:u,prefixPath:l.path}),type:ke.AMBIGUOUS_ALTS,ruleName:r.name,occurrence:e.idx,alternatives:l.alts}})}s(IC,"checkAlternativesAmbiguities");function kC(t,e,r,n){let i=me(t,(o,l,u)=>{let c=v(l,f=>({idx:u,path:f}));return o.concat(c)},[]);return Lt(Oe(i,o=>{if(e.definition[o.idx].ignoreAmbiguities===!0)return[];let u=o.idx,c=o.path,f=Ne(i,p=>e.definition[p.idx].ignoreAmbiguities!==!0&&p.idx<u&&wh(p.path,c));return v(f,p=>{let m=[p.idx+1,u+1],g=e.idx===0?"":e.idx;return{message:n.buildAlternationPrefixAmbiguityError({topLevelRule:r,alternation:e,ambiguityIndices:m,prefixPath:p.path}),type:ke.AMBIGUOUS_PREFIX_ALTS,ruleName:r.name,occurrence:g,alternatives:m}})}))}s(kC,"checkPrefixAlternativesAmbiguities");function SC(t,e,r){let n=[],i=v(e,a=>a.name);return k(t,a=>{let o=a.name;if(oe(i,o)){let l=r.buildNamespaceConflictError(a);n.push({message:l,type:ke.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:o})}}),n}s(SC,"checkTerminalAndNoneTerminalsNameSpace");function Fh(t){let e=ki(t,{errMsgProvider:Ah}),r={};return k(t.rules,n=>{r[n.name]=n}),Eh(r,e.errMsgProvider)}s(Fh,"resolveGrammar");function Gh(t){return t=ki(t,{errMsgProvider:Tt}),$h(t.rules,t.tokenTypes,t.errMsgProvider,t.grammarName)}s(Gh,"validateGrammar");var Uh="MismatchedTokenException",Bh="NoViableAltException",jh="EarlyExitException",Wh="NotAllInputParsedException",Kh=[Uh,Bh,jh,Wh];Object.freeze(Kh);function Sr(t){return oe(Kh,t.name)}s(Sr,"isRecognitionException");var Di=class extends Error{static{s(this,"RecognitionException")}constructor(e,r){super(e),this.token=r,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}},Yr=class extends Di{static{s(this,"MismatchedTokenException")}constructor(e,r,n){super(e,r),this.previousToken=n,this.name=Uh}},Fs=class extends Di{static{s(this,"NoViableAltException")}constructor(e,r,n){super(e,r),this.previousToken=n,this.name=Bh}},Gs=class extends Di{static{s(this,"NotAllInputParsedException")}constructor(e,r){super(e,r),this.name=Wh}},Us=class extends Di{static{s(this,"EarlyExitException")}constructor(e,r,n){super(e,r),this.previousToken=n,this.name=jh}};var cc={},dc="InRuleRecoveryException",fc=class extends Error{static{s(this,"InRuleRecoveryException")}constructor(e){super(e),this.name=dc}},zo=class{static{s(this,"Recoverable")}initRecoverable(e){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=S(e,"recoveryEnabled")?e.recoveryEnabled:qe.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=CC)}getTokenToInsert(e){let r=tr(e,"",NaN,NaN,NaN,NaN,NaN,NaN);return r.isInsertedInRecovery=!0,r}canTokenTypeBeInsertedInRecovery(e){return!0}canTokenTypeBeDeletedInRecovery(e){return!0}tryInRepetitionRecovery(e,r,n,i){let a=this.findReSyncTokenType(),o=this.exportLexerState(),l=[],u=!1,c=this.LA(1),f=this.LA(1),d=s(()=>{let p=this.LA(0),m=this.errorMessageProvider.buildMismatchTokenMessage({expected:i,actual:c,previous:p,ruleName:this.getCurrRuleFullName()}),g=new Yr(m,c,this.LA(0));g.resyncedTokens=Yt(l),this.SAVE_ERROR(g)},"generateErrorMessage");for(;!u;)if(this.tokenMatcher(f,i)){d();return}else if(n.call(this)){d(),e.apply(this,r);return}else this.tokenMatcher(f,a)?u=!0:(f=this.SKIP_TOKEN(),this.addToResyncTokens(f,l));this.importLexerState(o)}shouldInRepetitionRecoveryBeTried(e,r,n){return!(n===!1||this.tokenMatcher(this.LA(1),e)||this.isBackTracking()||this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,r)))}getFollowsForInRuleRecovery(e,r){let n=this.getCurrentGrammarPath(e,r);return this.getNextPossibleTokenTypes(n)}tryInRuleRecovery(e,r){if(this.canRecoverWithSingleTokenInsertion(e,r))return this.getTokenToInsert(e);if(this.canRecoverWithSingleTokenDeletion(e)){let n=this.SKIP_TOKEN();return this.consumeToken(),n}throw new fc("sad sad panda")}canPerformInRuleRecovery(e,r){return this.canRecoverWithSingleTokenInsertion(e,r)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,r){if(!this.canTokenTypeBeInsertedInRecovery(e)||D(r))return!1;let n=this.LA(1);return Mt(r,a=>this.tokenMatcher(n,a))!==void 0}canRecoverWithSingleTokenDeletion(e){return this.canTokenTypeBeDeletedInRecovery(e)?this.tokenMatcher(this.LA(2),e):!1}isInCurrentRuleReSyncSet(e){let r=this.getCurrFollowKey(),n=this.getFollowSetFromFollowKey(r);return oe(n,e)}findReSyncTokenType(){let e=this.flattenFollowSet(),r=this.LA(1),n=2;for(;;){let i=Mt(e,a=>Os(r,a));if(i!==void 0)return i;r=this.LA(n),n++}}getCurrFollowKey(){if(this.RULE_STACK.length===1)return cc;let e=this.getLastExplicitRuleShortName(),r=this.getLastExplicitRuleOccurrenceIndex(),n=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:r,inRule:this.shortRuleNameToFullName(n)}}buildFullFollowKeyStack(){let e=this.RULE_STACK,r=this.RULE_OCCURRENCE_STACK;return v(e,(n,i)=>i===0?cc:{ruleName:this.shortRuleNameToFullName(n),idxInCallingRule:r[i],inRule:this.shortRuleNameToFullName(e[i-1])})}flattenFollowSet(){let e=v(this.buildFullFollowKeyStack(),r=>this.getFollowSetFromFollowKey(r));return ye(e)}getFollowSetFromFollowKey(e){if(e===cc)return[ot];let r=e.ruleName+e.idxInCallingRule+$o+e.inRule;return this.resyncFollows[r]}addToResyncTokens(e,r){return this.tokenMatcher(e,ot)||r.push(e),r}reSyncTo(e){let r=[],n=this.LA(1);for(;this.tokenMatcher(n,e)===!1;)n=this.SKIP_TOKEN(),this.addToResyncTokens(n,r);return Yt(r)}attemptInRepetitionRecovery(e,r,n,i,a,o,l){}getCurrentGrammarPath(e,r){let n=this.getHumanReadableRuleStack(),i=ee(this.RULE_OCCURRENCE_STACK);return{ruleStack:n,occurrenceStack:i,lastTok:e,lastTokOccurrence:r}}getHumanReadableRuleStack(){return v(this.RULE_STACK,e=>this.shortRuleNameToFullName(e))}};function CC(t,e,r,n,i,a,o){let l=this.getKeyForAutomaticLookahead(n,i),u=this.firstAfterRepMap[l];if(u===void 0){let p=this.getCurrRuleFullName(),m=this.getGAstProductions()[p];u=new a(m,i).startWalking(),this.firstAfterRepMap[l]=u}let c=u.token,f=u.occurrence,d=u.isEndOfRule;this.RULE_STACK.length===1&&d&&c===void 0&&(c=ot,f=1),!(c===void 0||f===void 0)&&this.shouldInRepetitionRecoveryBeTried(c,f,o)&&this.tryInRepetitionRecovery(t,e,r,c)}s(CC,"attemptInRepetitionRecovery");function qo(t,e,r){return r|e|t}s(qo,"getKeyForAutomaticLookahead");var nr=class{static{s(this,"LLkLookaheadStrategy")}constructor(e){var r;this.maxLookahead=(r=e?.maxLookahead)!==null&&r!==void 0?r:qe.maxLookahead}validate(e){let r=this.validateNoLeftRecursion(e.rules);if(D(r)){let n=this.validateEmptyOrAlternatives(e.rules),i=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),a=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead);return[...r,...n,...i,...a]}return r}validateNoLeftRecursion(e){return Oe(e,r=>uc(r,r,Tt))}validateEmptyOrAlternatives(e){return Oe(e,r=>Ph(r,Tt))}validateAmbiguousAlternationAlternatives(e,r){return Oe(e,n=>Mh(n,r,Tt))}validateSomeNonEmptyLookaheadPath(e,r){return Dh(e,r,Tt)}buildLookaheadForAlternation(e){return Ih(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,Sh)}buildLookaheadForOptional(e){return kh(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,Ms(e.prodType),Ch)}};var Yo=class{static{s(this,"LooksAhead")}initLooksAhead(e){this.dynamicTokensEnabled=S(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:qe.dynamicTokensEnabled,this.maxLookahead=S(e,"maxLookahead")?e.maxLookahead:qe.maxLookahead,this.lookaheadStrategy=S(e,"lookaheadStrategy")?e.lookaheadStrategy:new nr({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){k(e,r=>{this.TRACE_INIT(`${r.name} Rule Lookahead`,()=>{let{alternation:n,repetition:i,option:a,repetitionMandatory:o,repetitionMandatoryWithSeparator:l,repetitionWithSeparator:u}=NC(r);k(n,c=>{let f=c.idx===0?"":c.idx;this.TRACE_INIT(`${rt(c)}${f}`,()=>{let d=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:c.idx,rule:r,maxLookahead:c.maxLookahead||this.maxLookahead,hasPredicates:c.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),p=qo(this.fullRuleNameToShort[r.name],256,c.idx);this.setLaFuncCache(p,d)})}),k(i,c=>{this.computeLookaheadFunc(r,c.idx,768,"Repetition",c.maxLookahead,rt(c))}),k(a,c=>{this.computeLookaheadFunc(r,c.idx,512,"Option",c.maxLookahead,rt(c))}),k(o,c=>{this.computeLookaheadFunc(r,c.idx,1024,"RepetitionMandatory",c.maxLookahead,rt(c))}),k(l,c=>{this.computeLookaheadFunc(r,c.idx,1536,"RepetitionMandatoryWithSeparator",c.maxLookahead,rt(c))}),k(u,c=>{this.computeLookaheadFunc(r,c.idx,1280,"RepetitionWithSeparator",c.maxLookahead,rt(c))})})})}computeLookaheadFunc(e,r,n,i,a,o){this.TRACE_INIT(`${o}${r===0?"":r}`,()=>{let l=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:r,rule:e,maxLookahead:a||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:i}),u=qo(this.fullRuleNameToShort[e.name],n,r);this.setLaFuncCache(u,l)})}getKeyForAutomaticLookahead(e,r){let n=this.getLastExplicitRuleShortName();return qo(n,e,r)}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,r){this.lookAheadFuncsCache.set(e,r)}},pc=class extends ze{static{s(this,"DslMethodsCollectorVisitor")}constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}},Xo=new pc;function NC(t){Xo.reset(),t.accept(Xo);let e=Xo.dslMethods;return Xo.reset(),e}s(NC,"collectMethods");function gc(t,e){isNaN(t.startOffset)===!0?(t.startOffset=e.startOffset,t.endOffset=e.endOffset):t.endOffset<e.endOffset&&(t.endOffset=e.endOffset)}s(gc,"setNodeLocationOnlyOffset");function yc(t,e){isNaN(t.startOffset)===!0?(t.startOffset=e.startOffset,t.startColumn=e.startColumn,t.startLine=e.startLine,t.endOffset=e.endOffset,t.endColumn=e.endColumn,t.endLine=e.endLine):t.endOffset<e.endOffset&&(t.endOffset=e.endOffset,t.endColumn=e.endColumn,t.endLine=e.endLine)}s(yc,"setNodeLocationFull");function Hh(t,e,r){t.children[r]===void 0?t.children[r]=[e]:t.children[r].push(e)}s(Hh,"addTerminalToCst");function Vh(t,e,r){t.children[e]===void 0?t.children[e]=[r]:t.children[e].push(r)}s(Vh,"addNoneTerminalToCst");var wC="name";function xc(t,e){Object.defineProperty(t,wC,{enumerable:!1,configurable:!0,writable:!1,value:e})}s(xc,"defineNameProp");function _C(t,e){let r=Z(t),n=r.length;for(let i=0;i<n;i++){let a=r[i],o=t[a],l=o.length;for(let u=0;u<l;u++){let c=o[u];c.tokenTypeIdx===void 0&&this[c.name](c.children,e)}}}s(_C,"defaultVisit");function zh(t,e){let r=s(function(){},"derivedConstructor");xc(r,t+"BaseSemantics");let n={visit:s(function(i,a){if(N(i)&&(i=i[0]),!Ue(i))return this[i.name](i.children,a)},"visit"),validateVisitor:s(function(){let i=bC(this,e);if(!D(i)){let a=v(i,o=>o.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${a.join(`

`).replace(/\n/g,`
	`)}`)}},"validateVisitor")};return r.prototype=n,r.prototype.constructor=r,r._RULE_NAMES=e,r}s(zh,"createBaseSemanticVisitorConstructor");function qh(t,e,r){let n=s(function(){},"derivedConstructor");xc(n,t+"BaseSemanticsWithDefaults");let i=Object.create(r.prototype);return k(e,a=>{i[a]=_C}),n.prototype=i,n.prototype.constructor=n,n}s(qh,"createBaseVisitorConstructorWithDefaults");var Tc;(function(t){t[t.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",t[t.MISSING_METHOD=1]="MISSING_METHOD"})(Tc||(Tc={}));function bC(t,e){return $C(t,e)}s(bC,"validateVisitor");function $C(t,e){let r=Ne(e,i=>He(t[i])===!1),n=v(r,i=>({msg:`Missing visitor method: <${i}> on ${t.constructor.name} CST Visitor.`,type:Tc.MISSING_METHOD,methodName:i}));return Lt(n)}s($C,"validateMissingCstMethods");var el=class{static{s(this,"TreeBuilder")}initTreeBuilder(e){if(this.CST_STACK=[],this.outputCst=e.outputCst,this.nodeLocationTracking=S(e,"nodeLocationTracking")?e.nodeLocationTracking:qe.nodeLocationTracking,!this.outputCst)this.cstInvocationStateUpdate=pe,this.cstFinallyStateUpdate=pe,this.cstPostTerminal=pe,this.cstPostNonTerminal=pe,this.cstPostRule=pe;else if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=yc,this.setNodeLocationFromNode=yc,this.cstPostRule=pe,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=pe,this.setNodeLocationFromNode=pe,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=gc,this.setNodeLocationFromNode=gc,this.cstPostRule=pe,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=pe,this.setNodeLocationFromNode=pe,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=pe,this.setNodeLocationFromNode=pe,this.cstPostRule=pe,this.setInitialNodeLocation=pe;else throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`)}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){let r=this.LA(1);e.location={startOffset:r.startOffset,startLine:r.startLine,startColumn:r.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){let r={name:e,children:Object.create(null)};this.setInitialNodeLocation(r),this.CST_STACK.push(r)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){let r=this.LA(0),n=e.location;n.startOffset<=r.startOffset?(n.endOffset=r.endOffset,n.endLine=r.endLine,n.endColumn=r.endColumn):(n.startOffset=NaN,n.startLine=NaN,n.startColumn=NaN)}cstPostRuleOnlyOffset(e){let r=this.LA(0),n=e.location;n.startOffset<=r.startOffset?n.endOffset=r.endOffset:n.startOffset=NaN}cstPostTerminal(e,r){let n=this.CST_STACK[this.CST_STACK.length-1];Hh(n,r,e),this.setNodeLocationFromToken(n.location,r)}cstPostNonTerminal(e,r){let n=this.CST_STACK[this.CST_STACK.length-1];Vh(n,r,e),this.setNodeLocationFromNode(n.location,e.location)}getBaseCstVisitorConstructor(){if(Ue(this.baseCstVisitorConstructor)){let e=zh(this.className,Z(this.gastProductionsCache));return this.baseCstVisitorConstructor=e,e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if(Ue(this.baseCstVisitorWithDefaultsConstructor)){let e=qh(this.className,Z(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=e,e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){let e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){let e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){let e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}};var tl=class{static{s(this,"LexerAdapter")}initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(e){if(this.selfAnalysisDone!==!0)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=e,this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):Fi}LA(e){let r=this.currIdx+e;return r<0||this.tokVectorLength<=r?Fi:this.tokVector[r]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}};var rl=class{static{s(this,"RecognizerApi")}ACTION(e){return e.call(this)}consume(e,r,n){return this.consumeInternal(r,e,n)}subrule(e,r,n){return this.subruleInternal(r,e,n)}option(e,r){return this.optionInternal(r,e)}or(e,r){return this.orInternal(r,e)}many(e,r){return this.manyInternal(e,r)}atLeastOne(e,r){return this.atLeastOneInternal(e,r)}CONSUME(e,r){return this.consumeInternal(e,0,r)}CONSUME1(e,r){return this.consumeInternal(e,1,r)}CONSUME2(e,r){return this.consumeInternal(e,2,r)}CONSUME3(e,r){return this.consumeInternal(e,3,r)}CONSUME4(e,r){return this.consumeInternal(e,4,r)}CONSUME5(e,r){return this.consumeInternal(e,5,r)}CONSUME6(e,r){return this.consumeInternal(e,6,r)}CONSUME7(e,r){return this.consumeInternal(e,7,r)}CONSUME8(e,r){return this.consumeInternal(e,8,r)}CONSUME9(e,r){return this.consumeInternal(e,9,r)}SUBRULE(e,r){return this.subruleInternal(e,0,r)}SUBRULE1(e,r){return this.subruleInternal(e,1,r)}SUBRULE2(e,r){return this.subruleInternal(e,2,r)}SUBRULE3(e,r){return this.subruleInternal(e,3,r)}SUBRULE4(e,r){return this.subruleInternal(e,4,r)}SUBRULE5(e,r){return this.subruleInternal(e,5,r)}SUBRULE6(e,r){return this.subruleInternal(e,6,r)}SUBRULE7(e,r){return this.subruleInternal(e,7,r)}SUBRULE8(e,r){return this.subruleInternal(e,8,r)}SUBRULE9(e,r){return this.subruleInternal(e,9,r)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,r,n=Gi){if(oe(this.definedRulesNames,e)){let o={message:Tt.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className}),type:ke.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(o)}this.definedRulesNames.push(e);let i=this.defineRule(e,r,n);return this[e]=i,i}OVERRIDE_RULE(e,r,n=Gi){let i=Lh(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(i);let a=this.defineRule(e,r,n);return this[e]=a,a}BACKTRACK(e,r){return function(){this.isBackTrackingStack.push(1);let n=this.saveRecogState();try{return e.apply(this,r),!0}catch(i){if(Sr(i))return!1;throw i}finally{this.reloadRecogState(n),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return bo(q(this.gastProductionsCache))}};var nl=class{static{s(this,"RecognizerEngine")}initRecognizerEngine(e,r){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=$i,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},S(r,"serializedGrammar"))throw Error(`The Parser's configuration can no longer contain a <serializedGrammar> property.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0
	For Further details.`);if(N(e)){if(D(e))throw Error(`A Token Vocabulary cannot be empty.
	Note that the first argument for the parser constructor
	is no longer a Token vector (since v4.0).`);if(typeof e[0].startOffset=="number")throw Error(`The Parser constructor no longer accepts a token vector as the first argument.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0
	For Further details.`)}if(N(e))this.tokensMap=me(e,(a,o)=>(a[o.name]=o,a),{});else if(S(e,"modes")&&Ge(ye(q(e.modes)),dh)){let a=ye(q(e.modes)),o=Si(a);this.tokensMap=me(o,(l,u)=>(l[u.name]=u,l),{})}else if(de(e))this.tokensMap=ee(e);else throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=ot;let n=S(e,"modes")?ye(q(e.modes)):q(e),i=Ge(n,a=>D(a.categoryMatches));this.tokenMatcher=i?$i:Zt,Qt(q(this.tokensMap))}defineRule(e,r,n){if(this.selfAnalysisDone)throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);let i=S(n,"resyncEnabled")?n.resyncEnabled:Gi.resyncEnabled,a=S(n,"recoveryValueFunc")?n.recoveryValueFunc:Gi.recoveryValueFunc,o=this.ruleShortNameIdx<<12;this.ruleShortNameIdx++,this.shortRuleNameToFull[o]=e,this.fullRuleNameToShort[e]=o;let l;return this.outputCst===!0?l=s(function(...f){try{this.ruleInvocationStateUpdate(o,e,this.subruleIdx),r.apply(this,f);let d=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(d),d}catch(d){return this.invokeRuleCatch(d,i,a)}finally{this.ruleFinallyStateUpdate()}},"invokeRuleWithTry"):l=s(function(...f){try{return this.ruleInvocationStateUpdate(o,e,this.subruleIdx),r.apply(this,f)}catch(d){return this.invokeRuleCatch(d,i,a)}finally{this.ruleFinallyStateUpdate()}},"invokeRuleWithTryCst"),Object.assign(l,{ruleName:e,originalGrammarAction:r})}invokeRuleCatch(e,r,n){let i=this.RULE_STACK.length===1,a=r&&!this.isBackTracking()&&this.recoveryEnabled;if(Sr(e)){let o=e;if(a){let l=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(l))if(o.resyncedTokens=this.reSyncTo(l),this.outputCst){let u=this.CST_STACK[this.CST_STACK.length-1];return u.recoveredNode=!0,u}else return n(e);else{if(this.outputCst){let u=this.CST_STACK[this.CST_STACK.length-1];u.recoveredNode=!0,o.partialCstResult=u}throw o}}else{if(i)return this.moveToTerminatedState(),n(e);throw o}}else throw e}optionInternal(e,r){let n=this.getKeyForAutomaticLookahead(512,r);return this.optionInternalLogic(e,r,n)}optionInternalLogic(e,r,n){let i=this.getLaFuncFromCache(n),a;if(typeof e!="function"){a=e.DEF;let o=e.GATE;if(o!==void 0){let l=i;i=s(()=>o.call(this)&&l.call(this),"lookAheadFunc")}}else a=e;if(i.call(this)===!0)return a.call(this)}atLeastOneInternal(e,r){let n=this.getKeyForAutomaticLookahead(1024,e);return this.atLeastOneInternalLogic(e,r,n)}atLeastOneInternalLogic(e,r,n){let i=this.getLaFuncFromCache(n),a;if(typeof r!="function"){a=r.DEF;let o=r.GATE;if(o!==void 0){let l=i;i=s(()=>o.call(this)&&l.call(this),"lookAheadFunc")}}else a=r;if(i.call(this)===!0){let o=this.doSingleRepetition(a);for(;i.call(this)===!0&&o===!0;)o=this.doSingleRepetition(a)}else throw this.raiseEarlyExitException(e,ce.REPETITION_MANDATORY,r.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,r],i,1024,e,Uo)}atLeastOneSepFirstInternal(e,r){let n=this.getKeyForAutomaticLookahead(1536,e);this.atLeastOneSepFirstInternalLogic(e,r,n)}atLeastOneSepFirstInternalLogic(e,r,n){let i=r.DEF,a=r.SEP;if(this.getLaFuncFromCache(n).call(this)===!0){i.call(this);let l=s(()=>this.tokenMatcher(this.LA(1),a),"separatorLookAheadFunc");for(;this.tokenMatcher(this.LA(1),a)===!0;)this.CONSUME(a),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,a,l,i,Ps],l,1536,e,Ps)}else throw this.raiseEarlyExitException(e,ce.REPETITION_MANDATORY_WITH_SEPARATOR,r.ERR_MSG)}manyInternal(e,r){let n=this.getKeyForAutomaticLookahead(768,e);return this.manyInternalLogic(e,r,n)}manyInternalLogic(e,r,n){let i=this.getLaFuncFromCache(n),a;if(typeof r!="function"){a=r.DEF;let l=r.GATE;if(l!==void 0){let u=i;i=s(()=>l.call(this)&&u.call(this),"lookaheadFunction")}}else a=r;let o=!0;for(;i.call(this)===!0&&o===!0;)o=this.doSingleRepetition(a);this.attemptInRepetitionRecovery(this.manyInternal,[e,r],i,768,e,Go,o)}manySepFirstInternal(e,r){let n=this.getKeyForAutomaticLookahead(1280,e);this.manySepFirstInternalLogic(e,r,n)}manySepFirstInternalLogic(e,r,n){let i=r.DEF,a=r.SEP;if(this.getLaFuncFromCache(n).call(this)===!0){i.call(this);let l=s(()=>this.tokenMatcher(this.LA(1),a),"separatorLookAheadFunc");for(;this.tokenMatcher(this.LA(1),a)===!0;)this.CONSUME(a),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,a,l,i,Ls],l,1280,e,Ls)}}repetitionSepSecondInternal(e,r,n,i,a){for(;n();)this.CONSUME(r),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,r,n,i,a],n,1536,e,a)}doSingleRepetition(e){let r=this.getLexerPosition();return e.call(this),this.getLexerPosition()>r}orInternal(e,r){let n=this.getKeyForAutomaticLookahead(256,r),i=N(e)?e:e.DEF,o=this.getLaFuncFromCache(n).call(this,i);if(o!==void 0)return i[o].ALT.call(this);this.raiseNoAltException(r,e.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),this.RULE_STACK.length===0&&this.isAtEndOfInput()===!1){let e=this.LA(1),r=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new Gs(r,e))}}subruleInternal(e,r,n){let i;try{let a=n!==void 0?n.ARGS:void 0;return this.subruleIdx=r,i=e.apply(this,a),this.cstPostNonTerminal(i,n!==void 0&&n.LABEL!==void 0?n.LABEL:e.ruleName),i}catch(a){throw this.subruleInternalError(a,n,e.ruleName)}}subruleInternalError(e,r,n){throw Sr(e)&&e.partialCstResult!==void 0&&(this.cstPostNonTerminal(e.partialCstResult,r!==void 0&&r.LABEL!==void 0?r.LABEL:n),delete e.partialCstResult),e}consumeInternal(e,r,n){let i;try{let a=this.LA(1);this.tokenMatcher(a,e)===!0?(this.consumeToken(),i=a):this.consumeInternalError(e,a,n)}catch(a){i=this.consumeInternalRecovery(e,r,a)}return this.cstPostTerminal(n!==void 0&&n.LABEL!==void 0?n.LABEL:e.name,i),i}consumeInternalError(e,r,n){let i,a=this.LA(0);throw n!==void 0&&n.ERR_MSG?i=n.ERR_MSG:i=this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:r,previous:a,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new Yr(i,r,a))}consumeInternalRecovery(e,r,n){if(this.recoveryEnabled&&n.name==="MismatchedTokenException"&&!this.isBackTracking()){let i=this.getFollowsForInRuleRecovery(e,r);try{return this.tryInRuleRecovery(e,i)}catch(a){throw a.name===dc?n:a}}else throw n}saveRecogState(){let e=this.errors,r=ee(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:r,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors,this.importLexerState(e.lexerState),this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,r,n){this.RULE_OCCURRENCE_STACK.push(n),this.RULE_STACK.push(e),this.cstInvocationStateUpdate(r)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){let e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),ot)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}};var il=class{static{s(this,"ErrorHandler")}initErrorHandler(e){this._errors=[],this.errorMessageProvider=S(e,"errorMessageProvider")?e.errorMessageProvider:qe.errorMessageProvider}SAVE_ERROR(e){if(Sr(e))return e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:ee(this.RULE_OCCURRENCE_STACK)},this._errors.push(e),e;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return ee(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,r,n){let i=this.getCurrRuleFullName(),a=this.getGAstProductions()[i],l=Mi(e,a,r,this.maxLookahead)[0],u=[];for(let f=1;f<=this.maxLookahead;f++)u.push(this.LA(f));let c=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:l,actual:u,previous:this.LA(0),customUserDescription:n,ruleName:i});throw this.SAVE_ERROR(new Us(c,this.LA(1),this.LA(0)))}raiseNoAltException(e,r){let n=this.getCurrRuleFullName(),i=this.getGAstProductions()[n],a=Pi(e,i,this.maxLookahead),o=[];for(let c=1;c<=this.maxLookahead;c++)o.push(this.LA(c));let l=this.LA(0),u=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:a,actual:o,previous:l,customUserDescription:r,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new Fs(u,this.LA(1),l))}};var sl=class{static{s(this,"ContentAssist")}initContentAssist(){}computeContentAssist(e,r){let n=this.gastProductionsCache[e];if(Ue(n))throw Error(`Rule ->${e}<- does not exist in this grammar.`);return jo([n],r,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){let r=we(e.ruleStack),i=this.getGAstProductions()[r];return new Fo(i,e).startWalking()}};var ll={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(ll);var Xh=!0,Yh=Math.pow(2,8)-1,Zh=kr({name:"RECORDING_PHASE_TOKEN",pattern:ue.NA});Qt([Zh]);var Qh=tr(Zh,`This IToken indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,-1,-1,-1,-1,-1,-1);Object.freeze(Qh);var LC={name:`This CSTNode indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,children:{}},al=class{static{s(this,"GastRecorder")}initGastRecorder(e){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let e=0;e<10;e++){let r=e>0?e:"";this[`CONSUME${r}`]=function(n,i){return this.consumeInternalRecord(n,e,i)},this[`SUBRULE${r}`]=function(n,i){return this.subruleInternalRecord(n,e,i)},this[`OPTION${r}`]=function(n){return this.optionInternalRecord(n,e)},this[`OR${r}`]=function(n){return this.orInternalRecord(n,e)},this[`MANY${r}`]=function(n){this.manyInternalRecord(e,n)},this[`MANY_SEP${r}`]=function(n){this.manySepFirstInternalRecord(e,n)},this[`AT_LEAST_ONE${r}`]=function(n){this.atLeastOneInternalRecord(e,n)},this[`AT_LEAST_ONE_SEP${r}`]=function(n){this.atLeastOneSepFirstInternalRecord(e,n)}}this.consume=function(e,r,n){return this.consumeInternalRecord(r,e,n)},this.subrule=function(e,r,n){return this.subruleInternalRecord(r,e,n)},this.option=function(e,r){return this.optionInternalRecord(r,e)},this.or=function(e,r){return this.orInternalRecord(r,e)},this.many=function(e,r){this.manyInternalRecord(e,r)},this.atLeastOne=function(e,r){this.atLeastOneInternalRecord(e,r)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{let e=this;for(let r=0;r<10;r++){let n=r>0?r:"";delete e[`CONSUME${n}`],delete e[`SUBRULE${n}`],delete e[`OPTION${n}`],delete e[`OR${n}`],delete e[`MANY${n}`],delete e[`MANY_SEP${n}`],delete e[`AT_LEAST_ONE${n}`],delete e[`AT_LEAST_ONE_SEP${n}`]}delete e.consume,delete e.subrule,delete e.option,delete e.or,delete e.many,delete e.atLeastOne,delete e.ACTION,delete e.BACKTRACK,delete e.LA})}ACTION_RECORD(e){}BACKTRACK_RECORD(e,r){return()=>!0}LA_RECORD(e){return Fi}topLevelRuleRecord(e,r){try{let n=new Ve({definition:[],name:e});return n.name=e,this.recordingProdStack.push(n),r.call(this),this.recordingProdStack.pop(),n}catch(n){if(n.KNOWN_RECORDER_ERROR!==!0)try{n.message=n.message+`
	 This error was thrown during the "grammar recording phase" For more info see:
	https://chevrotain.io/docs/guide/internals.html#grammar-recording`}catch{throw n}throw n}}optionInternalRecord(e,r){return js.call(this,z,e,r)}atLeastOneInternalRecord(e,r){js.call(this,re,r,e)}atLeastOneSepFirstInternalRecord(e,r){js.call(this,ne,r,e,Xh)}manyInternalRecord(e,r){js.call(this,G,r,e)}manySepFirstInternalRecord(e,r){js.call(this,X,r,e,Xh)}orInternalRecord(e,r){return PC.call(this,e,r)}subruleInternalRecord(e,r,n){if(ol(r),!e||S(e,"ruleName")===!1){let l=new Error(`<SUBRULE${Jh(r)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw l.KNOWN_RECORDER_ERROR=!0,l}let i=Pt(this.recordingProdStack),a=e.ruleName,o=new V({idx:r,nonTerminalName:a,label:n?.LABEL,referencedRule:void 0});return i.definition.push(o),this.outputCst?LC:ll}consumeInternalRecord(e,r,n){if(ol(r),!tc(e)){let o=new Error(`<CONSUME${Jh(r)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw o.KNOWN_RECORDER_ERROR=!0,o}let i=Pt(this.recordingProdStack),a=new F({idx:r,terminalType:e,label:n?.LABEL});return i.definition.push(a),Qh}};function js(t,e,r,n=!1){ol(r);let i=Pt(this.recordingProdStack),a=He(e)?e:e.DEF,o=new t({definition:[],idx:r});return n&&(o.separator=e.SEP),S(e,"MAX_LOOKAHEAD")&&(o.maxLookahead=e.MAX_LOOKAHEAD),this.recordingProdStack.push(o),a.call(this),i.definition.push(o),this.recordingProdStack.pop(),ll}s(js,"recordProd");function PC(t,e){ol(e);let r=Pt(this.recordingProdStack),n=N(t)===!1,i=n===!1?t:t.DEF,a=new Y({definition:[],idx:e,ignoreAmbiguities:n&&t.IGNORE_AMBIGUITIES===!0});S(t,"MAX_LOOKAHEAD")&&(a.maxLookahead=t.MAX_LOOKAHEAD);let o=Cs(i,l=>He(l.GATE));return a.hasPredicates=o,r.definition.push(a),k(i,l=>{let u=new te({definition:[]});a.definition.push(u),S(l,"IGNORE_AMBIGUITIES")?u.ignoreAmbiguities=l.IGNORE_AMBIGUITIES:S(l,"GATE")&&(u.ignoreAmbiguities=!0),this.recordingProdStack.push(u),l.ALT.call(this),this.recordingProdStack.pop()}),ll}s(PC,"recordOrProd");function Jh(t){return t===0?"":`${t}`}s(Jh,"getIdxSuffix");function ol(t){if(t<0||t>Yh){let e=new Error(`Invalid DSL Method idx value: <${t}>
	Idx value must be a none negative value smaller than ${Yh+1}`);throw e.KNOWN_RECORDER_ERROR=!0,e}}s(ol,"assertMethodIdxIsValid");var ul=class{static{s(this,"PerformanceTracer")}initPerformanceTracer(e){if(S(e,"traceInitPerf")){let r=e.traceInitPerf,n=typeof r=="number";this.traceInitMaxIdent=n?r:1/0,this.traceInitPerf=n?r>0:r}else this.traceInitMaxIdent=0,this.traceInitPerf=qe.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(e,r){if(this.traceInitPerf===!0){this.traceInitIndent++;let n=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${n}--> <${e}>`);let{time:i,value:a}=ws(r),o=i>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&o(`${n}<-- <${e}> time: ${i}ms`),this.traceInitIndent--,a}else return r()}};function eg(t,e){e.forEach(r=>{let n=r.prototype;Object.getOwnPropertyNames(n).forEach(i=>{if(i==="constructor")return;let a=Object.getOwnPropertyDescriptor(n,i);a&&(a.get||a.set)?Object.defineProperty(t.prototype,i,a):t.prototype[i]=r.prototype[i]})})}s(eg,"applyMixins");var Fi=tr(ot,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(Fi);var qe=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:rr,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),Gi=Object.freeze({recoveryValueFunc:s(()=>{},"recoveryValueFunc"),resyncEnabled:!0}),ke;(function(t){t[t.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",t[t.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",t[t.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",t[t.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",t[t.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",t[t.LEFT_RECURSION=5]="LEFT_RECURSION",t[t.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",t[t.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",t[t.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",t[t.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",t[t.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",t[t.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",t[t.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",t[t.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"})(ke||(ke={}));function cl(t=void 0){return function(){return t}}s(cl,"EMPTY_ALT");var Ws=class t{static{s(this,"Parser")}static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let e;this.selfAnalysisDone=!0;let r=this.className;this.TRACE_INIT("toFastProps",()=>{_s(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),k(this.definedRulesNames,i=>{let o=this[i].originalGrammarAction,l;this.TRACE_INIT(`${i} Rule`,()=>{l=this.topLevelRuleRecord(i,o)}),this.gastProductionsCache[i]=l})}finally{this.disableRecording()}});let n=[];if(this.TRACE_INIT("Grammar Resolving",()=>{n=Fh({rules:q(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(n)}),this.TRACE_INIT("Grammar Validations",()=>{if(D(n)&&this.skipValidations===!1){let i=Gh({rules:q(this.gastProductionsCache),tokenTypes:q(this.tokensMap),errMsgProvider:Tt,grammarName:r}),a=bh({lookaheadStrategy:this.lookaheadStrategy,rules:q(this.gastProductionsCache),tokenTypes:q(this.tokensMap),grammarName:r});this.definitionErrors=this.definitionErrors.concat(i,a)}}),D(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{let i=Vm(q(this.gastProductionsCache));this.resyncFollows=i}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var i,a;(a=(i=this.lookaheadStrategy).initialize)===null||a===void 0||a.call(i,{rules:q(this.gastProductionsCache)}),this.preComputeLookaheadFunctions(q(this.gastProductionsCache))})),!t.DEFER_DEFINITION_ERRORS_HANDLING&&!D(this.definitionErrors))throw e=v(this.definitionErrors,i=>i.message),new Error(`Parser Definition Errors detected:
 ${e.join(`
-------------------------------
`)}`)})}constructor(e,r){this.definitionErrors=[],this.selfAnalysisDone=!1;let n=this;if(n.initErrorHandler(r),n.initLexerAdapter(),n.initLooksAhead(r),n.initRecognizerEngine(e,r),n.initRecoverable(r),n.initTreeBuilder(r),n.initContentAssist(),n.initGastRecorder(r),n.initPerformanceTracer(r),S(r,"ignoredIssues"))throw new Error(`The <ignoredIssues> IParserConfig property has been deprecated.
	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.
	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES
	For further details.`);this.skipValidations=S(r,"skipValidations")?r.skipValidations:qe.skipValidations}};Ws.DEFER_DEFINITION_ERRORS_HANDLING=!1;eg(Ws,[zo,Yo,el,tl,nl,rl,il,sl,al,ul]);var Ks=class extends Ws{static{s(this,"EmbeddedActionsParser")}constructor(e,r=qe){let n=ee(r);n.outputCst=!1,super(e,n)}};function Jr(t,e,r){return`${t.name}_${e}_${r}`}s(Jr,"buildATNKey");var Cr=1,DC=2,tg=4,rg=5;var ji=7,FC=8,GC=9,UC=10,BC=11,ng=12,Hs=class{static{s(this,"AbstractTransition")}constructor(e){this.target=e}isEpsilon(){return!1}},Ui=class extends Hs{static{s(this,"AtomTransition")}constructor(e,r){super(e),this.tokenType=r}},Vs=class extends Hs{static{s(this,"EpsilonTransition")}constructor(e){super(e)}isEpsilon(){return!0}},Bi=class extends Hs{static{s(this,"RuleTransition")}constructor(e,r,n){super(e),this.rule=r,this.followState=n}isEpsilon(){return!0}};function ig(t){let e={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};jC(e,t);let r=t.length;for(let n=0;n<r;n++){let i=t[n],a=Zr(e,i,i);a!==void 0&&QC(e,i,a)}return e}s(ig,"createATN");function jC(t,e){let r=e.length;for(let n=0;n<r;n++){let i=e[n],a=_e(t,i,void 0,{type:DC}),o=_e(t,i,void 0,{type:ji});a.stop=o,t.ruleToStartState.set(i,a),t.ruleToStopState.set(i,o)}}s(jC,"createRuleStartAndStopATNStates");function sg(t,e,r){return r instanceof F?Ac(t,e,r.terminalType,r):r instanceof V?ZC(t,e,r):r instanceof Y?zC(t,e,r):r instanceof z?qC(t,e,r):r instanceof G?WC(t,e,r):r instanceof X?KC(t,e,r):r instanceof re?HC(t,e,r):r instanceof ne?VC(t,e,r):Zr(t,e,r)}s(sg,"atom");function WC(t,e,r){let n=_e(t,e,r,{type:rg});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r));return og(t,e,r,i)}s(WC,"repetition");function KC(t,e,r){let n=_e(t,e,r,{type:rg});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r)),a=Ac(t,e,r.separator,r);return og(t,e,r,i,a)}s(KC,"repetitionSep");function HC(t,e,r){let n=_e(t,e,r,{type:tg});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r));return ag(t,e,r,i)}s(HC,"repetitionMandatory");function VC(t,e,r){let n=_e(t,e,r,{type:tg});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r)),a=Ac(t,e,r.separator,r);return ag(t,e,r,i,a)}s(VC,"repetitionMandatorySep");function zC(t,e,r){let n=_e(t,e,r,{type:Cr});Nr(t,n);let i=v(r.definition,o=>sg(t,e,o));return Wi(t,e,n,r,...i)}s(zC,"alternation");function qC(t,e,r){let n=_e(t,e,r,{type:Cr});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r));return XC(t,e,r,i)}s(qC,"option");function Zr(t,e,r){let n=Ne(v(r.definition,i=>sg(t,e,i)),i=>i!==void 0);return n.length===1?n[0]:n.length===0?void 0:JC(t,n)}s(Zr,"block");function ag(t,e,r,n,i){let a=n.left,o=n.right,l=_e(t,e,r,{type:BC});Nr(t,l);let u=_e(t,e,r,{type:ng});return a.loopback=l,u.loopback=l,t.decisionMap[Jr(e,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",r.idx)]=l,ve(o,l),i===void 0?(ve(l,a),ve(l,u)):(ve(l,u),ve(l,i.left),ve(i.right,a)),{left:a,right:u}}s(ag,"plus");function og(t,e,r,n,i){let a=n.left,o=n.right,l=_e(t,e,r,{type:UC});Nr(t,l);let u=_e(t,e,r,{type:ng}),c=_e(t,e,r,{type:GC});return l.loopback=c,u.loopback=c,ve(l,a),ve(l,u),ve(o,c),i!==void 0?(ve(c,u),ve(c,i.left),ve(i.right,a)):ve(c,l),t.decisionMap[Jr(e,i?"RepetitionWithSeparator":"Repetition",r.idx)]=l,{left:l,right:u}}s(og,"star");function XC(t,e,r,n){let i=n.left,a=n.right;return ve(i,a),t.decisionMap[Jr(e,"Option",r.idx)]=i,n}s(XC,"optional");function Nr(t,e){return t.decisionStates.push(e),e.decision=t.decisionStates.length-1,e.decision}s(Nr,"defineDecisionState");function Wi(t,e,r,n,...i){let a=_e(t,e,n,{type:FC,start:r});r.end=a;for(let l of i)l!==void 0?(ve(r,l.left),ve(l.right,a)):ve(r,a);let o={left:r,right:a};return t.decisionMap[Jr(e,YC(n),n.idx)]=r,o}s(Wi,"makeAlts");function YC(t){if(t instanceof Y)return"Alternation";if(t instanceof z)return"Option";if(t instanceof G)return"Repetition";if(t instanceof X)return"RepetitionWithSeparator";if(t instanceof re)return"RepetitionMandatory";if(t instanceof ne)return"RepetitionMandatoryWithSeparator";throw new Error("Invalid production type encountered")}s(YC,"getProdType");function JC(t,e){let r=e.length;for(let a=0;a<r-1;a++){let o=e[a],l;o.left.transitions.length===1&&(l=o.left.transitions[0]);let u=l instanceof Bi,c=l,f=e[a+1].left;o.left.type===Cr&&o.right.type===Cr&&l!==void 0&&(u&&c.followState===o.right||l.target===o.right)?(u?c.followState=f:l.target=f,eN(t,o.right)):ve(o.right,f)}let n=e[0],i=e[r-1];return{left:n.left,right:i.right}}s(JC,"makeBlock");function Ac(t,e,r,n){let i=_e(t,e,n,{type:Cr}),a=_e(t,e,n,{type:Cr});return Ec(i,new Ui(a,r)),{left:i,right:a}}s(Ac,"tokenRef");function ZC(t,e,r){let n=r.referencedRule,i=t.ruleToStartState.get(n),a=_e(t,e,r,{type:Cr}),o=_e(t,e,r,{type:Cr}),l=new Bi(i,n,o);return Ec(a,l),{left:a,right:o}}s(ZC,"ruleRef");function QC(t,e,r){let n=t.ruleToStartState.get(e);ve(n,r.left);let i=t.ruleToStopState.get(e);return ve(r.right,i),{left:n,right:i}}s(QC,"buildRuleHandle");function ve(t,e){let r=new Vs(e);Ec(t,r)}s(ve,"epsilon");function _e(t,e,r,n){let i=Object.assign({atn:t,production:r,epsilonOnlyTransitions:!1,rule:e,transitions:[],nextTokenWithinRule:[],stateNumber:t.states.length},n);return t.states.push(i),i}s(_e,"newState");function Ec(t,e){t.transitions.length===0&&(t.epsilonOnlyTransitions=e.isEpsilon()),t.transitions.push(e)}s(Ec,"addTransition");function eN(t,e){t.states.splice(t.states.indexOf(e),1)}s(eN,"removeState");var zs={},Ki=class{static{s(this,"ATNConfigSet")}constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){let r=vc(e);r in this.map||(this.map[r]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return v(this.configs,e=>e.alt)}get key(){let e="";for(let r in this.map)e+=r+":";return e}};function vc(t,e=!0){return`${e?`a${t.alt}`:""}s${t.state.stateNumber}:${t.stack.map(r=>r.stateNumber.toString()).join("_")}`}s(vc,"getATNConfigKey");function tN(t,e){let r={};return n=>{let i=n.toString(),a=r[i];return a!==void 0||(a={atnStartState:t,decision:e,states:{}},r[i]=a),a}}s(tN,"createDFACache");var fl=class{static{s(this,"PredicateSet")}constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,r){this.predicates[e]=r}toString(){let e="",r=this.predicates.length;for(let n=0;n<r;n++)e+=this.predicates[n]===!0?"1":"0";return e}},lg=new fl,qs=class extends nr{static{s(this,"LLStarLookaheadStrategy")}constructor(e){var r;super(),this.logging=(r=e?.logging)!==null&&r!==void 0?r:n=>console.log(n)}initialize(e){this.atn=ig(e.rules),this.dfas=rN(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){let{prodOccurrence:r,rule:n,hasPredicates:i,dynamicTokensEnabled:a}=e,o=this.dfas,l=this.logging,u=Jr(n,"Alternation",r),f=this.atn.decisionMap[u].decision,d=v(Ko({maxLookahead:1,occurrence:r,prodType:"Alternation",rule:n}),p=>v(p,m=>m[0]));if(ug(d,!1)&&!a){let p=me(d,(m,g,y)=>(k(g,A=>{A&&(m[A.tokenTypeIdx]=y,k(A.categoryMatches,T=>{m[T]=y}))}),m),{});return i?function(m){var g;let y=this.LA(1),A=p[y.tokenTypeIdx];if(m!==void 0&&A!==void 0){let T=(g=m[A])===null||g===void 0?void 0:g.GATE;if(T!==void 0&&T.call(this)===!1)return}return A}:function(){let m=this.LA(1);return p[m.tokenTypeIdx]}}else return i?function(p){let m=new fl,g=p===void 0?0:p.length;for(let A=0;A<g;A++){let T=p?.[A].GATE;m.set(A,T===void 0||T.call(this))}let y=Ic.call(this,o,f,m,l);return typeof y=="number"?y:void 0}:function(){let p=Ic.call(this,o,f,lg,l);return typeof p=="number"?p:void 0}}buildLookaheadForOptional(e){let{prodOccurrence:r,rule:n,prodType:i,dynamicTokensEnabled:a}=e,o=this.dfas,l=this.logging,u=Jr(n,i,r),f=this.atn.decisionMap[u].decision,d=v(Ko({maxLookahead:1,occurrence:r,prodType:i,rule:n}),p=>v(p,m=>m[0]));if(ug(d)&&d[0][0]&&!a){let p=d[0],m=ye(p);if(m.length===1&&D(m[0].categoryMatches)){let y=m[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===y}}else{let g=me(m,(y,A)=>(A!==void 0&&(y[A.tokenTypeIdx]=!0,k(A.categoryMatches,T=>{y[T]=!0})),y),{});return function(){let y=this.LA(1);return g[y.tokenTypeIdx]===!0}}}return function(){let p=Ic.call(this,o,f,lg,l);return typeof p=="object"?!1:p===0}}};function ug(t,e=!0){let r=new Set;for(let n of t){let i=new Set;for(let a of n){if(a===void 0){if(e)break;return!1}let o=[a.tokenTypeIdx].concat(a.categoryMatches);for(let l of o)if(r.has(l)){if(!i.has(l))return!1}else r.add(l),i.add(l)}}return!0}s(ug,"isLL1Sequence");function rN(t){let e=t.decisionStates.length,r=Array(e);for(let n=0;n<e;n++)r[n]=tN(t.decisionStates[n],n);return r}s(rN,"initATNSimulator");function Ic(t,e,r,n){let i=t[e](r),a=i.start;if(a===void 0){let l=pN(i.atnStartState);a=dg(i,fg(l)),i.start=a}return nN.apply(this,[i,a,r,n])}s(Ic,"adaptivePredict");function nN(t,e,r,n){let i=e,a=1,o=[],l=this.LA(a++);for(;;){let u=uN(i,l);if(u===void 0&&(u=iN.apply(this,[t,i,l,a,r,n])),u===zs)return lN(o,i,l);if(u.isAcceptState===!0)return u.prediction;i=u,o.push(l),l=this.LA(a++)}}s(nN,"performLookahead");function iN(t,e,r,n,i,a){let o=cN(e.configs,r,i);if(o.size===0)return cg(t,e,r,zs),zs;let l=fg(o),u=dN(o,i);if(u!==void 0)l.isAcceptState=!0,l.prediction=u,l.configs.uniqueAlt=u;else if(yN(o)){let c=Dm(o.alts);l.isAcceptState=!0,l.prediction=c,l.configs.uniqueAlt=c,sN.apply(this,[t,n,o.alts,a])}return l=cg(t,e,r,l),l}s(iN,"computeLookaheadTarget");function sN(t,e,r,n){let i=[];for(let c=1;c<=e;c++)i.push(this.LA(c).tokenType);let a=t.atnStartState,o=a.rule,l=a.production,u=aN({topLevelRule:o,ambiguityIndices:r,production:l,prefixPath:i});n(u)}s(sN,"reportLookaheadAmbiguity");function aN(t){let e=v(t.prefixPath,i=>er(i)).join(", "),r=t.production.idx===0?"":t.production.idx,n=`Ambiguous Alternatives Detected: <${t.ambiguityIndices.join(", ")}> in <${oN(t.production)}${r}> inside <${t.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return n=n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,n}s(aN,"buildAmbiguityError");function oN(t){if(t instanceof V)return"SUBRULE";if(t instanceof z)return"OPTION";if(t instanceof Y)return"OR";if(t instanceof re)return"AT_LEAST_ONE";if(t instanceof ne)return"AT_LEAST_ONE_SEP";if(t instanceof X)return"MANY_SEP";if(t instanceof G)return"MANY";if(t instanceof F)return"CONSUME";throw Error("non exhaustive match")}s(oN,"getProductionDslName");function lN(t,e,r){let n=Oe(e.configs.elements,a=>a.state.transitions),i=Km(n.filter(a=>a instanceof Ui).map(a=>a.tokenType),a=>a.tokenTypeIdx);return{actualToken:r,possibleTokenTypes:i,tokenPath:t}}s(lN,"buildAdaptivePredictError");function uN(t,e){return t.edges[e.tokenTypeIdx]}s(uN,"getExistingTargetState");function cN(t,e,r){let n=new Ki,i=[];for(let o of t.elements){if(r.is(o.alt)===!1)continue;if(o.state.type===ji){i.push(o);continue}let l=o.state.transitions.length;for(let u=0;u<l;u++){let c=o.state.transitions[u],f=fN(c,e);f!==void 0&&n.add({state:f,alt:o.alt,stack:o.stack})}}let a;if(i.length===0&&n.size===1&&(a=n),a===void 0){a=new Ki;for(let o of n.elements)dl(o,a)}if(i.length>0&&!hN(a))for(let o of i)a.add(o);return a}s(cN,"computeReachSet");function fN(t,e){if(t instanceof Ui&&Os(e,t.tokenType))return t.target}s(fN,"getReachableTarget");function dN(t,e){let r;for(let n of t.elements)if(e.is(n.alt)===!0){if(r===void 0)r=n.alt;else if(r!==n.alt)return}return r}s(dN,"getUniqueAlt");function fg(t){return{configs:t,edges:{},isAcceptState:!1,prediction:-1}}s(fg,"newDFAState");function cg(t,e,r,n){return n=dg(t,n),e.edges[r.tokenTypeIdx]=n,n}s(cg,"addDFAEdge");function dg(t,e){if(e===zs)return e;let r=e.configs.key,n=t.states[r];return n!==void 0?n:(e.configs.finalize(),t.states[r]=e,e)}s(dg,"addDFAState");function pN(t){let e=new Ki,r=t.transitions.length;for(let n=0;n<r;n++){let a={state:t.transitions[n].target,alt:n,stack:[]};dl(a,e)}return e}s(pN,"computeStartState");function dl(t,e){let r=t.state;if(r.type===ji){if(t.stack.length>0){let i=[...t.stack],o={state:i.pop(),alt:t.alt,stack:i};dl(o,e)}else e.add(t);return}r.epsilonOnlyTransitions||e.add(t);let n=r.transitions.length;for(let i=0;i<n;i++){let a=r.transitions[i],o=mN(t,a);o!==void 0&&dl(o,e)}}s(dl,"closure");function mN(t,e){if(e instanceof Vs)return{state:e.target,alt:t.alt,stack:t.stack};if(e instanceof Bi){let r=[...t.stack,e.followState];return{state:e.target,alt:t.alt,stack:r}}}s(mN,"getEpsilonTarget");function hN(t){for(let e of t.elements)if(e.state.type===ji)return!0;return!1}s(hN,"hasConfigInRuleStopState");function gN(t){for(let e of t.elements)if(e.state.type!==ji)return!1;return!0}s(gN,"allConfigsInRuleStopStates");function yN(t){if(gN(t))return!0;let e=xN(t.elements);return TN(e)&&!RN(e)}s(yN,"hasConflictTerminatingPrediction");function xN(t){let e=new Map;for(let r of t){let n=vc(r,!1),i=e.get(n);i===void 0&&(i={},e.set(n,i)),i[r.alt]=!0}return e}s(xN,"getConflictingAltSets");function TN(t){for(let e of Array.from(t.values()))if(Object.keys(e).length>1)return!0;return!1}s(TN,"hasConflictingAltSet");function RN(t){for(let e of Array.from(t.values()))if(Object.keys(e).length===1)return!0;return!1}s(RN,"hasStateAssociatedWithOneAlt");var pg;(function(t){function e(r){return typeof r=="string"}s(e,"is"),t.is=e})(pg||(pg={}));var kc;(function(t){function e(r){return typeof r=="string"}s(e,"is"),t.is=e})(kc||(kc={}));var mg;(function(t){t.MIN_VALUE=-2147483648,t.MAX_VALUE=2147483647;function e(r){return typeof r=="number"&&t.MIN_VALUE<=r&&r<=t.MAX_VALUE}s(e,"is"),t.is=e})(mg||(mg={}));var pl;(function(t){t.MIN_VALUE=0,t.MAX_VALUE=2147483647;function e(r){return typeof r=="number"&&t.MIN_VALUE<=r&&r<=t.MAX_VALUE}s(e,"is"),t.is=e})(pl||(pl={}));var j;(function(t){function e(n,i){return n===Number.MAX_VALUE&&(n=pl.MAX_VALUE),i===Number.MAX_VALUE&&(i=pl.MAX_VALUE),{line:n,character:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.uinteger(i.line)&&h.uinteger(i.character)}s(r,"is"),t.is=r})(j||(j={}));var U;(function(t){function e(n,i,a,o){if(h.uinteger(n)&&h.uinteger(i)&&h.uinteger(a)&&h.uinteger(o))return{start:j.create(n,i),end:j.create(a,o)};if(j.is(n)&&j.is(i))return{start:n,end:i};throw new Error(`Range#create called with invalid arguments[${n}, ${i}, ${a}, ${o}]`)}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&j.is(i.start)&&j.is(i.end)}s(r,"is"),t.is=r})(U||(U={}));var ml;(function(t){function e(n,i){return{uri:n,range:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&U.is(i.range)&&(h.string(i.uri)||h.undefined(i.uri))}s(r,"is"),t.is=r})(ml||(ml={}));var hg;(function(t){function e(n,i,a,o){return{targetUri:n,targetRange:i,targetSelectionRange:a,originSelectionRange:o}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&U.is(i.targetRange)&&h.string(i.targetUri)&&U.is(i.targetSelectionRange)&&(U.is(i.originSelectionRange)||h.undefined(i.originSelectionRange))}s(r,"is"),t.is=r})(hg||(hg={}));var Sc;(function(t){function e(n,i,a,o){return{red:n,green:i,blue:a,alpha:o}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.numberRange(i.red,0,1)&&h.numberRange(i.green,0,1)&&h.numberRange(i.blue,0,1)&&h.numberRange(i.alpha,0,1)}s(r,"is"),t.is=r})(Sc||(Sc={}));var gg;(function(t){function e(n,i){return{range:n,color:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&U.is(i.range)&&Sc.is(i.color)}s(r,"is"),t.is=r})(gg||(gg={}));var yg;(function(t){function e(n,i,a){return{label:n,textEdit:i,additionalTextEdits:a}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.string(i.label)&&(h.undefined(i.textEdit)||Vi.is(i))&&(h.undefined(i.additionalTextEdits)||h.typedArray(i.additionalTextEdits,Vi.is))}s(r,"is"),t.is=r})(yg||(yg={}));var xg;(function(t){t.Comment="comment",t.Imports="imports",t.Region="region"})(xg||(xg={}));var Tg;(function(t){function e(n,i,a,o,l,u){let c={startLine:n,endLine:i};return h.defined(a)&&(c.startCharacter=a),h.defined(o)&&(c.endCharacter=o),h.defined(l)&&(c.kind=l),h.defined(u)&&(c.collapsedText=u),c}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.uinteger(i.startLine)&&h.uinteger(i.startLine)&&(h.undefined(i.startCharacter)||h.uinteger(i.startCharacter))&&(h.undefined(i.endCharacter)||h.uinteger(i.endCharacter))&&(h.undefined(i.kind)||h.string(i.kind))}s(r,"is"),t.is=r})(Tg||(Tg={}));var Cc;(function(t){function e(n,i){return{location:n,message:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&ml.is(i.location)&&h.string(i.message)}s(r,"is"),t.is=r})(Cc||(Cc={}));var Rg;(function(t){t.Error=1,t.Warning=2,t.Information=3,t.Hint=4})(Rg||(Rg={}));var Ag;(function(t){t.Unnecessary=1,t.Deprecated=2})(Ag||(Ag={}));var Eg;(function(t){function e(r){let n=r;return h.objectLiteral(n)&&h.string(n.href)}s(e,"is"),t.is=e})(Eg||(Eg={}));var hl;(function(t){function e(n,i,a,o,l,u){let c={range:n,message:i};return h.defined(a)&&(c.severity=a),h.defined(o)&&(c.code=o),h.defined(l)&&(c.source=l),h.defined(u)&&(c.relatedInformation=u),c}s(e,"create"),t.create=e;function r(n){var i;let a=n;return h.defined(a)&&U.is(a.range)&&h.string(a.message)&&(h.number(a.severity)||h.undefined(a.severity))&&(h.integer(a.code)||h.string(a.code)||h.undefined(a.code))&&(h.undefined(a.codeDescription)||h.string((i=a.codeDescription)===null||i===void 0?void 0:i.href))&&(h.string(a.source)||h.undefined(a.source))&&(h.undefined(a.relatedInformation)||h.typedArray(a.relatedInformation,Cc.is))}s(r,"is"),t.is=r})(hl||(hl={}));var Hi;(function(t){function e(n,i,...a){let o={title:n,command:i};return h.defined(a)&&a.length>0&&(o.arguments=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.title)&&h.string(i.command)}s(r,"is"),t.is=r})(Hi||(Hi={}));var Vi;(function(t){function e(a,o){return{range:a,newText:o}}s(e,"replace"),t.replace=e;function r(a,o){return{range:{start:a,end:a},newText:o}}s(r,"insert"),t.insert=r;function n(a){return{range:a,newText:""}}s(n,"del"),t.del=n;function i(a){let o=a;return h.objectLiteral(o)&&h.string(o.newText)&&U.is(o.range)}s(i,"is"),t.is=i})(Vi||(Vi={}));var Nc;(function(t){function e(n,i,a){let o={label:n};return i!==void 0&&(o.needsConfirmation=i),a!==void 0&&(o.description=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.string(i.label)&&(h.boolean(i.needsConfirmation)||i.needsConfirmation===void 0)&&(h.string(i.description)||i.description===void 0)}s(r,"is"),t.is=r})(Nc||(Nc={}));var zi;(function(t){function e(r){let n=r;return h.string(n)}s(e,"is"),t.is=e})(zi||(zi={}));var vg;(function(t){function e(a,o,l){return{range:a,newText:o,annotationId:l}}s(e,"replace"),t.replace=e;function r(a,o,l){return{range:{start:a,end:a},newText:o,annotationId:l}}s(r,"insert"),t.insert=r;function n(a,o){return{range:a,newText:"",annotationId:o}}s(n,"del"),t.del=n;function i(a){let o=a;return Vi.is(o)&&(Nc.is(o.annotationId)||zi.is(o.annotationId))}s(i,"is"),t.is=i})(vg||(vg={}));var wc;(function(t){function e(n,i){return{textDocument:n,edits:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&Lc.is(i.textDocument)&&Array.isArray(i.edits)}s(r,"is"),t.is=r})(wc||(wc={}));var _c;(function(t){function e(n,i,a){let o={kind:"create",uri:n};return i!==void 0&&(i.overwrite!==void 0||i.ignoreIfExists!==void 0)&&(o.options=i),a!==void 0&&(o.annotationId=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return i&&i.kind==="create"&&h.string(i.uri)&&(i.options===void 0||(i.options.overwrite===void 0||h.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||h.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||zi.is(i.annotationId))}s(r,"is"),t.is=r})(_c||(_c={}));var bc;(function(t){function e(n,i,a,o){let l={kind:"rename",oldUri:n,newUri:i};return a!==void 0&&(a.overwrite!==void 0||a.ignoreIfExists!==void 0)&&(l.options=a),o!==void 0&&(l.annotationId=o),l}s(e,"create"),t.create=e;function r(n){let i=n;return i&&i.kind==="rename"&&h.string(i.oldUri)&&h.string(i.newUri)&&(i.options===void 0||(i.options.overwrite===void 0||h.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||h.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||zi.is(i.annotationId))}s(r,"is"),t.is=r})(bc||(bc={}));var $c;(function(t){function e(n,i,a){let o={kind:"delete",uri:n};return i!==void 0&&(i.recursive!==void 0||i.ignoreIfNotExists!==void 0)&&(o.options=i),a!==void 0&&(o.annotationId=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return i&&i.kind==="delete"&&h.string(i.uri)&&(i.options===void 0||(i.options.recursive===void 0||h.boolean(i.options.recursive))&&(i.options.ignoreIfNotExists===void 0||h.boolean(i.options.ignoreIfNotExists)))&&(i.annotationId===void 0||zi.is(i.annotationId))}s(r,"is"),t.is=r})($c||($c={}));var Oc;(function(t){function e(r){let n=r;return n&&(n.changes!==void 0||n.documentChanges!==void 0)&&(n.documentChanges===void 0||n.documentChanges.every(i=>h.string(i.kind)?_c.is(i)||bc.is(i)||$c.is(i):wc.is(i)))}s(e,"is"),t.is=e})(Oc||(Oc={}));var Ig;(function(t){function e(n){return{uri:n}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.uri)}s(r,"is"),t.is=r})(Ig||(Ig={}));var kg;(function(t){function e(n,i){return{uri:n,version:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.uri)&&h.integer(i.version)}s(r,"is"),t.is=r})(kg||(kg={}));var Lc;(function(t){function e(n,i){return{uri:n,version:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.uri)&&(i.version===null||h.integer(i.version))}s(r,"is"),t.is=r})(Lc||(Lc={}));var Sg;(function(t){function e(n,i,a,o){return{uri:n,languageId:i,version:a,text:o}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.uri)&&h.string(i.languageId)&&h.integer(i.version)&&h.string(i.text)}s(r,"is"),t.is=r})(Sg||(Sg={}));var Pc;(function(t){t.PlainText="plaintext",t.Markdown="markdown";function e(r){let n=r;return n===t.PlainText||n===t.Markdown}s(e,"is"),t.is=e})(Pc||(Pc={}));var Xs;(function(t){function e(r){let n=r;return h.objectLiteral(r)&&Pc.is(n.kind)&&h.string(n.value)}s(e,"is"),t.is=e})(Xs||(Xs={}));var Cg;(function(t){t.Text=1,t.Method=2,t.Function=3,t.Constructor=4,t.Field=5,t.Variable=6,t.Class=7,t.Interface=8,t.Module=9,t.Property=10,t.Unit=11,t.Value=12,t.Enum=13,t.Keyword=14,t.Snippet=15,t.Color=16,t.File=17,t.Reference=18,t.Folder=19,t.EnumMember=20,t.Constant=21,t.Struct=22,t.Event=23,t.Operator=24,t.TypeParameter=25})(Cg||(Cg={}));var Ng;(function(t){t.PlainText=1,t.Snippet=2})(Ng||(Ng={}));var wg;(function(t){t.Deprecated=1})(wg||(wg={}));var _g;(function(t){function e(n,i,a){return{newText:n,insert:i,replace:a}}s(e,"create"),t.create=e;function r(n){let i=n;return i&&h.string(i.newText)&&U.is(i.insert)&&U.is(i.replace)}s(r,"is"),t.is=r})(_g||(_g={}));var bg;(function(t){t.asIs=1,t.adjustIndentation=2})(bg||(bg={}));var $g;(function(t){function e(r){let n=r;return n&&(h.string(n.detail)||n.detail===void 0)&&(h.string(n.description)||n.description===void 0)}s(e,"is"),t.is=e})($g||($g={}));var Og;(function(t){function e(r){return{label:r}}s(e,"create"),t.create=e})(Og||(Og={}));var Lg;(function(t){function e(r,n){return{items:r||[],isIncomplete:!!n}}s(e,"create"),t.create=e})(Lg||(Lg={}));var gl;(function(t){function e(n){return n.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}s(e,"fromPlainText"),t.fromPlainText=e;function r(n){let i=n;return h.string(i)||h.objectLiteral(i)&&h.string(i.language)&&h.string(i.value)}s(r,"is"),t.is=r})(gl||(gl={}));var Pg;(function(t){function e(r){let n=r;return!!n&&h.objectLiteral(n)&&(Xs.is(n.contents)||gl.is(n.contents)||h.typedArray(n.contents,gl.is))&&(r.range===void 0||U.is(r.range))}s(e,"is"),t.is=e})(Pg||(Pg={}));var Mg;(function(t){function e(r,n){return n?{label:r,documentation:n}:{label:r}}s(e,"create"),t.create=e})(Mg||(Mg={}));var Dg;(function(t){function e(r,n,...i){let a={label:r};return h.defined(n)&&(a.documentation=n),h.defined(i)?a.parameters=i:a.parameters=[],a}s(e,"create"),t.create=e})(Dg||(Dg={}));var Fg;(function(t){t.Text=1,t.Read=2,t.Write=3})(Fg||(Fg={}));var Gg;(function(t){function e(r,n){let i={range:r};return h.number(n)&&(i.kind=n),i}s(e,"create"),t.create=e})(Gg||(Gg={}));var Ug;(function(t){t.File=1,t.Module=2,t.Namespace=3,t.Package=4,t.Class=5,t.Method=6,t.Property=7,t.Field=8,t.Constructor=9,t.Enum=10,t.Interface=11,t.Function=12,t.Variable=13,t.Constant=14,t.String=15,t.Number=16,t.Boolean=17,t.Array=18,t.Object=19,t.Key=20,t.Null=21,t.EnumMember=22,t.Struct=23,t.Event=24,t.Operator=25,t.TypeParameter=26})(Ug||(Ug={}));var Bg;(function(t){t.Deprecated=1})(Bg||(Bg={}));var jg;(function(t){function e(r,n,i,a,o){let l={name:r,kind:n,location:{uri:a,range:i}};return o&&(l.containerName=o),l}s(e,"create"),t.create=e})(jg||(jg={}));var Wg;(function(t){function e(r,n,i,a){return a!==void 0?{name:r,kind:n,location:{uri:i,range:a}}:{name:r,kind:n,location:{uri:i}}}s(e,"create"),t.create=e})(Wg||(Wg={}));var Kg;(function(t){function e(n,i,a,o,l,u){let c={name:n,detail:i,kind:a,range:o,selectionRange:l};return u!==void 0&&(c.children=u),c}s(e,"create"),t.create=e;function r(n){let i=n;return i&&h.string(i.name)&&h.number(i.kind)&&U.is(i.range)&&U.is(i.selectionRange)&&(i.detail===void 0||h.string(i.detail))&&(i.deprecated===void 0||h.boolean(i.deprecated))&&(i.children===void 0||Array.isArray(i.children))&&(i.tags===void 0||Array.isArray(i.tags))}s(r,"is"),t.is=r})(Kg||(Kg={}));var Hg;(function(t){t.Empty="",t.QuickFix="quickfix",t.Refactor="refactor",t.RefactorExtract="refactor.extract",t.RefactorInline="refactor.inline",t.RefactorRewrite="refactor.rewrite",t.Source="source",t.SourceOrganizeImports="source.organizeImports",t.SourceFixAll="source.fixAll"})(Hg||(Hg={}));var yl;(function(t){t.Invoked=1,t.Automatic=2})(yl||(yl={}));var Vg;(function(t){function e(n,i,a){let o={diagnostics:n};return i!=null&&(o.only=i),a!=null&&(o.triggerKind=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.typedArray(i.diagnostics,hl.is)&&(i.only===void 0||h.typedArray(i.only,h.string))&&(i.triggerKind===void 0||i.triggerKind===yl.Invoked||i.triggerKind===yl.Automatic)}s(r,"is"),t.is=r})(Vg||(Vg={}));var zg;(function(t){function e(n,i,a){let o={title:n},l=!0;return typeof i=="string"?(l=!1,o.kind=i):Hi.is(i)?o.command=i:o.edit=i,l&&a!==void 0&&(o.kind=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return i&&h.string(i.title)&&(i.diagnostics===void 0||h.typedArray(i.diagnostics,hl.is))&&(i.kind===void 0||h.string(i.kind))&&(i.edit!==void 0||i.command!==void 0)&&(i.command===void 0||Hi.is(i.command))&&(i.isPreferred===void 0||h.boolean(i.isPreferred))&&(i.edit===void 0||Oc.is(i.edit))}s(r,"is"),t.is=r})(zg||(zg={}));var qg;(function(t){function e(n,i){let a={range:n};return h.defined(i)&&(a.data=i),a}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&U.is(i.range)&&(h.undefined(i.command)||Hi.is(i.command))}s(r,"is"),t.is=r})(qg||(qg={}));var Xg;(function(t){function e(n,i){return{tabSize:n,insertSpaces:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.uinteger(i.tabSize)&&h.boolean(i.insertSpaces)}s(r,"is"),t.is=r})(Xg||(Xg={}));var Yg;(function(t){function e(n,i,a){return{range:n,target:i,data:a}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&U.is(i.range)&&(h.undefined(i.target)||h.string(i.target))}s(r,"is"),t.is=r})(Yg||(Yg={}));var Jg;(function(t){function e(n,i){return{range:n,parent:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&U.is(i.range)&&(i.parent===void 0||t.is(i.parent))}s(r,"is"),t.is=r})(Jg||(Jg={}));var Zg;(function(t){t.namespace="namespace",t.type="type",t.class="class",t.enum="enum",t.interface="interface",t.struct="struct",t.typeParameter="typeParameter",t.parameter="parameter",t.variable="variable",t.property="property",t.enumMember="enumMember",t.event="event",t.function="function",t.method="method",t.macro="macro",t.keyword="keyword",t.modifier="modifier",t.comment="comment",t.string="string",t.number="number",t.regexp="regexp",t.operator="operator",t.decorator="decorator"})(Zg||(Zg={}));var Qg;(function(t){t.declaration="declaration",t.definition="definition",t.readonly="readonly",t.static="static",t.deprecated="deprecated",t.abstract="abstract",t.async="async",t.modification="modification",t.documentation="documentation",t.defaultLibrary="defaultLibrary"})(Qg||(Qg={}));var ey;(function(t){function e(r){let n=r;return h.objectLiteral(n)&&(n.resultId===void 0||typeof n.resultId=="string")&&Array.isArray(n.data)&&(n.data.length===0||typeof n.data[0]=="number")}s(e,"is"),t.is=e})(ey||(ey={}));var ty;(function(t){function e(n,i){return{range:n,text:i}}s(e,"create"),t.create=e;function r(n){let i=n;return i!=null&&U.is(i.range)&&h.string(i.text)}s(r,"is"),t.is=r})(ty||(ty={}));var ry;(function(t){function e(n,i,a){return{range:n,variableName:i,caseSensitiveLookup:a}}s(e,"create"),t.create=e;function r(n){let i=n;return i!=null&&U.is(i.range)&&h.boolean(i.caseSensitiveLookup)&&(h.string(i.variableName)||i.variableName===void 0)}s(r,"is"),t.is=r})(ry||(ry={}));var ny;(function(t){function e(n,i){return{range:n,expression:i}}s(e,"create"),t.create=e;function r(n){let i=n;return i!=null&&U.is(i.range)&&(h.string(i.expression)||i.expression===void 0)}s(r,"is"),t.is=r})(ny||(ny={}));var iy;(function(t){function e(n,i){return{frameId:n,stoppedLocation:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&U.is(n.stoppedLocation)}s(r,"is"),t.is=r})(iy||(iy={}));var Mc;(function(t){t.Type=1,t.Parameter=2;function e(r){return r===1||r===2}s(e,"is"),t.is=e})(Mc||(Mc={}));var Dc;(function(t){function e(n){return{value:n}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&(i.tooltip===void 0||h.string(i.tooltip)||Xs.is(i.tooltip))&&(i.location===void 0||ml.is(i.location))&&(i.command===void 0||Hi.is(i.command))}s(r,"is"),t.is=r})(Dc||(Dc={}));var sy;(function(t){function e(n,i,a){let o={position:n,label:i};return a!==void 0&&(o.kind=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&j.is(i.position)&&(h.string(i.label)||h.typedArray(i.label,Dc.is))&&(i.kind===void 0||Mc.is(i.kind))&&i.textEdits===void 0||h.typedArray(i.textEdits,Vi.is)&&(i.tooltip===void 0||h.string(i.tooltip)||Xs.is(i.tooltip))&&(i.paddingLeft===void 0||h.boolean(i.paddingLeft))&&(i.paddingRight===void 0||h.boolean(i.paddingRight))}s(r,"is"),t.is=r})(sy||(sy={}));var ay;(function(t){function e(r){return{kind:"snippet",value:r}}s(e,"createSnippet"),t.createSnippet=e})(ay||(ay={}));var oy;(function(t){function e(r,n,i,a){return{insertText:r,filterText:n,range:i,command:a}}s(e,"create"),t.create=e})(oy||(oy={}));var ly;(function(t){function e(r){return{items:r}}s(e,"create"),t.create=e})(ly||(ly={}));var uy;(function(t){t.Invoked=0,t.Automatic=1})(uy||(uy={}));var cy;(function(t){function e(r,n){return{range:r,text:n}}s(e,"create"),t.create=e})(cy||(cy={}));var fy;(function(t){function e(r,n){return{triggerKind:r,selectedCompletionInfo:n}}s(e,"create"),t.create=e})(fy||(fy={}));var dy;(function(t){function e(r){let n=r;return h.objectLiteral(n)&&kc.is(n.uri)&&h.string(n.name)}s(e,"is"),t.is=e})(dy||(dy={}));var py;(function(t){function e(a,o,l,u){return new Fc(a,o,l,u)}s(e,"create"),t.create=e;function r(a){let o=a;return!!(h.defined(o)&&h.string(o.uri)&&(h.undefined(o.languageId)||h.string(o.languageId))&&h.uinteger(o.lineCount)&&h.func(o.getText)&&h.func(o.positionAt)&&h.func(o.offsetAt))}s(r,"is"),t.is=r;function n(a,o){let l=a.getText(),u=i(o,(f,d)=>{let p=f.range.start.line-d.range.start.line;return p===0?f.range.start.character-d.range.start.character:p}),c=l.length;for(let f=u.length-1;f>=0;f--){let d=u[f],p=a.offsetAt(d.range.start),m=a.offsetAt(d.range.end);if(m<=c)l=l.substring(0,p)+d.newText+l.substring(m,l.length);else throw new Error("Overlapping edit");c=p}return l}s(n,"applyEdits"),t.applyEdits=n;function i(a,o){if(a.length<=1)return a;let l=a.length/2|0,u=a.slice(0,l),c=a.slice(l);i(u,o),i(c,o);let f=0,d=0,p=0;for(;f<u.length&&d<c.length;)o(u[f],c[d])<=0?a[p++]=u[f++]:a[p++]=c[d++];for(;f<u.length;)a[p++]=u[f++];for(;d<c.length;)a[p++]=c[d++];return a}s(i,"mergeSort")})(py||(py={}));var Fc=class{static{s(this,"FullTextDocument")}constructor(e,r,n,i){this._uri=e,this._languageId=r,this._version=n,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let r=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(r,n)}return this._content}update(e,r){this._content=e.text,this._version=r,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],r=this._content,n=!0;for(let i=0;i<r.length;i++){n&&(e.push(i),n=!1);let a=r.charAt(i);n=a==="\r"||a===`
`,a==="\r"&&i+1<r.length&&r.charAt(i+1)===`
`&&i++}n&&r.length>0&&e.push(r.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let r=this.getLineOffsets(),n=0,i=r.length;if(i===0)return j.create(0,e);for(;n<i;){let o=Math.floor((n+i)/2);r[o]>e?i=o:n=o+1}let a=n-1;return j.create(a,e-r[a])}offsetAt(e){let r=this.getLineOffsets();if(e.line>=r.length)return this._content.length;if(e.line<0)return 0;let n=r[e.line],i=e.line+1<r.length?r[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,i),n)}get lineCount(){return this.getLineOffsets().length}},h;(function(t){let e=Object.prototype.toString;function r(m){return typeof m<"u"}s(r,"defined"),t.defined=r;function n(m){return typeof m>"u"}s(n,"undefined"),t.undefined=n;function i(m){return m===!0||m===!1}s(i,"boolean"),t.boolean=i;function a(m){return e.call(m)==="[object String]"}s(a,"string"),t.string=a;function o(m){return e.call(m)==="[object Number]"}s(o,"number"),t.number=o;function l(m,g,y){return e.call(m)==="[object Number]"&&g<=m&&m<=y}s(l,"numberRange"),t.numberRange=l;function u(m){return e.call(m)==="[object Number]"&&-2147483648<=m&&m<=2147483647}s(u,"integer"),t.integer=u;function c(m){return e.call(m)==="[object Number]"&&0<=m&&m<=2147483647}s(c,"uinteger"),t.uinteger=c;function f(m){return e.call(m)==="[object Function]"}s(f,"func"),t.func=f;function d(m){return m!==null&&typeof m=="object"}s(d,"objectLiteral"),t.objectLiteral=d;function p(m,g){return Array.isArray(m)&&m.every(g)}s(p,"typedArray"),t.typedArray=p})(h||(h={}));var Ys=class{static{s(this,"CstNodeBuilder")}constructor(){this.nodeStack=[]}get current(){var e;return(e=this.nodeStack[this.nodeStack.length-1])!==null&&e!==void 0?e:this.rootNode}buildRootNode(e){return this.rootNode=new qi(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){let r=new en;return r.grammarSource=e,r.root=this.rootNode,this.current.content.push(r),this.nodeStack.push(r),r}buildLeafNode(e,r){let n=new Qr(e.startOffset,e.image.length,dn(e),e.tokenType,!r);return n.grammarSource=r,n.root=this.rootNode,this.current.content.push(n),n}removeNode(e){let r=e.container;if(r){let n=r.content.indexOf(e);n>=0&&r.content.splice(n,1)}}addHiddenNodes(e){let r=[];for(let a of e){let o=new Qr(a.startOffset,a.image.length,dn(a),a.tokenType,!0);o.root=this.rootNode,r.push(o)}let n=this.current,i=!1;if(n.content.length>0){n.content.push(...r);return}for(;n.container;){let a=n.container.content.indexOf(n);if(a>0){n.container.content.splice(a,0,...r),i=!0;break}n=n.container}i||this.rootNode.content.unshift(...r)}construct(e){let r=this.current;typeof e.$type=="string"&&(this.current.astNode=e),e.$cstNode=r;let n=this.nodeStack.pop();n?.content.length===0&&this.removeNode(n)}},Js=class{static{s(this,"AbstractCstNode")}get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,r;let n=typeof((e=this._astNode)===null||e===void 0?void 0:e.$type)=="string"?this._astNode:(r=this.container)===null||r===void 0?void 0:r.astNode;if(!n)throw new Error("This node has no associated AST element");return n}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}},Qr=class extends Js{static{s(this,"LeafCstNodeImpl")}get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,r,n,i,a=!1){super(),this._hidden=a,this._offset=e,this._tokenType=i,this._length=r,this._range=n}},en=class extends Js{static{s(this,"CompositeCstNodeImpl")}constructor(){super(...arguments),this.content=new Gc(this)}get children(){return this.content}get offset(){var e,r;return(r=(e=this.firstNonHiddenNode)===null||e===void 0?void 0:e.offset)!==null&&r!==void 0?r:0}get length(){return this.end-this.offset}get end(){var e,r;return(r=(e=this.lastNonHiddenNode)===null||e===void 0?void 0:e.end)!==null&&r!==void 0?r:0}get range(){let e=this.firstNonHiddenNode,r=this.lastNonHiddenNode;if(e&&r){if(this._rangeCache===void 0){let{range:n}=e,{range:i}=r;this._rangeCache={start:n.start,end:i.end.line<n.start.line?n.start:i.end}}return this._rangeCache}else return{start:j.create(0,0),end:j.create(0,0)}}get firstNonHiddenNode(){for(let e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){let r=this.content[e];if(!r.hidden)return r}return this.content[this.content.length-1]}},Gc=class t extends Array{static{s(this,"CstNodeContainer")}constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,t.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,r,...n){return this.addParents(n),super.splice(e,r,...n)}addParents(e){for(let r of e)r.container=this.parent}},qi=class extends en{static{s(this,"RootCstNodeImpl")}get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=e??""}};var xl=Symbol("Datatype");function Uc(t){return t.$type===xl}s(Uc,"isDataTypeNode");var my="\u200B",hy=s(t=>t.endsWith(my)?t:t+my,"withRuleSuffix"),Zs=class{static{s(this,"AbstractLangiumParser")}constructor(e){this._unorderedGroups=new Map,this.allRules=new Map,this.lexer=e.parser.Lexer;let r=this.lexer.definition,n=e.LanguageMetaData.mode==="production";this.wrapper=new Bc(r,Object.assign(Object.assign({},e.parser.ParserConfig),{skipValidations:n,errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,r){this.wrapper.wrapOr(e,r)}optional(e,r){this.wrapper.wrapOption(e,r)}many(e,r){this.wrapper.wrapMany(e,r)}atLeastOne(e,r){this.wrapper.wrapAtLeastOne(e,r)}getRule(e){return this.allRules.get(e)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}},Qs=class extends Zs{static{s(this,"LangiumParser")}get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new Ys,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,r){let n=this.computeRuleType(e),i=this.wrapper.DEFINE_RULE(hy(e.name),this.startImplementation(n,r).bind(this));return this.allRules.set(e.name,i),e.entry&&(this.mainRule=i),i}computeRuleType(e){if(!e.fragment){if(Es(e))return xl;{let r=ti(e);return r??e.name}}}parse(e,r={}){this.nodeBuilder.buildRootNode(e);let n=this.lexerResult=this.lexer.tokenize(e);this.wrapper.input=n.tokens;let i=r.rule?this.allRules.get(r.rule):this.mainRule;if(!i)throw new Error(r.rule?`No rule found with name '${r.rule}'`:"No main rule available.");let a=i.call(this.wrapper,{});return this.nodeBuilder.addHiddenNodes(n.hidden),this.unorderedGroups.clear(),this.lexerResult=void 0,{value:a,lexerErrors:n.errors,lexerReport:n.report,parserErrors:this.wrapper.errors}}startImplementation(e,r){return n=>{let i=!this.isRecording()&&e!==void 0;if(i){let o={$type:e};this.stack.push(o),e===xl&&(o.value="")}let a;try{a=r(n)}catch{a=void 0}return a===void 0&&i&&(a=this.construct()),a}}extractHiddenTokens(e){let r=this.lexerResult.hidden;if(!r.length)return[];let n=e.startOffset;for(let i=0;i<r.length;i++)if(r[i].startOffset>n)return r.splice(0,i);return r.splice(0,r.length)}consume(e,r,n){let i=this.wrapper.wrapConsume(e,r);if(!this.isRecording()&&this.isValidToken(i)){let a=this.extractHiddenTokens(i);this.nodeBuilder.addHiddenNodes(a);let o=this.nodeBuilder.buildLeafNode(i,n),{assignment:l,isCrossRef:u}=this.getAssignment(n),c=this.current;if(l){let f=ut(n)?i.image:this.converter.convert(i.image,o);this.assign(l.operator,l.feature,f,o,u)}else if(Uc(c)){let f=i.image;ut(n)||(f=this.converter.convert(f,o).toString()),c.value+=f}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&typeof e.endOffset=="number"&&!isNaN(e.endOffset)}subrule(e,r,n,i,a){let o;!this.isRecording()&&!n&&(o=this.nodeBuilder.buildCompositeNode(i));let l=this.wrapper.wrapSubrule(e,r,a);!this.isRecording()&&o&&o.length>0&&this.performSubruleAssignment(l,i,o)}performSubruleAssignment(e,r,n){let{assignment:i,isCrossRef:a}=this.getAssignment(r);if(i)this.assign(i.operator,i.feature,e,n,a);else if(!i){let o=this.current;if(Uc(o))o.value+=e.toString();else if(typeof e=="object"&&e){let u=this.assignWithoutOverride(e,o);this.stack.pop(),this.stack.push(u)}}}action(e,r){if(!this.isRecording()){let n=this.current;if(r.feature&&r.operator){n=this.construct(),this.nodeBuilder.removeNode(n.$cstNode),this.nodeBuilder.buildCompositeNode(r).content.push(n.$cstNode);let a={$type:e};this.stack.push(a),this.assign(r.operator,r.feature,n,n.$cstNode,!1)}else n.$type=e}}construct(){if(this.isRecording())return;let e=this.current;return za(e),this.nodeBuilder.construct(e),this.stack.pop(),Uc(e)?this.converter.convert(e.value,e.$cstNode):(Ru(this.astReflection,e),e)}getAssignment(e){if(!this.assignmentMap.has(e)){let r=Ur(e,gt);this.assignmentMap.set(e,{assignment:r,isCrossRef:r?Gr(r.terminal):!1})}return this.assignmentMap.get(e)}assign(e,r,n,i,a){let o=this.current,l;switch(a&&typeof n=="string"?l=this.linker.buildReference(o,r,i,n):l=n,e){case"=":{o[r]=l;break}case"?=":{o[r]=!0;break}case"+=":Array.isArray(o[r])||(o[r]=[]),o[r].push(l)}}assignWithoutOverride(e,r){for(let[i,a]of Object.entries(r)){let o=e[i];o===void 0?e[i]=a:Array.isArray(o)&&Array.isArray(a)&&(a.push(...o),e[i]=a)}let n=e.$cstNode;return n&&(n.astNode=void 0,e.$cstNode=void 0),e}get definitionErrors(){return this.wrapper.definitionErrors}},Tl=class{static{s(this,"AbstractParserErrorMessageProvider")}buildMismatchTokenMessage(e){return rr.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return rr.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return rr.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return rr.buildEarlyExitMessage(e)}},Xi=class extends Tl{static{s(this,"LangiumParserErrorMessageProvider")}buildMismatchTokenMessage({expected:e,actual:r}){return`Expecting ${e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`} but found \`${r.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}},ea=class extends Zs{static{s(this,"LangiumCompletionParser")}constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();let r=this.lexer.tokenize(e,{mode:"partial"});return this.tokens=r.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,r){let n=this.wrapper.DEFINE_RULE(hy(e.name),this.startImplementation(r).bind(this));return this.allRules.set(e.name,n),e.entry&&(this.mainRule=n),n}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return r=>{let n=this.keepStackSize();try{e(r)}finally{this.resetStackSize(n)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){let e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,r,n){this.wrapper.wrapConsume(e,r),this.isRecording()||(this.lastElementStack=[...this.elementStack,n],this.nextTokenIndex=this.currIdx+1)}subrule(e,r,n,i,a){this.before(i),this.wrapper.wrapSubrule(e,r,a),this.after(i)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){let r=this.elementStack.lastIndexOf(e);r>=0&&this.elementStack.splice(r)}}get currIdx(){return this.wrapper.currIdx}},AN={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new Xi},Bc=class extends Ks{static{s(this,"ChevrotainWrapper")}constructor(e,r){let n=r&&"maxLookahead"in r;super(e,Object.assign(Object.assign(Object.assign({},AN),{lookaheadStrategy:n?new nr({maxLookahead:r.maxLookahead}):new qs({logging:r.skipValidations?()=>{}:void 0})}),r))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,r){return this.RULE(e,r)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,r){return this.consume(e,r)}wrapSubrule(e,r,n){return this.subrule(e,r,{ARGS:[n]})}wrapOr(e,r){this.or(e,r)}wrapOption(e,r){this.option(e,r)}wrapMany(e,r){this.many(e,r)}wrapAtLeastOne(e,r){this.atLeastOne(e,r)}};function ta(t,e,r){return EN({parser:e,tokens:r,ruleNames:new Map},t),e}s(ta,"createParser");function EN(t,e){let r=Rs(e,!1),n=H(e.rules).filter(De).filter(i=>r.has(i));for(let i of n){let a=Object.assign(Object.assign({},t),{consume:1,optional:1,subrule:1,many:1,or:1});t.parser.rule(i,tn(a,i.definition))}}s(EN,"buildRules");function tn(t,e,r=!1){let n;if(ut(e))n=wN(t,e);else if(Ht(e))n=vN(t,e);else if(gt(e))n=tn(t,e.terminal);else if(Gr(e))n=gy(t,e);else if(yt(e))n=IN(t,e);else if(Ka(e))n=SN(t,e);else if(Va(e))n=CN(t,e);else if(cr(e))n=NN(t,e);else if(fu(e)){let i=t.consume++;n=s(()=>t.parser.consume(i,ot,e),"method")}else throw new Dr(e.$cstNode,`Unexpected element type: ${e.$type}`);return yy(t,r?void 0:Rl(e),n,e.cardinality)}s(tn,"buildElement");function vN(t,e){let r=vs(e);return()=>t.parser.action(r,e)}s(vN,"buildAction");function IN(t,e){let r=e.rule.ref;if(De(r)){let n=t.subrule++,i=r.fragment,a=e.arguments.length>0?kN(r,e.arguments):()=>({});return o=>t.parser.subrule(n,xy(t,r),i,e,a(o))}else if(st(r)){let n=t.consume++,i=jc(t,r.name);return()=>t.parser.consume(n,i,e)}else if(r)vt(r);else throw new Dr(e.$cstNode,`Undefined rule: ${e.rule.$refText}`)}s(IN,"buildRuleCall");function kN(t,e){let r=e.map(n=>ir(n.value));return n=>{let i={};for(let a=0;a<r.length;a++){let o=t.parameters[a],l=r[a];i[o.name]=l(n)}return i}}s(kN,"buildRuleCallPredicate");function ir(t){if(iu(t)){let e=ir(t.left),r=ir(t.right);return n=>e(n)||r(n)}else if(nu(t)){let e=ir(t.left),r=ir(t.right);return n=>e(n)&&r(n)}else if(su(t)){let e=ir(t.value);return r=>!e(r)}else if(au(t)){let e=t.parameter.ref.name;return r=>r!==void 0&&r[e]===!0}else if(ru(t)){let e=!!t.true;return()=>e}vt(t)}s(ir,"buildPredicate");function SN(t,e){if(e.elements.length===1)return tn(t,e.elements[0]);{let r=[];for(let i of e.elements){let a={ALT:tn(t,i,!0)},o=Rl(i);o&&(a.GATE=ir(o)),r.push(a)}let n=t.or++;return i=>t.parser.alternatives(n,r.map(a=>{let o={ALT:s(()=>a.ALT(i),"ALT")},l=a.GATE;return l&&(o.GATE=()=>l(i)),o}))}}s(SN,"buildAlternatives");function CN(t,e){if(e.elements.length===1)return tn(t,e.elements[0]);let r=[];for(let l of e.elements){let u={ALT:tn(t,l,!0)},c=Rl(l);c&&(u.GATE=ir(c)),r.push(u)}let n=t.or++,i=s((l,u)=>{let c=u.getRuleStack().join("-");return`uGroup_${l}_${c}`},"idFunc"),a=s(l=>t.parser.alternatives(n,r.map((u,c)=>{let f={ALT:s(()=>!0,"ALT")},d=t.parser;f.ALT=()=>{if(u.ALT(l),!d.isRecording()){let m=i(n,d);d.unorderedGroups.get(m)||d.unorderedGroups.set(m,[]);let g=d.unorderedGroups.get(m);typeof g?.[c]>"u"&&(g[c]=!0)}};let p=u.GATE;return p?f.GATE=()=>p(l):f.GATE=()=>{let m=d.unorderedGroups.get(i(n,d));return!m?.[c]},f})),"alternatives"),o=yy(t,Rl(e),a,"*");return l=>{o(l),t.parser.isRecording()||t.parser.unorderedGroups.delete(i(n,t.parser))}}s(CN,"buildUnorderedGroup");function NN(t,e){let r=e.elements.map(n=>tn(t,n));return n=>r.forEach(i=>i(n))}s(NN,"buildGroup");function Rl(t){if(cr(t))return t.guardCondition}s(Rl,"getGuardCondition");function gy(t,e,r=e.terminal){if(r)if(yt(r)&&De(r.rule.ref)){let n=r.rule.ref,i=t.subrule++;return a=>t.parser.subrule(i,xy(t,n),!1,e,a)}else if(yt(r)&&st(r.rule.ref)){let n=t.consume++,i=jc(t,r.rule.ref.name);return()=>t.parser.consume(n,i,e)}else if(ut(r)){let n=t.consume++,i=jc(t,r.value);return()=>t.parser.consume(n,i,e)}else throw new Error("Could not build cross reference parser");else{if(!e.type.ref)throw new Error("Could not resolve reference to type: "+e.type.$refText);let n=Za(e.type.ref),i=n?.terminal;if(!i)throw new Error("Could not find name assignment for type: "+vs(e.type.ref));return gy(t,e,i)}}s(gy,"buildCrossReference");function wN(t,e){let r=t.consume++,n=t.tokens[e.value];if(!n)throw new Error("Could not find token for keyword: "+e.value);return()=>t.parser.consume(r,n,e)}s(wN,"buildKeyword");function yy(t,e,r,n){let i=e&&ir(e);if(!n)if(i){let a=t.or++;return o=>t.parser.alternatives(a,[{ALT:s(()=>r(o),"ALT"),GATE:s(()=>i(o),"GATE")},{ALT:cl(),GATE:s(()=>!i(o),"GATE")}])}else return r;if(n==="*"){let a=t.many++;return o=>t.parser.many(a,{DEF:s(()=>r(o),"DEF"),GATE:i?()=>i(o):void 0})}else if(n==="+"){let a=t.many++;if(i){let o=t.or++;return l=>t.parser.alternatives(o,[{ALT:s(()=>t.parser.atLeastOne(a,{DEF:s(()=>r(l),"DEF")}),"ALT"),GATE:s(()=>i(l),"GATE")},{ALT:cl(),GATE:s(()=>!i(l),"GATE")}])}else return o=>t.parser.atLeastOne(a,{DEF:s(()=>r(o),"DEF")})}else if(n==="?"){let a=t.optional++;return o=>t.parser.optional(a,{DEF:s(()=>r(o),"DEF"),GATE:i?()=>i(o):void 0})}else vt(n)}s(yy,"wrap");function xy(t,e){let r=_N(t,e),n=t.parser.getRule(r);if(!n)throw new Error(`Rule "${r}" not found."`);return n}s(xy,"getRule");function _N(t,e){if(De(e))return e.name;if(t.ruleNames.has(e))return t.ruleNames.get(e);{let r=e,n=r.$container,i=e.$type;for(;!De(n);)(cr(n)||Ka(n)||Va(n))&&(i=n.elements.indexOf(r).toString()+":"+i),r=n,n=n.$container;return i=n.name+":"+i,t.ruleNames.set(e,i),i}}s(_N,"getRuleName");function jc(t,e){let r=t.tokens[e];if(!r)throw new Error(`Token "${e}" not found."`);return r}s(jc,"getToken");function Wc(t){let e=t.Grammar,r=t.parser.Lexer,n=new ea(t);return ta(e,n,r.definition),n.finalize(),n}s(Wc,"createCompletionParser");function Kc(t){let e=Ty(t);return e.finalize(),e}s(Kc,"createLangiumParser");function Ty(t){let e=t.Grammar,r=t.parser.Lexer,n=new Qs(t);return ta(e,n,r.definition)}s(Ty,"prepareLangiumParser");var sr=class{static{s(this,"DefaultTokenBuilder")}constructor(){this.diagnostics=[]}buildTokens(e,r){let n=H(Rs(e,!1)),i=this.buildTerminalTokens(n),a=this.buildKeywordTokens(n,i,r);return i.forEach(o=>{let l=o.PATTERN;typeof l=="object"&&l&&"test"in l&&ei(l)?a.unshift(o):a.push(o)}),a}flushLexingReport(e){return{diagnostics:this.popDiagnostics()}}popDiagnostics(){let e=[...this.diagnostics];return this.diagnostics=[],e}buildTerminalTokens(e){return e.filter(st).filter(r=>!r.fragment).map(r=>this.buildTerminalToken(r)).toArray()}buildTerminalToken(e){let r=ri(e),n=this.requiresCustomPattern(r)?this.regexPatternFunction(r):r,i={name:e.name,PATTERN:n};return typeof n=="function"&&(i.LINE_BREAKS=!0),e.hidden&&(i.GROUP=ei(r)?ue.SKIPPED:"hidden"),i}requiresCustomPattern(e){return e.flags.includes("u")||e.flags.includes("s")?!0:!!(e.source.includes("?<=")||e.source.includes("?<!"))}regexPatternFunction(e){let r=new RegExp(e,e.flags+"y");return(n,i)=>(r.lastIndex=i,r.exec(n))}buildKeywordTokens(e,r,n){return e.filter(De).flatMap(i=>It(i).filter(ut)).distinct(i=>i.value).toArray().sort((i,a)=>a.value.length-i.value.length).map(i=>this.buildKeywordToken(i,r,!!n?.caseInsensitive))}buildKeywordToken(e,r,n){let i=this.buildKeywordPattern(e,n),a={name:e.value,PATTERN:i,LONGER_ALT:this.findLongerAlt(e,r)};return typeof i=="function"&&(a.LINE_BREAKS=!0),a}buildKeywordPattern(e,r){return r?new RegExp(Su(e.value)):e.value}findLongerAlt(e,r){return r.reduce((n,i)=>{let a=i?.PATTERN;return a?.source&&Cu("^"+a.source+"$",e.value)&&n.push(i),n},[])}};var rn=class{static{s(this,"DefaultValueConverter")}convert(e,r){let n=r.grammarSource;if(Gr(n)&&(n=_u(n)),yt(n)){let i=n.rule.ref;if(!i)throw new Error("This cst node was not parsed by a rule.");return this.runConverter(i,e,r)}return e}runConverter(e,r,n){var i;switch(e.name.toUpperCase()){case"INT":return Ft.convertInt(r);case"STRING":return Ft.convertString(r);case"ID":return Ft.convertID(r)}switch((i=Du(e))===null||i===void 0?void 0:i.toLowerCase()){case"number":return Ft.convertNumber(r);case"boolean":return Ft.convertBoolean(r);case"bigint":return Ft.convertBigint(r);case"date":return Ft.convertDate(r);default:return r}}},Ft;(function(t){function e(c){let f="";for(let d=1;d<c.length-1;d++){let p=c.charAt(d);if(p==="\\"){let m=c.charAt(++d);f+=r(m)}else f+=p}return f}s(e,"convertString"),t.convertString=e;function r(c){switch(c){case"b":return"\b";case"f":return"\f";case"n":return`
`;case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\0";default:return c}}s(r,"convertEscapeCharacter");function n(c){return c.charAt(0)==="^"?c.substring(1):c}s(n,"convertID"),t.convertID=n;function i(c){return parseInt(c)}s(i,"convertInt"),t.convertInt=i;function a(c){return BigInt(c)}s(a,"convertBigint"),t.convertBigint=a;function o(c){return new Date(c)}s(o,"convertDate"),t.convertDate=o;function l(c){return Number(c)}s(l,"convertNumber"),t.convertNumber=l;function u(c){return c.toLowerCase()==="true"}s(u,"convertBoolean"),t.convertBoolean=u})(Ft||(Ft={}));var _={};B(_,Of(Iy(),1));function Qc(){return new Promise(t=>{typeof setImmediate>"u"?setTimeout(t,0):setImmediate(t)})}s(Qc,"delayNextTick");var Il=0,ky=10;function kl(){return Il=performance.now(),new _.CancellationTokenSource}s(kl,"startCancelableOperation");function Sy(t){ky=t}s(Sy,"setInterruptionPeriod");var Gt=Symbol("OperationCancelled");function Ut(t){return t===Gt}s(Ut,"isOperationCancelled");async function Te(t){if(t===_.CancellationToken.None)return;let e=performance.now();if(e-Il>=ky&&(Il=e,await Qc(),Il=performance.now()),t.isCancellationRequested)throw Gt}s(Te,"interruptAndCheck");var Xe=class{static{s(this,"Deferred")}constructor(){this.promise=new Promise((e,r)=>{this.resolve=n=>(e(n),this),this.reject=n=>(r(n),this)})}};var Sl=class t{static{s(this,"FullTextDocument")}constructor(e,r,n,i){this._uri=e,this._languageId=r,this._version=n,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let r=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(r,n)}return this._content}update(e,r){for(let n of e)if(t.isIncremental(n)){let i=wy(n.range),a=this.offsetAt(i.start),o=this.offsetAt(i.end);this._content=this._content.substring(0,a)+n.text+this._content.substring(o,this._content.length);let l=Math.max(i.start.line,0),u=Math.max(i.end.line,0),c=this._lineOffsets,f=Cy(n.text,!1,a);if(u-l===f.length)for(let p=0,m=f.length;p<m;p++)c[p+l+1]=f[p];else f.length<1e4?c.splice(l+1,u-l,...f):this._lineOffsets=c=c.slice(0,l+1).concat(f,c.slice(u+1));let d=n.text.length-(o-a);if(d!==0)for(let p=l+1+f.length,m=c.length;p<m;p++)c[p]=c[p]+d}else if(t.isFull(n))this._content=n.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=r}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=Cy(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let r=this.getLineOffsets(),n=0,i=r.length;if(i===0)return{line:0,character:e};for(;n<i;){let o=Math.floor((n+i)/2);r[o]>e?i=o:n=o+1}let a=n-1;return e=this.ensureBeforeEOL(e,r[a]),{line:a,character:e-r[a]}}offsetAt(e){let r=this.getLineOffsets();if(e.line>=r.length)return this._content.length;if(e.line<0)return 0;let n=r[e.line];if(e.character<=0)return n;let i=e.line+1<r.length?r[e.line+1]:this._content.length,a=Math.min(n+e.character,i);return this.ensureBeforeEOL(a,n)}ensureBeforeEOL(e,r){for(;e>r&&Ny(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){let r=e;return r!=null&&typeof r.text=="string"&&r.range!==void 0&&(r.rangeLength===void 0||typeof r.rangeLength=="number")}static isFull(e){let r=e;return r!=null&&typeof r.text=="string"&&r.range===void 0&&r.rangeLength===void 0}},Zi;(function(t){function e(i,a,o,l){return new Sl(i,a,o,l)}s(e,"create"),t.create=e;function r(i,a,o){if(i instanceof Sl)return i.update(a,o),i;throw new Error("TextDocument.update: document must be created by TextDocument.create")}s(r,"update"),t.update=r;function n(i,a){let o=i.getText(),l=ef(a.map(UN),(f,d)=>{let p=f.range.start.line-d.range.start.line;return p===0?f.range.start.character-d.range.start.character:p}),u=0,c=[];for(let f of l){let d=i.offsetAt(f.range.start);if(d<u)throw new Error("Overlapping edit");d>u&&c.push(o.substring(u,d)),f.newText.length&&c.push(f.newText),u=i.offsetAt(f.range.end)}return c.push(o.substr(u)),c.join("")}s(n,"applyEdits"),t.applyEdits=n})(Zi||(Zi={}));function ef(t,e){if(t.length<=1)return t;let r=t.length/2|0,n=t.slice(0,r),i=t.slice(r);ef(n,e),ef(i,e);let a=0,o=0,l=0;for(;a<n.length&&o<i.length;)e(n[a],i[o])<=0?t[l++]=n[a++]:t[l++]=i[o++];for(;a<n.length;)t[l++]=n[a++];for(;o<i.length;)t[l++]=i[o++];return t}s(ef,"mergeSort");function Cy(t,e,r=0){let n=e?[r]:[];for(let i=0;i<t.length;i++){let a=t.charCodeAt(i);Ny(a)&&(a===13&&i+1<t.length&&t.charCodeAt(i+1)===10&&i++,n.push(r+i+1))}return n}s(Cy,"computeLineOffsets");function Ny(t){return t===13||t===10}s(Ny,"isEOL");function wy(t){let e=t.start,r=t.end;return e.line>r.line||e.line===r.line&&e.character>r.character?{start:r,end:e}:t}s(wy,"getWellformedRange");function UN(t){let e=wy(t.range);return e!==t.range?{newText:t.newText,range:e}:t}s(UN,"getWellformedEdit");var _y;(()=>{"use strict";var t={470:i=>{function a(u){if(typeof u!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(u))}s(a,"e");function o(u,c){for(var f,d="",p=0,m=-1,g=0,y=0;y<=u.length;++y){if(y<u.length)f=u.charCodeAt(y);else{if(f===47)break;f=47}if(f===47){if(!(m===y-1||g===1))if(m!==y-1&&g===2){if(d.length<2||p!==2||d.charCodeAt(d.length-1)!==46||d.charCodeAt(d.length-2)!==46){if(d.length>2){var A=d.lastIndexOf("/");if(A!==d.length-1){A===-1?(d="",p=0):p=(d=d.slice(0,A)).length-1-d.lastIndexOf("/"),m=y,g=0;continue}}else if(d.length===2||d.length===1){d="",p=0,m=y,g=0;continue}}c&&(d.length>0?d+="/..":d="..",p=2)}else d.length>0?d+="/"+u.slice(m+1,y):d=u.slice(m+1,y),p=y-m-1;m=y,g=0}else f===46&&g!==-1?++g:g=-1}return d}s(o,"r");var l={resolve:s(function(){for(var u,c="",f=!1,d=arguments.length-1;d>=-1&&!f;d--){var p;d>=0?p=arguments[d]:(u===void 0&&(u=process.cwd()),p=u),a(p),p.length!==0&&(c=p+"/"+c,f=p.charCodeAt(0)===47)}return c=o(c,!f),f?c.length>0?"/"+c:"/":c.length>0?c:"."},"resolve"),normalize:s(function(u){if(a(u),u.length===0)return".";var c=u.charCodeAt(0)===47,f=u.charCodeAt(u.length-1)===47;return(u=o(u,!c)).length!==0||c||(u="."),u.length>0&&f&&(u+="/"),c?"/"+u:u},"normalize"),isAbsolute:s(function(u){return a(u),u.length>0&&u.charCodeAt(0)===47},"isAbsolute"),join:s(function(){if(arguments.length===0)return".";for(var u,c=0;c<arguments.length;++c){var f=arguments[c];a(f),f.length>0&&(u===void 0?u=f:u+="/"+f)}return u===void 0?".":l.normalize(u)},"join"),relative:s(function(u,c){if(a(u),a(c),u===c||(u=l.resolve(u))===(c=l.resolve(c)))return"";for(var f=1;f<u.length&&u.charCodeAt(f)===47;++f);for(var d=u.length,p=d-f,m=1;m<c.length&&c.charCodeAt(m)===47;++m);for(var g=c.length-m,y=p<g?p:g,A=-1,T=0;T<=y;++T){if(T===y){if(g>y){if(c.charCodeAt(m+T)===47)return c.slice(m+T+1);if(T===0)return c.slice(m+T)}else p>y&&(u.charCodeAt(f+T)===47?A=T:T===0&&(A=0));break}var E=u.charCodeAt(f+T);if(E!==c.charCodeAt(m+T))break;E===47&&(A=T)}var R="";for(T=f+A+1;T<=d;++T)T!==d&&u.charCodeAt(T)!==47||(R.length===0?R+="..":R+="/..");return R.length>0?R+c.slice(m+A):(m+=A,c.charCodeAt(m)===47&&++m,c.slice(m))},"relative"),_makeLong:s(function(u){return u},"_makeLong"),dirname:s(function(u){if(a(u),u.length===0)return".";for(var c=u.charCodeAt(0),f=c===47,d=-1,p=!0,m=u.length-1;m>=1;--m)if((c=u.charCodeAt(m))===47){if(!p){d=m;break}}else p=!1;return d===-1?f?"/":".":f&&d===1?"//":u.slice(0,d)},"dirname"),basename:s(function(u,c){if(c!==void 0&&typeof c!="string")throw new TypeError('"ext" argument must be a string');a(u);var f,d=0,p=-1,m=!0;if(c!==void 0&&c.length>0&&c.length<=u.length){if(c.length===u.length&&c===u)return"";var g=c.length-1,y=-1;for(f=u.length-1;f>=0;--f){var A=u.charCodeAt(f);if(A===47){if(!m){d=f+1;break}}else y===-1&&(m=!1,y=f+1),g>=0&&(A===c.charCodeAt(g)?--g==-1&&(p=f):(g=-1,p=y))}return d===p?p=y:p===-1&&(p=u.length),u.slice(d,p)}for(f=u.length-1;f>=0;--f)if(u.charCodeAt(f)===47){if(!m){d=f+1;break}}else p===-1&&(m=!1,p=f+1);return p===-1?"":u.slice(d,p)},"basename"),extname:s(function(u){a(u);for(var c=-1,f=0,d=-1,p=!0,m=0,g=u.length-1;g>=0;--g){var y=u.charCodeAt(g);if(y!==47)d===-1&&(p=!1,d=g+1),y===46?c===-1?c=g:m!==1&&(m=1):c!==-1&&(m=-1);else if(!p){f=g+1;break}}return c===-1||d===-1||m===0||m===1&&c===d-1&&c===f+1?"":u.slice(c,d)},"extname"),format:s(function(u){if(u===null||typeof u!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof u);return function(c,f){var d=f.dir||f.root,p=f.base||(f.name||"")+(f.ext||"");return d?d===f.root?d+p:d+"/"+p:p}(0,u)},"format"),parse:s(function(u){a(u);var c={root:"",dir:"",base:"",ext:"",name:""};if(u.length===0)return c;var f,d=u.charCodeAt(0),p=d===47;p?(c.root="/",f=1):f=0;for(var m=-1,g=0,y=-1,A=!0,T=u.length-1,E=0;T>=f;--T)if((d=u.charCodeAt(T))!==47)y===-1&&(A=!1,y=T+1),d===46?m===-1?m=T:E!==1&&(E=1):m!==-1&&(E=-1);else if(!A){g=T+1;break}return m===-1||y===-1||E===0||E===1&&m===y-1&&m===g+1?y!==-1&&(c.base=c.name=g===0&&p?u.slice(1,y):u.slice(g,y)):(g===0&&p?(c.name=u.slice(1,m),c.base=u.slice(1,y)):(c.name=u.slice(g,m),c.base=u.slice(g,y)),c.ext=u.slice(m,y)),g>0?c.dir=u.slice(0,g-1):p&&(c.dir="/"),c},"parse"),sep:"/",delimiter:":",win32:null,posix:null};l.posix=l,i.exports=l}},e={};function r(i){var a=e[i];if(a!==void 0)return a.exports;var o=e[i]={exports:{}};return t[i](o,o.exports,r),o.exports}s(r,"r"),r.d=(i,a)=>{for(var o in a)r.o(a,o)&&!r.o(i,o)&&Object.defineProperty(i,o,{enumerable:!0,get:a[o]})},r.o=(i,a)=>Object.prototype.hasOwnProperty.call(i,a),r.r=i=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})};var n={};(()=>{let i;r.r(n),r.d(n,{URI:s(()=>p,"URI"),Utils:s(()=>jt,"Utils")}),typeof process=="object"?i=process.platform==="win32":typeof navigator=="object"&&(i=navigator.userAgent.indexOf("Windows")>=0);let a=/^\w[\w\d+.-]*$/,o=/^\//,l=/^\/\//;function u(I,x){if(!I.scheme&&x)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${I.authority}", path: "${I.path}", query: "${I.query}", fragment: "${I.fragment}"}`);if(I.scheme&&!a.test(I.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(I.path){if(I.authority){if(!o.test(I.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(l.test(I.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}s(u,"s");let c="",f="/",d=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class p{static{s(this,"f")}static isUri(x){return x instanceof p||!!x&&typeof x.authority=="string"&&typeof x.fragment=="string"&&typeof x.path=="string"&&typeof x.query=="string"&&typeof x.scheme=="string"&&typeof x.fsPath=="string"&&typeof x.with=="function"&&typeof x.toString=="function"}scheme;authority;path;query;fragment;constructor(x,w,C,K,M,P=!1){typeof x=="object"?(this.scheme=x.scheme||c,this.authority=x.authority||c,this.path=x.path||c,this.query=x.query||c,this.fragment=x.fragment||c):(this.scheme=function(je,We){return je||We?je:"file"}(x,P),this.authority=w||c,this.path=function(je,We){switch(je){case"https":case"http":case"file":We?We[0]!==f&&(We=f+We):We=f}return We}(this.scheme,C||c),this.query=K||c,this.fragment=M||c,u(this,P))}get fsPath(){return E(this,!1)}with(x){if(!x)return this;let{scheme:w,authority:C,path:K,query:M,fragment:P}=x;return w===void 0?w=this.scheme:w===null&&(w=c),C===void 0?C=this.authority:C===null&&(C=c),K===void 0?K=this.path:K===null&&(K=c),M===void 0?M=this.query:M===null&&(M=c),P===void 0?P=this.fragment:P===null&&(P=c),w===this.scheme&&C===this.authority&&K===this.path&&M===this.query&&P===this.fragment?this:new g(w,C,K,M,P)}static parse(x,w=!1){let C=d.exec(x);return C?new g(C[2]||c,Se(C[4]||c),Se(C[5]||c),Se(C[7]||c),Se(C[9]||c),w):new g(c,c,c,c,c)}static file(x){let w=c;if(i&&(x=x.replace(/\\/g,f)),x[0]===f&&x[1]===f){let C=x.indexOf(f,2);C===-1?(w=x.substring(2),x=f):(w=x.substring(2,C),x=x.substring(C)||f)}return new g("file",w,x,c,c)}static from(x){let w=new g(x.scheme,x.authority,x.path,x.query,x.fragment);return u(w,!0),w}toString(x=!1){return R(this,x)}toJSON(){return this}static revive(x){if(x){if(x instanceof p)return x;{let w=new g(x);return w._formatted=x.external,w._fsPath=x._sep===m?x.fsPath:null,w}}return x}}let m=i?1:void 0;class g extends p{static{s(this,"l")}_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=E(this,!1)),this._fsPath}toString(x=!1){return x?R(this,!0):(this._formatted||(this._formatted=R(this,!1)),this._formatted)}toJSON(){let x={$mid:1};return this._fsPath&&(x.fsPath=this._fsPath,x._sep=m),this._formatted&&(x.external=this._formatted),this.path&&(x.path=this.path),this.scheme&&(x.scheme=this.scheme),this.authority&&(x.authority=this.authority),this.query&&(x.query=this.query),this.fragment&&(x.fragment=this.fragment),x}}let y={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function A(I,x,w){let C,K=-1;for(let M=0;M<I.length;M++){let P=I.charCodeAt(M);if(P>=97&&P<=122||P>=65&&P<=90||P>=48&&P<=57||P===45||P===46||P===95||P===126||x&&P===47||w&&P===91||w&&P===93||w&&P===58)K!==-1&&(C+=encodeURIComponent(I.substring(K,M)),K=-1),C!==void 0&&(C+=I.charAt(M));else{C===void 0&&(C=I.substr(0,M));let je=y[P];je!==void 0?(K!==-1&&(C+=encodeURIComponent(I.substring(K,M)),K=-1),C+=je):K===-1&&(K=M)}}return K!==-1&&(C+=encodeURIComponent(I.substring(K))),C!==void 0?C:I}s(A,"d");function T(I){let x;for(let w=0;w<I.length;w++){let C=I.charCodeAt(w);C===35||C===63?(x===void 0&&(x=I.substr(0,w)),x+=y[C]):x!==void 0&&(x+=I[w])}return x!==void 0?x:I}s(T,"p");function E(I,x){let w;return w=I.authority&&I.path.length>1&&I.scheme==="file"?`//${I.authority}${I.path}`:I.path.charCodeAt(0)===47&&(I.path.charCodeAt(1)>=65&&I.path.charCodeAt(1)<=90||I.path.charCodeAt(1)>=97&&I.path.charCodeAt(1)<=122)&&I.path.charCodeAt(2)===58?x?I.path.substr(1):I.path[1].toLowerCase()+I.path.substr(2):I.path,i&&(w=w.replace(/\//g,"\\")),w}s(E,"m");function R(I,x){let w=x?T:A,C="",{scheme:K,authority:M,path:P,query:je,fragment:We}=I;if(K&&(C+=K,C+=":"),(M||K==="file")&&(C+=f,C+=f),M){let ie=M.indexOf("@");if(ie!==-1){let _r=M.substr(0,ie);M=M.substr(ie+1),ie=_r.lastIndexOf(":"),ie===-1?C+=w(_r,!1,!1):(C+=w(_r.substr(0,ie),!1,!1),C+=":",C+=w(_r.substr(ie+1),!1,!0)),C+="@"}M=M.toLowerCase(),ie=M.lastIndexOf(":"),ie===-1?C+=w(M,!1,!0):(C+=w(M.substr(0,ie),!1,!0),C+=M.substr(ie))}if(P){if(P.length>=3&&P.charCodeAt(0)===47&&P.charCodeAt(2)===58){let ie=P.charCodeAt(1);ie>=65&&ie<=90&&(P=`/${String.fromCharCode(ie+32)}:${P.substr(3)}`)}else if(P.length>=2&&P.charCodeAt(1)===58){let ie=P.charCodeAt(0);ie>=65&&ie<=90&&(P=`${String.fromCharCode(ie+32)}:${P.substr(2)}`)}C+=w(P,!0,!1)}return je&&(C+="?",C+=w(je,!1,!1)),We&&(C+="#",C+=x?We:A(We,!1,!1)),C}s(R,"y");function O(I){try{return decodeURIComponent(I)}catch{return I.length>3?I.substr(0,3)+O(I.substr(3)):I}}s(O,"v");let L=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function Se(I){return I.match(L)?I.replace(L,x=>O(x)):I}s(Se,"C");var un=r(470);let Le=un.posix||un,or="/";var jt;(function(I){I.joinPath=function(x,...w){return x.with({path:Le.join(x.path,...w)})},I.resolvePath=function(x,...w){let C=x.path,K=!1;C[0]!==or&&(C=or+C,K=!0);let M=Le.resolve(C,...w);return K&&M[0]===or&&!x.authority&&(M=M.substring(1)),x.with({path:M})},I.dirname=function(x){if(x.path.length===0||x.path===or)return x;let w=Le.dirname(x.path);return w.length===1&&w.charCodeAt(0)===46&&(w=""),x.with({path:w})},I.basename=function(x){return Le.basename(x.path)},I.extname=function(x){return Le.extname(x.path)}})(jt||(jt={}))})(),_y=n})();var{URI:Ye,Utils:Qi}=_y;var Je;(function(t){t.basename=Qi.basename,t.dirname=Qi.dirname,t.extname=Qi.extname,t.joinPath=Qi.joinPath,t.resolvePath=Qi.resolvePath;function e(i,a){return i?.toString()===a?.toString()}s(e,"equals"),t.equals=e;function r(i,a){let o=typeof i=="string"?i:i.path,l=typeof a=="string"?a:a.path,u=o.split("/").filter(m=>m.length>0),c=l.split("/").filter(m=>m.length>0),f=0;for(;f<u.length&&u[f]===c[f];f++);let d="../".repeat(u.length-f),p=c.slice(f).join("/");return d+p}s(r,"relative"),t.relative=r;function n(i){return Ye.parse(i.toString()).toString()}s(n,"normalize"),t.normalize=n})(Je||(Je={}));var J;(function(t){t[t.Changed=0]="Changed",t[t.Parsed=1]="Parsed",t[t.IndexedContent=2]="IndexedContent",t[t.ComputedScopes=3]="ComputedScopes",t[t.Linked=4]="Linked",t[t.IndexedReferences=5]="IndexedReferences",t[t.Validated=6]="Validated"})(J||(J={}));var ra=class{static{s(this,"DefaultLangiumDocumentFactory")}constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,r=_.CancellationToken.None){let n=await this.fileSystemProvider.readFile(e);return this.createAsync(e,n,r)}fromTextDocument(e,r,n){return r=r??Ye.parse(e.uri),_.CancellationToken.is(n)?this.createAsync(r,e,n):this.create(r,e,n)}fromString(e,r,n){return _.CancellationToken.is(n)?this.createAsync(r,e,n):this.create(r,e,n)}fromModel(e,r){return this.create(r,{$model:e})}create(e,r,n){if(typeof r=="string"){let i=this.parse(e,r,n);return this.createLangiumDocument(i,e,void 0,r)}else if("$model"in r){let i={value:r.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(i,e)}else{let i=this.parse(e,r.getText(),n);return this.createLangiumDocument(i,e,r)}}async createAsync(e,r,n){if(typeof r=="string"){let i=await this.parseAsync(e,r,n);return this.createLangiumDocument(i,e,void 0,r)}else{let i=await this.parseAsync(e,r.getText(),n);return this.createLangiumDocument(i,e,r)}}createLangiumDocument(e,r,n,i){let a;if(n)a={parseResult:e,uri:r,state:J.Parsed,references:[],textDocument:n};else{let o=this.createTextDocumentGetter(r,i);a={parseResult:e,uri:r,state:J.Parsed,references:[],get textDocument(){return o()}}}return e.value.$document=a,a}async update(e,r){var n,i;let a=(n=e.parseResult.value.$cstNode)===null||n===void 0?void 0:n.root.fullText,o=(i=this.textDocuments)===null||i===void 0?void 0:i.get(e.uri.toString()),l=o?o.getText():await this.fileSystemProvider.readFile(e.uri);if(o)Object.defineProperty(e,"textDocument",{value:o});else{let u=this.createTextDocumentGetter(e.uri,l);Object.defineProperty(e,"textDocument",{get:u})}return a!==l&&(e.parseResult=await this.parseAsync(e.uri,l,r),e.parseResult.value.$document=e),e.state=J.Parsed,e}parse(e,r,n){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(r,n)}parseAsync(e,r,n){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(r,n)}createTextDocumentGetter(e,r){let n=this.serviceRegistry,i;return()=>i??(i=Zi.create(e.toString(),n.getServices(e).LanguageMetaData.languageId,0,r??""))}},na=class{static{s(this,"DefaultLangiumDocuments")}constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.serviceRegistry=e.ServiceRegistry}get all(){return H(this.documentMap.values())}addDocument(e){let r=e.uri.toString();if(this.documentMap.has(r))throw new Error(`A document with the URI '${r}' is already present.`);this.documentMap.set(r,e)}getDocument(e){let r=e.toString();return this.documentMap.get(r)}async getOrCreateDocument(e,r){let n=this.getDocument(e);return n||(n=await this.langiumDocumentFactory.fromUri(e,r),this.addDocument(n),n)}createDocument(e,r,n){if(n)return this.langiumDocumentFactory.fromString(r,e,n).then(i=>(this.addDocument(i),i));{let i=this.langiumDocumentFactory.fromString(r,e);return this.addDocument(i),i}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){let r=e.toString(),n=this.documentMap.get(r);return n&&(this.serviceRegistry.getServices(e).references.Linker.unlink(n),n.state=J.Changed,n.precomputedScopes=void 0,n.diagnostics=void 0),n}deleteDocument(e){let r=e.toString(),n=this.documentMap.get(r);return n&&(n.state=J.Changed,this.documentMap.delete(r)),n}};var tf=Symbol("ref_resolving"),ia=class{static{s(this,"DefaultLinker")}constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,r=_.CancellationToken.None){for(let n of ct(e.parseResult.value))await Te(r),Zn(n).forEach(i=>this.doLink(i,e))}doLink(e,r){var n;let i=e.reference;if(i._ref===void 0){i._ref=tf;try{let a=this.getCandidate(e);if(Lr(a))i._ref=a;else if(i._nodeDescription=a,this.langiumDocuments().hasDocument(a.documentUri)){let o=this.loadAstNode(a);i._ref=o??this.createLinkingError(e,a)}else i._ref=void 0}catch(a){console.error(`An error occurred while resolving reference to '${i.$refText}':`,a);let o=(n=a.message)!==null&&n!==void 0?n:String(a);i._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${i.$refText}': ${o}`})}r.references.push(i)}}unlink(e){for(let r of e.references)delete r._ref,delete r._nodeDescription;e.references=[]}getCandidate(e){let n=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return n??this.createLinkingError(e)}buildReference(e,r,n,i){let a=this,o={$refNode:n,$refText:i,get ref(){var l;if(fe(this._ref))return this._ref;if(Xl(this._nodeDescription)){let u=a.loadAstNode(this._nodeDescription);this._ref=u??a.createLinkingError({reference:o,container:e,property:r},this._nodeDescription)}else if(this._ref===void 0){this._ref=tf;let u=hs(e).$document,c=a.getLinkedNode({reference:o,container:e,property:r});if(c.error&&u&&u.state<J.ComputedScopes)return this._ref=void 0;this._ref=(l=c.node)!==null&&l!==void 0?l:c.error,this._nodeDescription=c.descr,u?.references.push(this)}else if(this._ref===tf)throw new Error(`Cyclic reference resolution detected: ${a.astNodeLocator.getAstNodePath(e)}/${r} (symbol '${i}')`);return fe(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return Lr(this._ref)?this._ref:void 0}};return o}getLinkedNode(e){var r;try{let n=this.getCandidate(e);if(Lr(n))return{error:n};let i=this.loadAstNode(n);return i?{node:i,descr:n}:{descr:n,error:this.createLinkingError(e,n)}}catch(n){console.error(`An error occurred while resolving reference to '${e.reference.$refText}':`,n);let i=(r=n.message)!==null&&r!==void 0?r:String(n);return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${i}`})}}}loadAstNode(e){if(e.node)return e.node;let r=this.langiumDocuments().getDocument(e.documentUri);if(r)return this.astNodeLocator.getAstNode(r.parseResult.value,e.path)}createLinkingError(e,r){let n=hs(e.container).$document;n&&n.state<J.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${n.uri}).`);let i=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${i} named '${e.reference.$refText}'.`,targetDescription:r})}};function by(t){return typeof t.name=="string"}s(by,"isNamed");var sa=class{static{s(this,"DefaultNameProvider")}getName(e){if(by(e))return e.name}getNameNode(e){return As(e.$cstNode,"name")}};var aa=class{static{s(this,"DefaultReferences")}constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){let r=Mu(e),n=e.astNode;if(r&&n){let i=n[r.feature];if(be(i))return i.ref;if(Array.isArray(i)){for(let a of i)if(be(a)&&a.$refNode&&a.$refNode.offset<=e.offset&&a.$refNode.end>=e.end)return a.ref}}if(n){let i=this.nameProvider.getNameNode(n);if(i&&(i===e||Jl(e,i)))return n}}}findDeclarationNode(e){let r=this.findDeclaration(e);if(r?.$cstNode){let n=this.nameProvider.getNameNode(r);return n??r.$cstNode}}findReferences(e,r){let n=[];if(r.includeDeclaration){let a=this.getReferenceToSelf(e);a&&n.push(a)}let i=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return r.documentUri&&(i=i.filter(a=>Je.equals(a.sourceUri,r.documentUri))),n.push(...i),H(n)}getReferenceToSelf(e){let r=this.nameProvider.getNameNode(e);if(r){let n=Fe(e),i=this.nodeLocator.getAstNodePath(e);return{sourceUri:n.uri,sourcePath:i,targetUri:n.uri,targetPath:i,segment:Mr(r),local:!0}}}};var Rt=class{static{s(this,"MultiMap")}constructor(e){if(this.map=new Map,e)for(let[r,n]of e)this.add(r,n)}get size(){return fn.sum(H(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,r){if(r===void 0)return this.map.delete(e);{let n=this.map.get(e);if(n){let i=n.indexOf(r);if(i>=0)return n.length===1?this.map.delete(e):n.splice(i,1),!0}return!1}}get(e){var r;return(r=this.map.get(e))!==null&&r!==void 0?r:[]}has(e,r){if(r===void 0)return this.map.has(e);{let n=this.map.get(e);return n?n.indexOf(r)>=0:!1}}add(e,r){return this.map.has(e)?this.map.get(e).push(r):this.map.set(e,[r]),this}addAll(e,r){return this.map.has(e)?this.map.get(e).push(...r):this.map.set(e,Array.from(r)),this}forEach(e){this.map.forEach((r,n)=>r.forEach(i=>e(i,n,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return H(this.map.entries()).flatMap(([e,r])=>r.map(n=>[e,n]))}keys(){return H(this.map.keys())}values(){return H(this.map.values()).flat()}entriesGroupedByKey(){return H(this.map.entries())}},nn=class{static{s(this,"BiMap")}get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(let[r,n]of e)this.set(r,n)}clear(){this.map.clear(),this.inverse.clear()}set(e,r){return this.map.set(e,r),this.inverse.set(r,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){let r=this.map.get(e);return r!==void 0?(this.map.delete(e),this.inverse.delete(r),!0):!1}};var oa=class{static{s(this,"DefaultScopeComputation")}constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,r=_.CancellationToken.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,r)}async computeExportsForNode(e,r,n=gs,i=_.CancellationToken.None){let a=[];this.exportNode(e,a,r);for(let o of n(e))await Te(i),this.exportNode(o,a,r);return a}exportNode(e,r,n){let i=this.nameProvider.getName(e);i&&r.push(this.descriptions.createDescription(e,i,n))}async computeLocalScopes(e,r=_.CancellationToken.None){let n=e.parseResult.value,i=new Rt;for(let a of It(n))await Te(r),this.processNode(a,e,i);return i}processNode(e,r,n){let i=e.$container;if(i){let a=this.nameProvider.getName(e);a&&n.add(i,this.descriptions.createDescription(e,a,r))}}};var es=class{static{s(this,"StreamScope")}constructor(e,r,n){var i;this.elements=e,this.outerScope=r,this.caseInsensitive=(i=n?.caseInsensitive)!==null&&i!==void 0?i:!1}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){let r=this.caseInsensitive?this.elements.find(n=>n.name.toLowerCase()===e.toLowerCase()):this.elements.find(n=>n.name===e);if(r)return r;if(this.outerScope)return this.outerScope.getElement(e)}},la=class{static{s(this,"MapScope")}constructor(e,r,n){var i;this.elements=new Map,this.caseInsensitive=(i=n?.caseInsensitive)!==null&&i!==void 0?i:!1;for(let a of e){let o=this.caseInsensitive?a.name.toLowerCase():a.name;this.elements.set(o,a)}this.outerScope=r}getElement(e){let r=this.caseInsensitive?e.toLowerCase():e,n=this.elements.get(r);if(n)return n;if(this.outerScope)return this.outerScope.getElement(e)}getAllElements(){let e=H(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}},BN={getElement(){},getAllElements(){return ss}};var ts=class{static{s(this,"DisposableCache")}constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw new Error("This cache has already been disposed")}},ua=class extends ts{static{s(this,"SimpleCache")}constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,r){this.throwIfDisposed(),this.cache.set(e,r)}get(e,r){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(r){let n=r();return this.cache.set(e,n),n}else return}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}},sn=class extends ts{static{s(this,"ContextCache")}constructor(e){super(),this.cache=new Map,this.converter=e??(r=>r)}has(e,r){return this.throwIfDisposed(),this.cacheForContext(e).has(r)}set(e,r,n){this.throwIfDisposed(),this.cacheForContext(e).set(r,n)}get(e,r,n){this.throwIfDisposed();let i=this.cacheForContext(e);if(i.has(r))return i.get(r);if(n){let a=n();return i.set(r,a),a}else return}delete(e,r){return this.throwIfDisposed(),this.cacheForContext(e).delete(r)}clear(e){if(this.throwIfDisposed(),e){let r=this.converter(e);this.cache.delete(r)}else this.cache.clear()}cacheForContext(e){let r=this.converter(e),n=this.cache.get(r);return n||(n=new Map,this.cache.set(r,n)),n}},Cl=class extends sn{static{s(this,"DocumentCache")}constructor(e,r){super(n=>n.toString()),r?(this.toDispose.push(e.workspace.DocumentBuilder.onDocumentPhase(r,n=>{this.clear(n.uri.toString())})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{for(let a of i)this.clear(a)}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{let a=n.concat(i);for(let o of a)this.clear(o)}))}},rs=class extends ua{static{s(this,"WorkspaceCache")}constructor(e,r){super(),r?(this.toDispose.push(e.workspace.DocumentBuilder.onBuildPhase(r,()=>{this.clear()})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{i.length>0&&this.clear()}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}};var ca=class{static{s(this,"DefaultScopeProvider")}constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new rs(e.shared)}getScope(e){let r=[],n=this.reflection.getReferenceType(e),i=Fe(e.container).precomputedScopes;if(i){let o=e.container;do{let l=i.get(o);l.length>0&&r.push(H(l).filter(u=>this.reflection.isSubtype(u.type,n))),o=o.$container}while(o)}let a=this.getGlobalScope(n,e);for(let o=r.length-1;o>=0;o--)a=this.createScope(r[o],a);return a}createScope(e,r,n){return new es(H(e),r,n)}createScopeForNodes(e,r,n){let i=H(e).map(a=>{let o=this.nameProvider.getName(a);if(o)return this.descriptions.createDescription(a,o)}).nonNullable();return new es(i,r,n)}getGlobalScope(e,r){return this.globalScopeCache.get(e,()=>new la(this.indexManager.allElements(e)))}};function rf(t){return typeof t.$comment=="string"}s(rf,"isAstNodeWithComment");function $y(t){return typeof t=="object"&&!!t&&("$ref"in t||"$error"in t)}s($y,"isIntermediateReference");var fa=class{static{s(this,"DefaultJsonSerializer")}constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,r){let n=r??{},i=r?.replacer,a=s((l,u)=>this.replacer(l,u,n),"defaultReplacer"),o=i?(l,u)=>i(l,u,a):a;try{return this.currentDocument=Fe(e),JSON.stringify(e,o,r?.space)}finally{this.currentDocument=void 0}}deserialize(e,r){let n=r??{},i=JSON.parse(e);return this.linkNode(i,i,n),i}replacer(e,r,{refText:n,sourceText:i,textRegions:a,comments:o,uriConverter:l}){var u,c,f,d;if(!this.ignoreProperties.has(e))if(be(r)){let p=r.ref,m=n?r.$refText:void 0;if(p){let g=Fe(p),y="";this.currentDocument&&this.currentDocument!==g&&(l?y=l(g.uri,r):y=g.uri.toString());let A=this.astNodeLocator.getAstNodePath(p);return{$ref:`${y}#${A}`,$refText:m}}else return{$error:(c=(u=r.error)===null||u===void 0?void 0:u.message)!==null&&c!==void 0?c:"Could not resolve reference",$refText:m}}else if(fe(r)){let p;if(a&&(p=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},r)),(!e||r.$document)&&p?.$textRegion&&(p.$textRegion.documentURI=(f=this.currentDocument)===null||f===void 0?void 0:f.uri.toString())),i&&!e&&(p??(p=Object.assign({},r)),p.$sourceText=(d=r.$cstNode)===null||d===void 0?void 0:d.text),o){p??(p=Object.assign({},r));let m=this.commentProvider.getComment(r);m&&(p.$comment=m.replace(/\r/g,""))}return p??r}else return r}addAstNodeRegionWithAssignmentsTo(e){let r=s(n=>({offset:n.offset,end:n.end,length:n.length,range:n.range}),"createDocumentSegment");if(e.$cstNode){let n=e.$textRegion=r(e.$cstNode),i=n.assignments={};return Object.keys(e).filter(a=>!a.startsWith("$")).forEach(a=>{let o=$u(e.$cstNode,a).map(r);o.length!==0&&(i[a]=o)}),e}}linkNode(e,r,n,i,a,o){for(let[u,c]of Object.entries(e))if(Array.isArray(c))for(let f=0;f<c.length;f++){let d=c[f];$y(d)?c[f]=this.reviveReference(e,u,r,d,n):fe(d)&&this.linkNode(d,r,n,e,u,f)}else $y(c)?e[u]=this.reviveReference(e,u,r,c,n):fe(c)&&this.linkNode(c,r,n,e,u);let l=e;l.$container=i,l.$containerProperty=a,l.$containerIndex=o}reviveReference(e,r,n,i,a){let o=i.$refText,l=i.$error;if(i.$ref){let u=this.getRefNode(n,i.$ref,a.uriConverter);if(fe(u))return o||(o=this.nameProvider.getName(u)),{$refText:o??"",ref:u};l=u}if(l){let u={$refText:o??""};return u.error={container:e,property:r,message:l,reference:u},u}else return}getRefNode(e,r,n){try{let i=r.indexOf("#");if(i===0){let u=this.astNodeLocator.getAstNode(e,r.substring(1));return u||"Could not resolve path: "+r}if(i<0){let u=n?n(r):Ye.parse(r),c=this.langiumDocuments.getDocument(u);return c?c.parseResult.value:"Could not find document for URI: "+r}let a=n?n(r.substring(0,i)):Ye.parse(r.substring(0,i)),o=this.langiumDocuments.getDocument(a);if(!o)return"Could not find document for URI: "+r;if(i===r.length-1)return o.parseResult.value;let l=this.astNodeLocator.getAstNode(o.parseResult.value,r.substring(i+1));return l||"Could not resolve URI: "+r}catch(i){return String(i)}}};var da=class{static{s(this,"DefaultServiceRegistry")}get map(){return this.fileExtensionMap}constructor(e){this.languageIdMap=new Map,this.fileExtensionMap=new Map,this.textDocuments=e?.workspace.TextDocuments}register(e){let r=e.LanguageMetaData;for(let n of r.fileExtensions)this.fileExtensionMap.has(n)&&console.warn(`The file extension ${n} is used by multiple languages. It is now assigned to '${r.languageId}'.`),this.fileExtensionMap.set(n,e);this.languageIdMap.set(r.languageId,e),this.languageIdMap.size===1?this.singleton=e:this.singleton=void 0}getServices(e){var r,n;if(this.singleton!==void 0)return this.singleton;if(this.languageIdMap.size===0)throw new Error("The service registry is empty. Use `register` to register the services of a language.");let i=(n=(r=this.textDocuments)===null||r===void 0?void 0:r.get(e))===null||n===void 0?void 0:n.languageId;if(i!==void 0){let l=this.languageIdMap.get(i);if(l)return l}let a=Je.extname(e),o=this.fileExtensionMap.get(a);if(!o)throw i?new Error(`The service registry contains no services for the extension '${a}' for language '${i}'.`):new Error(`The service registry contains no services for the extension '${a}'.`);return o}hasServices(e){try{return this.getServices(e),!0}catch{return!1}}get all(){return Array.from(this.languageIdMap.values())}};function an(t){return{code:t}}s(an,"diagnosticData");var ns;(function(t){t.all=["fast","slow","built-in"]})(ns||(ns={}));var pa=class{static{s(this,"ValidationRegistry")}constructor(e){this.entries=new Rt,this.entriesBefore=[],this.entriesAfter=[],this.reflection=e.shared.AstReflection}register(e,r=this,n="fast"){if(n==="built-in")throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(let[i,a]of Object.entries(e)){let o=a;if(Array.isArray(o))for(let l of o){let u={check:this.wrapValidationException(l,r),category:n};this.addEntry(i,u)}else if(typeof o=="function"){let l={check:this.wrapValidationException(o,r),category:n};this.addEntry(i,l)}else vt(o)}}wrapValidationException(e,r){return async(n,i,a)=>{await this.handleException(()=>e.call(r,n,i,a),"An error occurred during validation",i,n)}}async handleException(e,r,n,i){try{await e()}catch(a){if(Ut(a))throw a;console.error(`${r}:`,a),a instanceof Error&&a.stack&&console.error(a.stack);let o=a instanceof Error?a.message:String(a);n("error",`${r}: ${o}`,{node:i})}}addEntry(e,r){if(e==="AstNode"){this.entries.add("AstNode",r);return}for(let n of this.reflection.getAllSubTypes(e))this.entries.add(n,r)}getChecks(e,r){let n=H(this.entries.get(e)).concat(this.entries.get("AstNode"));return r&&(n=n.filter(i=>r.includes(i.category))),n.map(i=>i.check)}registerBeforeDocument(e,r=this){this.entriesBefore.push(this.wrapPreparationException(e,"An error occurred during set-up of the validation",r))}registerAfterDocument(e,r=this){this.entriesAfter.push(this.wrapPreparationException(e,"An error occurred during tear-down of the validation",r))}wrapPreparationException(e,r,n){return async(i,a,o,l)=>{await this.handleException(()=>e.call(n,i,a,o,l),r,a,i)}}get checksBefore(){return this.entriesBefore}get checksAfter(){return this.entriesAfter}};var ma=class{static{s(this,"DefaultDocumentValidator")}constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}async validateDocument(e,r={},n=_.CancellationToken.None){let i=e.parseResult,a=[];if(await Te(n),(!r.categories||r.categories.includes("built-in"))&&(this.processLexingErrors(i,a,r),r.stopAfterLexingErrors&&a.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===pt.LexingError})||(this.processParsingErrors(i,a,r),r.stopAfterParsingErrors&&a.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===pt.ParsingError}))||(this.processLinkingErrors(e,a,r),r.stopAfterLinkingErrors&&a.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===pt.LinkingError}))))return a;try{a.push(...await this.validateAst(i.value,r,n))}catch(o){if(Ut(o))throw o;console.error("An error occurred during validation:",o)}return await Te(n),a}processLexingErrors(e,r,n){var i,a,o;let l=[...e.lexerErrors,...(a=(i=e.lexerReport)===null||i===void 0?void 0:i.diagnostics)!==null&&a!==void 0?a:[]];for(let u of l){let c=(o=u.severity)!==null&&o!==void 0?o:"error",f={severity:Nl(c),range:{start:{line:u.line-1,character:u.column-1},end:{line:u.line-1,character:u.column+u.length-1}},message:u.message,data:Ly(c),source:this.getSource()};r.push(f)}}processParsingErrors(e,r,n){for(let i of e.parserErrors){let a;if(isNaN(i.token.startOffset)){if("previousToken"in i){let o=i.previousToken;if(isNaN(o.startOffset)){let l={line:0,character:0};a={start:l,end:l}}else{let l={line:o.endLine-1,character:o.endColumn};a={start:l,end:l}}}}else a=dn(i.token);if(a){let o={severity:Nl("error"),range:a,message:i.message,data:an(pt.ParsingError),source:this.getSource()};r.push(o)}}}processLinkingErrors(e,r,n){for(let i of e.references){let a=i.error;if(a){let o={node:a.container,property:a.property,index:a.index,data:{code:pt.LinkingError,containerType:a.container.$type,property:a.property,refText:a.reference.$refText}};r.push(this.toDiagnostic("error",a.message,o))}}}async validateAst(e,r,n=_.CancellationToken.None){let i=[],a=s((o,l,u)=>{i.push(this.toDiagnostic(o,l,u))},"acceptor");return await this.validateAstBefore(e,r,a,n),await this.validateAstNodes(e,r,a,n),await this.validateAstAfter(e,r,a,n),i}async validateAstBefore(e,r,n,i=_.CancellationToken.None){var a;let o=this.validationRegistry.checksBefore;for(let l of o)await Te(i),await l(e,n,(a=r.categories)!==null&&a!==void 0?a:[],i)}async validateAstNodes(e,r,n,i=_.CancellationToken.None){await Promise.all(ct(e).map(async a=>{await Te(i);let o=this.validationRegistry.getChecks(a.$type,r.categories);for(let l of o)await l(a,n,i)}))}async validateAstAfter(e,r,n,i=_.CancellationToken.None){var a;let o=this.validationRegistry.checksAfter;for(let l of o)await Te(i),await l(e,n,(a=r.categories)!==null&&a!==void 0?a:[],i)}toDiagnostic(e,r,n){return{message:r,range:Oy(n),severity:Nl(e),code:n.code,codeDescription:n.codeDescription,tags:n.tags,relatedInformation:n.relatedInformation,data:n.data,source:this.getSource()}}getSource(){return this.metadata.languageId}};function Oy(t){if(t.range)return t.range;let e;return typeof t.property=="string"?e=As(t.node.$cstNode,t.property,t.index):typeof t.keyword=="string"&&(e=Lu(t.node.$cstNode,t.keyword,t.index)),e??(e=t.node.$cstNode),e?e.range:{start:{line:0,character:0},end:{line:0,character:0}}}s(Oy,"getDiagnosticRange");function Nl(t){switch(t){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+t)}}s(Nl,"toDiagnosticSeverity");function Ly(t){switch(t){case"error":return an(pt.LexingError);case"warning":return an(pt.LexingWarning);case"info":return an(pt.LexingInfo);case"hint":return an(pt.LexingHint);default:throw new Error("Invalid diagnostic severity: "+t)}}s(Ly,"toDiagnosticData");var pt;(function(t){t.LexingError="lexing-error",t.LexingWarning="lexing-warning",t.LexingInfo="lexing-info",t.LexingHint="lexing-hint",t.ParsingError="parsing-error",t.LinkingError="linking-error"})(pt||(pt={}));var ha=class{static{s(this,"DefaultAstNodeDescriptionProvider")}constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,r,n){let i=n??Fe(e);r??(r=this.nameProvider.getName(e));let a=this.astNodeLocator.getAstNodePath(e);if(!r)throw new Error(`Node at path ${a} has no name.`);let o,l=s(()=>{var u;return o??(o=Mr((u=this.nameProvider.getNameNode(e))!==null&&u!==void 0?u:e.$cstNode))},"nameSegmentGetter");return{node:e,name:r,get nameSegment(){return l()},selectionSegment:Mr(e.$cstNode),type:e.$type,documentUri:i.uri,path:a}}},ga=class{static{s(this,"DefaultReferenceDescriptionProvider")}constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,r=_.CancellationToken.None){let n=[],i=e.parseResult.value;for(let a of ct(i))await Te(r),Zn(a).filter(o=>!Lr(o)).forEach(o=>{let l=this.createDescription(o);l&&n.push(l)});return n}createDescription(e){let r=e.reference.$nodeDescription,n=e.reference.$refNode;if(!r||!n)return;let i=Fe(e.container).uri;return{sourceUri:i,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:r.documentUri,targetPath:r.path,segment:Mr(n),local:Je.equals(r.documentUri,i)}}};var ya=class{static{s(this,"DefaultAstNodeLocator")}constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){let r=this.getAstNodePath(e.$container),n=this.getPathSegment(e);return r+this.segmentSeparator+n}return""}getPathSegment({$containerProperty:e,$containerIndex:r}){if(!e)throw new Error("Missing '$containerProperty' in AST node.");return r!==void 0?e+this.indexSeparator+r:e}getAstNode(e,r){return r.split(this.segmentSeparator).reduce((i,a)=>{if(!i||a.length===0)return i;let o=a.indexOf(this.indexSeparator);if(o>0){let l=a.substring(0,o),u=parseInt(a.substring(o+1)),c=i[l];return c?.[u]}return i[a]},e)}};var ae={};B(ae,Of(Yc(),1));var xa=class{static{s(this,"DefaultConfigurationProvider")}constructor(e){this._ready=new Xe,this.settings={},this.workspaceConfig=!1,this.onConfigurationSectionUpdateEmitter=new ae.Emitter,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var r,n;this.workspaceConfig=(n=(r=e.capabilities.workspace)===null||r===void 0?void 0:r.configuration)!==null&&n!==void 0?n:!1}async initialized(e){if(this.workspaceConfig){if(e.register){let r=this.serviceRegistry.all;e.register({section:r.map(n=>this.toSectionName(n.LanguageMetaData.languageId))})}if(e.fetchConfiguration){let r=this.serviceRegistry.all.map(i=>({section:this.toSectionName(i.LanguageMetaData.languageId)})),n=await e.fetchConfiguration(r);r.forEach((i,a)=>{this.updateSectionConfiguration(i.section,n[a])})}}this._ready.resolve()}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(r=>{let n=e.settings[r];this.updateSectionConfiguration(r,n),this.onConfigurationSectionUpdateEmitter.fire({section:r,configuration:n})})}updateSectionConfiguration(e,r){this.settings[e]=r}async getConfiguration(e,r){await this.ready;let n=this.toSectionName(e);if(this.settings[n])return this.settings[n][r]}toSectionName(e){return`${e}`}get onConfigurationSectionUpdate(){return this.onConfigurationSectionUpdateEmitter.event}};var wr;(function(t){function e(r){return{dispose:s(async()=>await r(),"dispose")}}s(e,"create"),t.create=e})(wr||(wr={}));var Ta=class{static{s(this,"DefaultDocumentBuilder")}constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new Rt,this.documentPhaseListeners=new Rt,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=J.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.textDocuments=e.workspace.TextDocuments,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}async build(e,r={},n=_.CancellationToken.None){var i,a;for(let o of e){let l=o.uri.toString();if(o.state===J.Validated){if(typeof r.validation=="boolean"&&r.validation)o.state=J.IndexedReferences,o.diagnostics=void 0,this.buildState.delete(l);else if(typeof r.validation=="object"){let u=this.buildState.get(l),c=(i=u?.result)===null||i===void 0?void 0:i.validationChecks;if(c){let d=((a=r.validation.categories)!==null&&a!==void 0?a:ns.all).filter(p=>!c.includes(p));d.length>0&&(this.buildState.set(l,{completed:!1,options:{validation:Object.assign(Object.assign({},r.validation),{categories:d})},result:u.result}),o.state=J.IndexedReferences)}}}else this.buildState.delete(l)}this.currentState=J.Changed,await this.emitUpdate(e.map(o=>o.uri),[]),await this.buildDocuments(e,r,n)}async update(e,r,n=_.CancellationToken.None){this.currentState=J.Changed;for(let o of r)this.langiumDocuments.deleteDocument(o),this.buildState.delete(o.toString()),this.indexManager.remove(o);for(let o of e){if(!this.langiumDocuments.invalidateDocument(o)){let u=this.langiumDocumentFactory.fromModel({$type:"INVALID"},o);u.state=J.Changed,this.langiumDocuments.addDocument(u)}this.buildState.delete(o.toString())}let i=H(e).concat(r).map(o=>o.toString()).toSet();this.langiumDocuments.all.filter(o=>!i.has(o.uri.toString())&&this.shouldRelink(o,i)).forEach(o=>{this.serviceRegistry.getServices(o.uri).references.Linker.unlink(o),o.state=Math.min(o.state,J.ComputedScopes),o.diagnostics=void 0}),await this.emitUpdate(e,r),await Te(n);let a=this.sortDocuments(this.langiumDocuments.all.filter(o=>{var l;return o.state<J.Linked||!(!((l=this.buildState.get(o.uri.toString()))===null||l===void 0)&&l.completed)}).toArray());await this.buildDocuments(a,this.updateBuildOptions,n)}async emitUpdate(e,r){await Promise.all(this.updateListeners.map(n=>n(e,r)))}sortDocuments(e){let r=0,n=e.length-1;for(;r<n;){for(;r<e.length&&this.hasTextDocument(e[r]);)r++;for(;n>=0&&!this.hasTextDocument(e[n]);)n--;r<n&&([e[r],e[n]]=[e[n],e[r]])}return e}hasTextDocument(e){var r;return!!(!((r=this.textDocuments)===null||r===void 0)&&r.get(e.uri))}shouldRelink(e,r){return e.references.some(n=>n.error!==void 0)?!0:this.indexManager.isAffected(e,r)}onUpdate(e){return this.updateListeners.push(e),wr.create(()=>{let r=this.updateListeners.indexOf(e);r>=0&&this.updateListeners.splice(r,1)})}async buildDocuments(e,r,n){this.prepareBuild(e,r),await this.runCancelable(e,J.Parsed,n,a=>this.langiumDocumentFactory.update(a,n)),await this.runCancelable(e,J.IndexedContent,n,a=>this.indexManager.updateContent(a,n)),await this.runCancelable(e,J.ComputedScopes,n,async a=>{let o=this.serviceRegistry.getServices(a.uri).references.ScopeComputation;a.precomputedScopes=await o.computeLocalScopes(a,n)}),await this.runCancelable(e,J.Linked,n,a=>this.serviceRegistry.getServices(a.uri).references.Linker.link(a,n)),await this.runCancelable(e,J.IndexedReferences,n,a=>this.indexManager.updateReferences(a,n));let i=e.filter(a=>this.shouldValidate(a));await this.runCancelable(i,J.Validated,n,a=>this.validate(a,n));for(let a of e){let o=this.buildState.get(a.uri.toString());o&&(o.completed=!0)}}prepareBuild(e,r){for(let n of e){let i=n.uri.toString(),a=this.buildState.get(i);(!a||a.completed)&&this.buildState.set(i,{completed:!1,options:r,result:a?.result})}}async runCancelable(e,r,n,i){let a=e.filter(l=>l.state<r);for(let l of a)await Te(n),await i(l),l.state=r,await this.notifyDocumentPhase(l,r,n);let o=e.filter(l=>l.state===r);await this.notifyBuildPhase(o,r,n),this.currentState=r}onBuildPhase(e,r){return this.buildPhaseListeners.add(e,r),wr.create(()=>{this.buildPhaseListeners.delete(e,r)})}onDocumentPhase(e,r){return this.documentPhaseListeners.add(e,r),wr.create(()=>{this.documentPhaseListeners.delete(e,r)})}waitUntil(e,r,n){let i;if(r&&"path"in r?i=r:n=r,n??(n=_.CancellationToken.None),i){let a=this.langiumDocuments.getDocument(i);if(a&&a.state>e)return Promise.resolve(i)}return this.currentState>=e?Promise.resolve(void 0):n.isCancellationRequested?Promise.reject(Gt):new Promise((a,o)=>{let l=this.onBuildPhase(e,()=>{if(l.dispose(),u.dispose(),i){let c=this.langiumDocuments.getDocument(i);a(c?.uri)}else a(void 0)}),u=n.onCancellationRequested(()=>{l.dispose(),u.dispose(),o(Gt)})})}async notifyDocumentPhase(e,r,n){let a=this.documentPhaseListeners.get(r).slice();for(let o of a)try{await o(e,n)}catch(l){if(!Ut(l))throw l}}async notifyBuildPhase(e,r,n){if(e.length===0)return;let a=this.buildPhaseListeners.get(r).slice();for(let o of a)await Te(n),await o(e,n)}shouldValidate(e){return!!this.getBuildOptions(e).validation}async validate(e,r){var n,i;let a=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,o=this.getBuildOptions(e).validation,l=typeof o=="object"?o:void 0,u=await a.validateDocument(e,l,r);e.diagnostics?e.diagnostics.push(...u):e.diagnostics=u;let c=this.buildState.get(e.uri.toString());if(c){(n=c.result)!==null&&n!==void 0||(c.result={});let f=(i=l?.categories)!==null&&i!==void 0?i:ns.all;c.result.validationChecks?c.result.validationChecks.push(...f):c.result.validationChecks=[...f]}}getBuildOptions(e){var r,n;return(n=(r=this.buildState.get(e.uri.toString()))===null||r===void 0?void 0:r.options)!==null&&n!==void 0?n:{}}};var Ra=class{static{s(this,"DefaultIndexManager")}constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new sn,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,r){let n=Fe(e).uri,i=[];return this.referenceIndex.forEach(a=>{a.forEach(o=>{Je.equals(o.targetUri,n)&&o.targetPath===r&&i.push(o)})}),H(i)}allElements(e,r){let n=H(this.symbolIndex.keys());return r&&(n=n.filter(i=>!r||r.has(i))),n.map(i=>this.getFileDescriptions(i,e)).flat()}getFileDescriptions(e,r){var n;return r?this.symbolByTypeIndex.get(e,r,()=>{var a;return((a=this.symbolIndex.get(e))!==null&&a!==void 0?a:[]).filter(l=>this.astReflection.isSubtype(l.type,r))}):(n=this.symbolIndex.get(e))!==null&&n!==void 0?n:[]}remove(e){let r=e.toString();this.symbolIndex.delete(r),this.symbolByTypeIndex.clear(r),this.referenceIndex.delete(r)}async updateContent(e,r=_.CancellationToken.None){let i=await this.serviceRegistry.getServices(e.uri).references.ScopeComputation.computeExports(e,r),a=e.uri.toString();this.symbolIndex.set(a,i),this.symbolByTypeIndex.clear(a)}async updateReferences(e,r=_.CancellationToken.None){let i=await this.serviceRegistry.getServices(e.uri).workspace.ReferenceDescriptionProvider.createDescriptions(e,r);this.referenceIndex.set(e.uri.toString(),i)}isAffected(e,r){let n=this.referenceIndex.get(e.uri.toString());return n?n.some(i=>!i.local&&r.has(i.targetUri.toString())):!1}};var Aa=class{static{s(this,"DefaultWorkspaceManager")}constructor(e){this.initialBuildOptions={},this._ready=new Xe,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}get workspaceFolders(){return this.folders}initialize(e){var r;this.folders=(r=e.workspaceFolders)!==null&&r!==void 0?r:void 0}initialized(e){return this.mutex.write(r=>{var n;return this.initializeWorkspace((n=this.folders)!==null&&n!==void 0?n:[],r)})}async initializeWorkspace(e,r=_.CancellationToken.None){let n=await this.performStartup(e);await Te(r),await this.documentBuilder.build(n,this.initialBuildOptions,r)}async performStartup(e){let r=this.serviceRegistry.all.flatMap(a=>a.LanguageMetaData.fileExtensions),n=[],i=s(a=>{n.push(a),this.langiumDocuments.hasDocument(a.uri)||this.langiumDocuments.addDocument(a)},"collector");return await this.loadAdditionalDocuments(e,i),await Promise.all(e.map(a=>[a,this.getRootFolder(a)]).map(async a=>this.traverseFolder(...a,r,i))),this._ready.resolve(),n}loadAdditionalDocuments(e,r){return Promise.resolve()}getRootFolder(e){return Ye.parse(e.uri)}async traverseFolder(e,r,n,i){let a=await this.fileSystemProvider.readDirectory(r);await Promise.all(a.map(async o=>{if(this.includeEntry(e,o,n)){if(o.isDirectory)await this.traverseFolder(e,o.uri,n,i);else if(o.isFile){let l=await this.langiumDocuments.getOrCreateDocument(o.uri);i(l)}}}))}includeEntry(e,r,n){let i=Je.basename(r.uri);if(i.startsWith("."))return!1;if(r.isDirectory)return i!=="node_modules"&&i!=="out";if(r.isFile){let a=Je.extname(r.uri);return n.includes(a)}return!1}};var Ea=class{static{s(this,"DefaultLexerErrorMessageProvider")}buildUnexpectedCharactersMessage(e,r,n,i,a){return Oi.buildUnexpectedCharactersMessage(e,r,n,i,a)}buildUnableToPopLexerModeMessage(e){return Oi.buildUnableToPopLexerModeMessage(e)}},wl={mode:"full"},on=class{static{s(this,"DefaultLexer")}constructor(e){this.errorMessageProvider=e.parser.LexerErrorMessageProvider,this.tokenBuilder=e.parser.TokenBuilder;let r=this.tokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(r);let n=nf(r)?Object.values(r):r,i=e.LanguageMetaData.mode==="production";this.chevrotainLexer=new ue(n,{positionTracking:"full",skipValidations:i,errorMessageProvider:this.errorMessageProvider})}get definition(){return this.tokenTypes}tokenize(e,r=wl){var n,i,a;let o=this.chevrotainLexer.tokenize(e);return{tokens:o.tokens,errors:o.errors,hidden:(n=o.groups.hidden)!==null&&n!==void 0?n:[],report:(a=(i=this.tokenBuilder).flushLexingReport)===null||a===void 0?void 0:a.call(i,e)}}toTokenTypeDictionary(e){if(nf(e))return e;let r=sf(e)?Object.values(e.modes).flat():e,n={};return r.forEach(i=>n[i.name]=i),n}};function _l(t){return Array.isArray(t)&&(t.length===0||"name"in t[0])}s(_l,"isTokenTypeArray");function sf(t){return t&&"modes"in t&&"defaultMode"in t}s(sf,"isIMultiModeLexerDefinition");function nf(t){return!_l(t)&&!sf(t)}s(nf,"isTokenTypeDictionary");function lf(t,e,r){let n,i;typeof t=="string"?(i=e,n=r):(i=t.range.start,n=e),i||(i=j.create(0,0));let a=Dy(t),o=cf(n),l=WN({lines:a,position:i,options:o});return qN({index:0,tokens:l,position:i})}s(lf,"parseJSDoc");function uf(t,e){let r=cf(e),n=Dy(t);if(n.length===0)return!1;let i=n[0],a=n[n.length-1],o=r.start,l=r.end;return!!o?.exec(i)&&!!l?.exec(a)}s(uf,"isJSDoc");function Dy(t){let e="";return typeof t=="string"?e=t:e=t.text,e.split(Iu)}s(Dy,"getLines");var Py=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,jN=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function WN(t){var e,r,n;let i=[],a=t.position.line,o=t.position.character;for(let l=0;l<t.lines.length;l++){let u=l===0,c=l===t.lines.length-1,f=t.lines[l],d=0;if(u&&t.options.start){let m=(e=t.options.start)===null||e===void 0?void 0:e.exec(f);m&&(d=m.index+m[0].length)}else{let m=(r=t.options.line)===null||r===void 0?void 0:r.exec(f);m&&(d=m.index+m[0].length)}if(c){let m=(n=t.options.end)===null||n===void 0?void 0:n.exec(f);m&&(f=f.substring(0,m.index))}if(f=f.substring(0,zN(f)),of(f,d)>=f.length){if(i.length>0){let m=j.create(a,o);i.push({type:"break",content:"",range:U.create(m,m)})}}else{Py.lastIndex=d;let m=Py.exec(f);if(m){let g=m[0],y=m[1],A=j.create(a,o+d),T=j.create(a,o+d+g.length);i.push({type:"tag",content:y,range:U.create(A,T)}),d+=g.length,d=of(f,d)}if(d<f.length){let g=f.substring(d),y=Array.from(g.matchAll(jN));i.push(...KN(y,g,a,o+d))}}a++,o=0}return i.length>0&&i[i.length-1].type==="break"?i.slice(0,-1):i}s(WN,"tokenize");function KN(t,e,r,n){let i=[];if(t.length===0){let a=j.create(r,n),o=j.create(r,n+e.length);i.push({type:"text",content:e,range:U.create(a,o)})}else{let a=0;for(let l of t){let u=l.index,c=e.substring(a,u);c.length>0&&i.push({type:"text",content:e.substring(a,u),range:U.create(j.create(r,a+n),j.create(r,u+n))});let f=c.length+1,d=l[1];if(i.push({type:"inline-tag",content:d,range:U.create(j.create(r,a+f+n),j.create(r,a+f+d.length+n))}),f+=d.length,l.length===4){f+=l[2].length;let p=l[3];i.push({type:"text",content:p,range:U.create(j.create(r,a+f+n),j.create(r,a+f+p.length+n))})}else i.push({type:"text",content:"",range:U.create(j.create(r,a+f+n),j.create(r,a+f+n))});a=u+l[0].length}let o=e.substring(a);o.length>0&&i.push({type:"text",content:o,range:U.create(j.create(r,a+n),j.create(r,a+n+o.length))})}return i}s(KN,"buildInlineTokens");var HN=/\S/,VN=/\s*$/;function of(t,e){let r=t.substring(e).match(HN);return r?e+r.index:t.length}s(of,"skipWhitespace");function zN(t){let e=t.match(VN);if(e&&typeof e.index=="number")return e.index}s(zN,"lastCharacter");function qN(t){var e,r,n,i;let a=j.create(t.position.line,t.position.character);if(t.tokens.length===0)return new bl([],U.create(a,a));let o=[];for(;t.index<t.tokens.length;){let c=XN(t,o[o.length-1]);c&&o.push(c)}let l=(r=(e=o[0])===null||e===void 0?void 0:e.range.start)!==null&&r!==void 0?r:a,u=(i=(n=o[o.length-1])===null||n===void 0?void 0:n.range.end)!==null&&i!==void 0?i:a;return new bl(o,U.create(l,u))}s(qN,"parseJSDocComment");function XN(t,e){let r=t.tokens[t.index];if(r.type==="tag")return Gy(t,!1);if(r.type==="text"||r.type==="inline-tag")return Fy(t);YN(r,e),t.index++}s(XN,"parseJSDocElement");function YN(t,e){if(e){let r=new $l("",t.range);"inlines"in e?e.inlines.push(r):e.content.inlines.push(r)}}s(YN,"appendEmptyLine");function Fy(t){let e=t.tokens[t.index],r=e,n=e,i=[];for(;e&&e.type!=="break"&&e.type!=="tag";)i.push(JN(t)),n=e,e=t.tokens[t.index];return new Ia(i,U.create(r.range.start,n.range.end))}s(Fy,"parseJSDocText");function JN(t){return t.tokens[t.index].type==="inline-tag"?Gy(t,!0):Uy(t)}s(JN,"parseJSDocInline");function Gy(t,e){let r=t.tokens[t.index++],n=r.content.substring(1),i=t.tokens[t.index];if(i?.type==="text")if(e){let a=Uy(t);return new va(n,new Ia([a],a.range),e,U.create(r.range.start,a.range.end))}else{let a=Fy(t);return new va(n,a,e,U.create(r.range.start,a.range.end))}else{let a=r.range;return new va(n,new Ia([],a),e,a)}}s(Gy,"parseJSDocTag");function Uy(t){let e=t.tokens[t.index++];return new $l(e.content,e.range)}s(Uy,"parseJSDocLine");function cf(t){if(!t)return cf({start:"/**",end:"*/",line:"*"});let{start:e,end:r,line:n}=t;return{start:af(e,!0),end:af(r,!1),line:af(n,!0)}}s(cf,"normalizeOptions");function af(t,e){if(typeof t=="string"||typeof t=="object"){let r=typeof t=="string"?Kr(t):t.source;return e?new RegExp(`^\\s*${r}`):new RegExp(`\\s*${r}\\s*$`)}else return t}s(af,"normalizeOption");var bl=class{static{s(this,"JSDocCommentImpl")}constructor(e,r){this.elements=e,this.range=r}getTag(e){return this.getAllTags().find(r=>r.name===e)}getTags(e){return this.getAllTags().filter(r=>r.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(let r of this.elements)if(e.length===0)e=r.toString();else{let n=r.toString();e+=My(e)+n}return e.trim()}toMarkdown(e){let r="";for(let n of this.elements)if(r.length===0)r=n.toMarkdown(e);else{let i=n.toMarkdown(e);r+=My(r)+i}return r.trim()}},va=class{static{s(this,"JSDocTagImpl")}constructor(e,r,n,i){this.name=e,this.content=r,this.inline=n,this.range=i}toString(){let e=`@${this.name}`,r=this.content.toString();return this.content.inlines.length===1?e=`${e} ${r}`:this.content.inlines.length>1&&(e=`${e}
${r}`),this.inline?`{${e}}`:e}toMarkdown(e){var r,n;return(n=(r=e?.renderTag)===null||r===void 0?void 0:r.call(e,this))!==null&&n!==void 0?n:this.toMarkdownDefault(e)}toMarkdownDefault(e){let r=this.content.toMarkdown(e);if(this.inline){let a=ZN(this.name,r,e??{});if(typeof a=="string")return a}let n="";e?.tag==="italic"||e?.tag===void 0?n="*":e?.tag==="bold"?n="**":e?.tag==="bold-italic"&&(n="***");let i=`${n}@${this.name}${n}`;return this.content.inlines.length===1?i=`${i} \u2014 ${r}`:this.content.inlines.length>1&&(i=`${i}
${r}`),this.inline?`{${i}}`:i}};function ZN(t,e,r){var n,i;if(t==="linkplain"||t==="linkcode"||t==="link"){let a=e.indexOf(" "),o=e;if(a>0){let u=of(e,a);o=e.substring(u),e=e.substring(0,a)}return(t==="linkcode"||t==="link"&&r.link==="code")&&(o=`\`${o}\``),(i=(n=r.renderLink)===null||n===void 0?void 0:n.call(r,e,o))!==null&&i!==void 0?i:QN(e,o)}}s(ZN,"renderInlineTag");function QN(t,e){try{return Ye.parse(t,!0),`[${e}](${t})`}catch{return t}}s(QN,"renderLinkDefault");var Ia=class{static{s(this,"JSDocTextImpl")}constructor(e,r){this.inlines=e,this.range=r}toString(){let e="";for(let r=0;r<this.inlines.length;r++){let n=this.inlines[r],i=this.inlines[r+1];e+=n.toString(),i&&i.range.start.line>n.range.start.line&&(e+=`
`)}return e}toMarkdown(e){let r="";for(let n=0;n<this.inlines.length;n++){let i=this.inlines[n],a=this.inlines[n+1];r+=i.toMarkdown(e),a&&a.range.start.line>i.range.start.line&&(r+=`
`)}return r}},$l=class{static{s(this,"JSDocLineImpl")}constructor(e,r){this.text=e,this.range=r}toString(){return this.text}toMarkdown(){return this.text}};function My(t){return t.endsWith(`
`)?`
`:`

`}s(My,"fillNewlines");var ka=class{static{s(this,"JSDocDocumentationProvider")}constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){let r=this.commentProvider.getComment(e);if(r&&uf(r))return lf(r).toMarkdown({renderLink:s((i,a)=>this.documentationLinkRenderer(e,i,a),"renderLink"),renderTag:s(i=>this.documentationTagRenderer(e,i),"renderTag")})}documentationLinkRenderer(e,r,n){var i;let a=(i=this.findNameInPrecomputedScopes(e,r))!==null&&i!==void 0?i:this.findNameInGlobalScope(e,r);if(a&&a.nameSegment){let o=a.nameSegment.range.start.line+1,l=a.nameSegment.range.start.character+1,u=a.documentUri.with({fragment:`L${o},${l}`});return`[${n}](${u.toString()})`}else return}documentationTagRenderer(e,r){}findNameInPrecomputedScopes(e,r){let i=Fe(e).precomputedScopes;if(!i)return;let a=e;do{let l=i.get(a).find(u=>u.name===r);if(l)return l;a=a.$container}while(a)}findNameInGlobalScope(e,r){return this.indexManager.allElements().find(i=>i.name===r)}};var Sa=class{static{s(this,"DefaultCommentProvider")}constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var r;return rf(e)?e.$comment:(r=Ql(e.$cstNode,this.grammarConfig().multilineCommentRules))===null||r===void 0?void 0:r.text}};var Ca=class{static{s(this,"DefaultAsyncParser")}constructor(e){this.syncParser=e.parser.LangiumParser}parse(e,r){return Promise.resolve(this.syncParser.parse(e))}},ff=class{static{s(this,"AbstractThreadedAsyncParser")}constructor(e){this.threadCount=8,this.terminationDelay=200,this.workerPool=[],this.queue=[],this.hydrator=e.serializer.Hydrator}initializeWorkers(){for(;this.workerPool.length<this.threadCount;){let e=this.createWorker();e.onReady(()=>{if(this.queue.length>0){let r=this.queue.shift();r&&(e.lock(),r.resolve(e))}}),this.workerPool.push(e)}}async parse(e,r){let n=await this.acquireParserWorker(r),i=new Xe,a,o=r.onCancellationRequested(()=>{a=setTimeout(()=>{this.terminateWorker(n)},this.terminationDelay)});return n.parse(e).then(l=>{let u=this.hydrator.hydrate(l);i.resolve(u)}).catch(l=>{i.reject(l)}).finally(()=>{o.dispose(),clearTimeout(a)}),i.promise}terminateWorker(e){e.terminate();let r=this.workerPool.indexOf(e);r>=0&&this.workerPool.splice(r,1)}async acquireParserWorker(e){this.initializeWorkers();for(let n of this.workerPool)if(n.ready)return n.lock(),n;let r=new Xe;return e.onCancellationRequested(()=>{let n=this.queue.indexOf(r);n>=0&&this.queue.splice(n,1),r.reject(Gt)}),this.queue.push(r),r.promise}},df=class{static{s(this,"ParserWorker")}get ready(){return this._ready}get onReady(){return this.onReadyEmitter.event}constructor(e,r,n,i){this.onReadyEmitter=new ae.Emitter,this.deferred=new Xe,this._ready=!0,this._parsing=!1,this.sendMessage=e,this._terminate=i,r(a=>{let o=a;this.deferred.resolve(o),this.unlock()}),n(a=>{this.deferred.reject(a),this.unlock()})}terminate(){this.deferred.reject(Gt),this._terminate()}lock(){this._ready=!1}unlock(){this._parsing=!1,this._ready=!0,this.onReadyEmitter.fire()}parse(e){if(this._parsing)throw new Error("Parser worker is busy");return this._parsing=!0,this.deferred=new Xe,this.sendMessage(e),this.deferred.promise}};var Na=class{static{s(this,"DefaultWorkspaceLock")}constructor(){this.previousTokenSource=new _.CancellationTokenSource,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();let r=kl();return this.previousTokenSource=r,this.enqueue(this.writeQueue,e,r.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,r,n=_.CancellationToken.None){let i=new Xe,a={action:r,deferred:i,cancellationToken:n};return e.push(a),this.performNextOperation(),i.promise}async performNextOperation(){if(!this.done)return;let e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else if(this.readQueue.length>0)e.push(...this.readQueue.splice(0,this.readQueue.length));else return;this.done=!1,await Promise.all(e.map(async({action:r,deferred:n,cancellationToken:i})=>{try{let a=await Promise.resolve().then(()=>r(i));n.resolve(a)}catch(a){Ut(a)?n.resolve(void 0):n.reject(a)}})),this.done=!0,this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}};var wa=class{static{s(this,"DefaultHydrator")}constructor(e){this.grammarElementIdMap=new nn,this.tokenTypeIdMap=new nn,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport?this.dehydrateLexerReport(e.lexerReport):void 0,parserErrors:e.parserErrors.map(r=>Object.assign(Object.assign({},r),{message:r.message})),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}dehydrateLexerReport(e){return e}createDehyrationContext(e){let r=new Map,n=new Map;for(let i of ct(e))r.set(i,{});if(e.$cstNode)for(let i of Pr(e.$cstNode))n.set(i,{});return{astNodes:r,cstNodes:n}}dehydrateAstNode(e,r){let n=r.astNodes.get(e);n.$type=e.$type,n.$containerIndex=e.$containerIndex,n.$containerProperty=e.$containerProperty,e.$cstNode!==void 0&&(n.$cstNode=this.dehydrateCstNode(e.$cstNode,r));for(let[i,a]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(a)){let o=[];n[i]=o;for(let l of a)fe(l)?o.push(this.dehydrateAstNode(l,r)):be(l)?o.push(this.dehydrateReference(l,r)):o.push(l)}else fe(a)?n[i]=this.dehydrateAstNode(a,r):be(a)?n[i]=this.dehydrateReference(a,r):a!==void 0&&(n[i]=a);return n}dehydrateReference(e,r){let n={};return n.$refText=e.$refText,e.$refNode&&(n.$refNode=r.cstNodes.get(e.$refNode)),n}dehydrateCstNode(e,r){let n=r.cstNodes.get(e);return is(e)?n.fullText=e.fullText:n.grammarSource=this.getGrammarElementId(e.grammarSource),n.hidden=e.hidden,n.astNode=r.astNodes.get(e.astNode),ht(e)?n.content=e.content.map(i=>this.dehydrateCstNode(i,r)):ur(e)&&(n.tokenType=e.tokenType.name,n.offset=e.offset,n.length=e.length,n.startLine=e.range.start.line,n.startColumn=e.range.start.character,n.endLine=e.range.end.line,n.endColumn=e.range.end.character),n}hydrate(e){let r=e.value,n=this.createHydrationContext(r);return"$cstNode"in r&&this.hydrateCstNode(r.$cstNode,n),{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport,parserErrors:e.parserErrors,value:this.hydrateAstNode(r,n)}}createHydrationContext(e){let r=new Map,n=new Map;for(let a of ct(e))r.set(a,{});let i;if(e.$cstNode)for(let a of Pr(e.$cstNode)){let o;"fullText"in a?(o=new qi(a.fullText),i=o):"content"in a?o=new en:"tokenType"in a&&(o=this.hydrateCstLeafNode(a)),o&&(n.set(a,o),o.root=i)}return{astNodes:r,cstNodes:n}}hydrateAstNode(e,r){let n=r.astNodes.get(e);n.$type=e.$type,n.$containerIndex=e.$containerIndex,n.$containerProperty=e.$containerProperty,e.$cstNode&&(n.$cstNode=r.cstNodes.get(e.$cstNode));for(let[i,a]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(a)){let o=[];n[i]=o;for(let l of a)fe(l)?o.push(this.setParent(this.hydrateAstNode(l,r),n)):be(l)?o.push(this.hydrateReference(l,n,i,r)):o.push(l)}else fe(a)?n[i]=this.setParent(this.hydrateAstNode(a,r),n):be(a)?n[i]=this.hydrateReference(a,n,i,r):a!==void 0&&(n[i]=a);return n}setParent(e,r){return e.$container=r,e}hydrateReference(e,r,n,i){return this.linker.buildReference(r,n,i.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,r,n=0){let i=r.cstNodes.get(e);if(typeof e.grammarSource=="number"&&(i.grammarSource=this.getGrammarElement(e.grammarSource)),i.astNode=r.astNodes.get(e.astNode),ht(i))for(let a of e.content){let o=this.hydrateCstNode(a,r,n++);i.content.push(o)}return i}hydrateCstLeafNode(e){let r=this.getTokenType(e.tokenType),n=e.offset,i=e.length,a=e.startLine,o=e.startColumn,l=e.endLine,u=e.endColumn,c=e.hidden;return new Qr(n,i,{start:{line:a,character:o},end:{line:l,character:u}},r,c)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){if(e)return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.getKey(e)}createGrammarElementIdMap(){let e=0;for(let r of ct(this.grammar))ds(r)&&this.grammarElementIdMap.set(r,e++)}};function pf(t){return{documentation:{CommentProvider:s(e=>new Sa(e),"CommentProvider"),DocumentationProvider:s(e=>new ka(e),"DocumentationProvider")},parser:{AsyncParser:s(e=>new Ca(e),"AsyncParser"),GrammarConfig:s(e=>Gu(e),"GrammarConfig"),LangiumParser:s(e=>Kc(e),"LangiumParser"),CompletionParser:s(e=>Wc(e),"CompletionParser"),ValueConverter:s(()=>new rn,"ValueConverter"),TokenBuilder:s(()=>new sr,"TokenBuilder"),Lexer:s(e=>new on(e),"Lexer"),ParserErrorMessageProvider:s(()=>new Xi,"ParserErrorMessageProvider"),LexerErrorMessageProvider:s(()=>new Ea,"LexerErrorMessageProvider")},workspace:{AstNodeLocator:s(()=>new ya,"AstNodeLocator"),AstNodeDescriptionProvider:s(e=>new ha(e),"AstNodeDescriptionProvider"),ReferenceDescriptionProvider:s(e=>new ga(e),"ReferenceDescriptionProvider")},references:{Linker:s(e=>new ia(e),"Linker"),NameProvider:s(()=>new sa,"NameProvider"),ScopeProvider:s(e=>new ca(e),"ScopeProvider"),ScopeComputation:s(e=>new oa(e),"ScopeComputation"),References:s(e=>new aa(e),"References")},serializer:{Hydrator:s(e=>new wa(e),"Hydrator"),JsonSerializer:s(e=>new fa(e),"JsonSerializer")},validation:{DocumentValidator:s(e=>new ma(e),"DocumentValidator"),ValidationRegistry:s(e=>new pa(e),"ValidationRegistry")},shared:s(()=>t.shared,"shared")}}s(pf,"createDefaultCoreModule");function mf(t){return{ServiceRegistry:s(e=>new da(e),"ServiceRegistry"),workspace:{LangiumDocuments:s(e=>new na(e),"LangiumDocuments"),LangiumDocumentFactory:s(e=>new ra(e),"LangiumDocumentFactory"),DocumentBuilder:s(e=>new Ta(e),"DocumentBuilder"),IndexManager:s(e=>new Ra(e),"IndexManager"),WorkspaceManager:s(e=>new Aa(e),"WorkspaceManager"),FileSystemProvider:s(e=>t.fileSystemProvider(e),"FileSystemProvider"),WorkspaceLock:s(()=>new Na,"WorkspaceLock"),ConfigurationProvider:s(e=>new xa(e),"ConfigurationProvider")}}}s(mf,"createDefaultSharedCoreModule");var hf;(function(t){t.merge=(e,r)=>Ol(Ol({},e),r)})(hf||(hf={}));function Ll(t,e,r,n,i,a,o,l,u){let c=[t,e,r,n,i,a,o,l,u].reduce(Ol,{});return Hy(c)}s(Ll,"inject");var Wy=Symbol("isProxy");function Ky(t){if(t&&t[Wy])for(let e of Object.values(t))Ky(e);return t}s(Ky,"eagerLoad");function Hy(t,e){let r=new Proxy({},{deleteProperty:s(()=>!1,"deleteProperty"),set:s(()=>{throw new Error("Cannot set property on injected service container")},"set"),get:s((n,i)=>i===Wy?!0:jy(n,i,t,e||r),"get"),getOwnPropertyDescriptor:s((n,i)=>(jy(n,i,t,e||r),Object.getOwnPropertyDescriptor(n,i)),"getOwnPropertyDescriptor"),has:s((n,i)=>i in t,"has"),ownKeys:s(()=>[...Object.getOwnPropertyNames(t)],"ownKeys")});return r}s(Hy,"_inject");var By=Symbol();function jy(t,e,r,n){if(e in t){if(t[e]instanceof Error)throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:t[e]});if(t[e]===By)throw new Error('Cycle detected. Please make "'+String(e)+'" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');return t[e]}else if(e in r){let i=r[e];t[e]=By;try{t[e]=typeof i=="function"?i(n):Hy(i,n)}catch(a){throw t[e]=a instanceof Error?a:void 0,a}return t[e]}else return}s(jy,"_resolve");function Ol(t,e){if(e){for(let[r,n]of Object.entries(e))if(n!==void 0){let i=t[r];i!==null&&n!==null&&typeof i=="object"&&typeof n=="object"?t[r]=Ol(i,n):t[r]=n}}return t}s(Ol,"_merge");var gf={indentTokenName:"INDENT",dedentTokenName:"DEDENT",whitespaceTokenName:"WS",ignoreIndentationDelimiters:[]},ln;(function(t){t.REGULAR="indentation-sensitive",t.IGNORE_INDENTATION="ignore-indentation"})(ln||(ln={}));var Pl=class extends sr{static{s(this,"IndentationAwareTokenBuilder")}constructor(e=gf){super(),this.indentationStack=[0],this.whitespaceRegExp=/[ \t]+/y,this.options=Object.assign(Object.assign({},gf),e),this.indentTokenType=kr({name:this.options.indentTokenName,pattern:this.indentMatcher.bind(this),line_breaks:!1}),this.dedentTokenType=kr({name:this.options.dedentTokenName,pattern:this.dedentMatcher.bind(this),line_breaks:!1})}buildTokens(e,r){let n=super.buildTokens(e,r);if(!_l(n))throw new Error("Invalid tokens built by default builder");let{indentTokenName:i,dedentTokenName:a,whitespaceTokenName:o,ignoreIndentationDelimiters:l}=this.options,u,c,f,d=[];for(let p of n){for(let[m,g]of l)p.name===m?p.PUSH_MODE=ln.IGNORE_INDENTATION:p.name===g&&(p.POP_MODE=!0);p.name===a?u=p:p.name===i?c=p:p.name===o?f=p:d.push(p)}if(!u||!c||!f)throw new Error("Some indentation/whitespace tokens not found!");return l.length>0?{modes:{[ln.REGULAR]:[u,c,...d,f],[ln.IGNORE_INDENTATION]:[...d,f]},defaultMode:ln.REGULAR}:[u,c,f,...d]}flushLexingReport(e){let r=super.flushLexingReport(e);return Object.assign(Object.assign({},r),{remainingDedents:this.flushRemainingDedents(e)})}isStartOfLine(e,r){return r===0||`\r
`.includes(e[r-1])}matchWhitespace(e,r,n,i){var a;this.whitespaceRegExp.lastIndex=r;let o=this.whitespaceRegExp.exec(e);return{currIndentLevel:(a=o?.[0].length)!==null&&a!==void 0?a:0,prevIndentLevel:this.indentationStack.at(-1),match:o}}createIndentationTokenInstance(e,r,n,i){let a=this.getLineNumber(r,i);return tr(e,n,i,i+n.length,a,a,1,n.length)}getLineNumber(e,r){return e.substring(0,r).split(/\r\n|\r|\n/).length}indentMatcher(e,r,n,i){if(!this.isStartOfLine(e,r))return null;let{currIndentLevel:a,prevIndentLevel:o,match:l}=this.matchWhitespace(e,r,n,i);return a<=o?null:(this.indentationStack.push(a),l)}dedentMatcher(e,r,n,i){var a,o,l,u;if(!this.isStartOfLine(e,r))return null;let{currIndentLevel:c,prevIndentLevel:f,match:d}=this.matchWhitespace(e,r,n,i);if(c>=f)return null;let p=this.indentationStack.lastIndexOf(c);if(p===-1)return this.diagnostics.push({severity:"error",message:`Invalid dedent level ${c} at offset: ${r}. Current indentation stack: ${this.indentationStack}`,offset:r,length:(o=(a=d?.[0])===null||a===void 0?void 0:a.length)!==null&&o!==void 0?o:0,line:this.getLineNumber(e,r),column:1}),null;let m=this.indentationStack.length-p-1,g=(u=(l=e.substring(0,r).match(/[\r\n]+$/))===null||l===void 0?void 0:l[0].length)!==null&&u!==void 0?u:1;for(let y=0;y<m;y++){let A=this.createIndentationTokenInstance(this.dedentTokenType,e,"",r-(g-1));n.push(A),this.indentationStack.pop()}return null}buildTerminalToken(e){let r=super.buildTerminalToken(e),{indentTokenName:n,dedentTokenName:i,whitespaceTokenName:a}=this.options;return r.name===n?this.indentTokenType:r.name===i?this.dedentTokenType:r.name===a?kr({name:a,pattern:this.whitespaceRegExp,group:ue.SKIPPED}):r}flushRemainingDedents(e){let r=[];for(;this.indentationStack.length>1;)r.push(this.createIndentationTokenInstance(this.dedentTokenType,e,"",e.length)),this.indentationStack.pop();return this.indentationStack=[0],r}},yf=class extends on{static{s(this,"IndentationAwareLexer")}constructor(e){if(super(e),e.parser.TokenBuilder instanceof Pl)this.indentationTokenBuilder=e.parser.TokenBuilder;else throw new Error("IndentationAwareLexer requires an accompanying IndentationAwareTokenBuilder")}tokenize(e,r=wl){let n=super.tokenize(e),i=n.report;r?.mode==="full"&&n.tokens.push(...i.remainingDedents),i.remainingDedents=[];let{indentTokenType:a,dedentTokenType:o}=this.indentationTokenBuilder,l=a.tokenTypeIdx,u=o.tokenTypeIdx,c=[],f=n.tokens.length-1;for(let d=0;d<f;d++){let p=n.tokens[d],m=n.tokens[d+1];if(p.tokenTypeIdx===l&&m.tokenTypeIdx===u){d++;continue}c.push(p)}return f>=0&&c.push(n.tokens[f]),n.tokens=c,n}};var W={};$r(W,{AstUtils:()=>qa,BiMap:()=>nn,Cancellation:()=>_,ContextCache:()=>sn,CstUtils:()=>Ga,DONE_RESULT:()=>Me,Deferred:()=>Xe,Disposable:()=>wr,DisposableCache:()=>ts,DocumentCache:()=>Cl,EMPTY_STREAM:()=>ss,ErrorWithLocation:()=>Dr,GrammarUtils:()=>Qa,MultiMap:()=>Rt,OperationCancelled:()=>Gt,Reduction:()=>fn,RegExpUtils:()=>Ja,SimpleCache:()=>ua,StreamImpl:()=>it,TreeStreamImpl:()=>At,URI:()=>Ye,UriUtils:()=>Je,WorkspaceCache:()=>rs,assertUnreachable:()=>vt,delayNextTick:()=>Qc,interruptAndCheck:()=>Te,isOperationCancelled:()=>Ut,loadGrammarFromJson:()=>ar,setInterruptionPeriod:()=>Sy,startCancelableOperation:()=>kl,stream:()=>H});B(W,ae);var Ml=class{static{s(this,"EmptyFileSystemProvider")}readFile(){throw new Error("No file system is available.")}async readDirectory(){return[]}},xf={fileSystemProvider:s(()=>new Ml,"fileSystemProvider")};var ew={Grammar:s(()=>{},"Grammar"),LanguageMetaData:s(()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"}),"LanguageMetaData")},tw={AstReflection:s(()=>new Jn,"AstReflection")};function rw(){let t=Ll(mf(xf),tw),e=Ll(pf({shared:t}),ew);return t.ServiceRegistry.register(e),e}s(rw,"createMinimalGrammarServices");function ar(t){var e;let r=rw(),n=r.serializer.JsonSerializer.deserialize(t);return r.shared.workspace.LangiumDocumentFactory.fromModel(n,Ye.parse(`memory://${(e=n.name)!==null&&e!==void 0?e:"grammar"}.langium`)),n}s(ar,"loadGrammarFromJson");B(Re,W);var Vy="Statement";var Bl="Architecture";function U6(t){return Bt.isInstance(t,Bl)}s(U6,"isArchitecture");var Dl="Axis";var _a="Branch";function B6(t){return Bt.isInstance(t,_a)}s(B6,"isBranch");var Fl="Checkout";var Gl="CherryPicking";var ba="Commit";function j6(t){return Bt.isInstance(t,ba)}s(j6,"isCommit");var Tf="Curve";var Rf="Edge";var Af="Entry";var $a="GitGraph";function W6(t){return Bt.isInstance(t,$a)}s(W6,"isGitGraph");var Ef="Group";var jl="Info";function K6(t){return Bt.isInstance(t,jl)}s(K6,"isInfo");var vf="Junction";var Oa="Merge";function H6(t){return Bt.isInstance(t,Oa)}s(H6,"isMerge");var If="Option";var Wl="Packet";function V6(t){return Bt.isInstance(t,Wl)}s(V6,"isPacket");var Kl="PacketBlock";function z6(t){return Bt.isInstance(t,Kl)}s(z6,"isPacketBlock");var Hl="Pie";function q6(t){return Bt.isInstance(t,Hl)}s(q6,"isPie");var Vl="PieSection";function X6(t){return Bt.isInstance(t,Vl)}s(X6,"isPieSection");var kf="Radar";var Sf="Service";var Ul="Direction";var La=class extends Or{static{s(this,"MermaidAstReflection")}getAllTypes(){return[Bl,Dl,_a,Fl,Gl,ba,Tf,Ul,Rf,Af,$a,Ef,jl,vf,Oa,If,Wl,Kl,Hl,Vl,kf,Sf,Vy]}computeIsSubtype(e,r){switch(e){case _a:case Fl:case Gl:case ba:case Oa:return this.isSubtype(Vy,r);case Ul:return this.isSubtype($a,r);default:return!1}}getReferenceType(e){let r=`${e.container.$type}:${e.property}`;switch(r){case"Entry:axis":return Dl;default:throw new Error(`${r} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case Bl:return{name:Bl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case Dl:return{name:Dl,properties:[{name:"label"},{name:"name"}]};case _a:return{name:_a,properties:[{name:"name"},{name:"order"}]};case Fl:return{name:Fl,properties:[{name:"branch"}]};case Gl:return{name:Gl,properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case ba:return{name:ba,properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case Tf:return{name:Tf,properties:[{name:"entries",defaultValue:[]},{name:"label"},{name:"name"}]};case Rf:return{name:Rf,properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case Af:return{name:Af,properties:[{name:"axis"},{name:"value"}]};case $a:return{name:$a,properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case Ef:return{name:Ef,properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case jl:return{name:jl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case vf:return{name:vf,properties:[{name:"id"},{name:"in"}]};case Oa:return{name:Oa,properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case If:return{name:If,properties:[{name:"name"},{name:"value",defaultValue:!1}]};case Wl:return{name:Wl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case Kl:return{name:Kl,properties:[{name:"bits"},{name:"end"},{name:"label"},{name:"start"}]};case Hl:return{name:Hl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case Vl:return{name:Vl,properties:[{name:"label"},{name:"value"}]};case kf:return{name:kf,properties:[{name:"accDescr"},{name:"accTitle"},{name:"axes",defaultValue:[]},{name:"curves",defaultValue:[]},{name:"options",defaultValue:[]},{name:"title"}]};case Sf:return{name:Sf,properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case Ul:return{name:Ul,properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};default:return{name:e,properties:[]}}}},Bt=new La;var zy,Qy=s(()=>zy??(zy=ar(`{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Info","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@7"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"InfoGrammar"),qy,ex=s(()=>qy??(qy=ar(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Packet","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"packet-beta"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}],"cardinality":"?"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"+"},{"$type":"Assignment","feature":"bits","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]}]},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@9"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"PacketGrammar"),Xy,tx=s(()=>Xy??(Xy=ar(`{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Pie","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@9"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"PieGrammar"),Yy,rx=s(()=>Yy??(Yy=ar(`{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Architecture","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"LeftPort","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"RightPort","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Arrow","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@28"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@28"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@18"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@19"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"ArchitectureGrammar"),Jy,nx=s(()=>Jy??(Jy=ar(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"GitGraph","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@14"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@15"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"TerminalRule","name":"REFERENCE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"GitGraphGrammar"),Zy,ix=s(()=>Zy??(Zy=ar(`{"$type":"Grammar","isDeclared":true,"name":"Radar","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Radar","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":"radar-beta:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},{"$type":"Group","elements":[{"$type":"Keyword","value":"axis"},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"curve"},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Label","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"["},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}},{"$type":"Keyword","value":"]"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Axis","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Curve","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"},{"$type":"Keyword","value":"{"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Keyword","value":"}"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Entries","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"DetailedEntry","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"axis","operator":"=","terminal":{"$type":"CrossReference","type":{"$ref":"#/rules@2"},"terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},"deprecatedSyntax":false}},{"$type":"Keyword","value":":","cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"NumberEntry","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Option","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"showLegend"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"ticks"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"max"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"min"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"graticule"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"GRATICULE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"circle"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"polygon"}}]},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@15"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@16"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"interfaces":[{"$type":"Interface","name":"Entry","attributes":[{"$type":"TypeAttribute","name":"axis","isOptional":true,"type":{"$type":"ReferenceType","referenceType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@2"}}}},{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}],"superTypes":[]}],"definesHiddenTokens":false,"hiddenTokens":[],"types":[],"usedGrammars":[]}`)),"RadarGrammar");var nw={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},iw={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},sw={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},aw={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},ow={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},lw={languageId:"radar",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},n8={AstReflection:s(()=>new La,"AstReflection")},i8={Grammar:s(()=>Qy(),"Grammar"),LanguageMetaData:s(()=>nw,"LanguageMetaData"),parser:{}},s8={Grammar:s(()=>ex(),"Grammar"),LanguageMetaData:s(()=>iw,"LanguageMetaData"),parser:{}},a8={Grammar:s(()=>tx(),"Grammar"),LanguageMetaData:s(()=>sw,"LanguageMetaData"),parser:{}},o8={Grammar:s(()=>rx(),"Grammar"),LanguageMetaData:s(()=>aw,"LanguageMetaData"),parser:{}},l8={Grammar:s(()=>nx(),"Grammar"),LanguageMetaData:s(()=>ow,"LanguageMetaData"),parser:{}},u8={Grammar:s(()=>ix(),"Grammar"),LanguageMetaData:s(()=>lw,"LanguageMetaData"),parser:{}};var sx=/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,ax=/accTitle[\t ]*:([^\n\r]*)/,ox=/title([\t ][^\n\r]*|)/;var uw={ACC_DESCR:sx,ACC_TITLE:ax,TITLE:ox},Cf=class extends rn{static{s(this,"AbstractMermaidValueConverter")}runConverter(e,r,n){let i=this.runCommonConverter(e,r,n);return i===void 0&&(i=this.runCustomConverter(e,r,n)),i===void 0?super.runConverter(e,r,n):i}runCommonConverter(e,r,n){let i=uw[e.name];if(i===void 0)return;let a=i.exec(r);if(a!==null){if(a[1]!==void 0)return a[1].trim().replace(/[\t ]{2,}/gm," ");if(a[2]!==void 0)return a[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,`
`)}}},lx=class extends Cf{static{s(this,"CommonValueConverter")}runCustomConverter(e,r,n){}};var Nf=class extends sr{static{s(this,"AbstractMermaidTokenBuilder")}constructor(e){super(),this.keywords=new Set(e)}buildKeywordTokens(e,r,n){let i=super.buildKeywordTokens(e,r,n);return i.forEach(a=>{this.keywords.has(a.name)&&a.PATTERN!==void 0&&(a.PATTERN=new RegExp(a.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),i}},ux=class extends Nf{static{s(this,"CommonTokenBuilder")}};export{s as a,pf as b,mf as c,Ll as d,xf as e,Re as f,Vy as g,Bl as h,U6 as i,_a as j,B6 as k,ba as l,j6 as m,$a as n,W6 as o,jl as p,K6 as q,Oa as r,H6 as s,Wl as t,V6 as u,Kl as v,z6 as w,Hl as x,q6 as y,Vl as z,X6 as A,kf as B,n8 as C,i8 as D,s8 as E,a8 as F,o8 as G,l8 as H,u8 as I,Cf as J,lx as K,Nf as L,ux as M};
/*! Bundled license information:

lodash-es/lodash.js:
  (**
   * @license
   * Lodash (Custom Build) <https://lodash.com/>
   * Build: `lodash modularize exports="es" -o ./`
   * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
   * Released under MIT license <https://lodash.com/license>
   * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
   * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
   *)
*/
