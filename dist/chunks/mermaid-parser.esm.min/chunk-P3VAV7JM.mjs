import{C as u,E as s,K as m,L as l,a as e,b as c,c as d,d as o,e as n,f as S}from"./chunk-AWU6ROIC.mjs";var r=class extends l{static{e(this,"PacketTokenBuilder")}constructor(){super(["packet-beta"])}};var v={parser:{TokenBuilder:e(()=>new r,"TokenBuilder"),ValueConverter:e(()=>new m,"ValueConverter")}};function h(a=n){let t=o(d(a),u),i=o(c({shared:t}),s,v);return t.ServiceRegistry.register(i),{shared:t,Packet:i}}e(h,"createPacketServices");export{v as a,h as b};
