{"version": 3, "sources": ["../../../src/language/generated/ast.ts", "../../../src/language/generated/grammar.ts", "../../../src/language/generated/module.ts", "../../../src/language/common/valueConverter.ts", "../../../src/language/common/matcher.ts", "../../../src/language/common/tokenBuilder.ts"], "sourcesContent": ["/******************************************************************************\n * This file was generated by langium-cli 3.3.0.\n * DO NOT EDIT MANUALLY!\n ******************************************************************************/\n\n/* eslint-disable */\nimport type { AstNode, Reference, ReferenceInfo, TypeMetaData } from 'langium';\nimport { AbstractAstReflection } from 'langium';\n\nexport const MermaidTerminals = {\n    ACC_DESCR: /[\\t ]*accDescr(?:[\\t ]*:([^\\n\\r]*?(?=%%)|[^\\n\\r]*)|\\s*{([^}]*)})/,\n    ACC_TITLE: /[\\t ]*accTitle[\\t ]*:(?:[^\\n\\r]*?(?=%%)|[^\\n\\r]*)/,\n    TITLE: /[\\t ]*title(?:[\\t ][^\\n\\r]*?(?=%%)|[\\t ][^\\n\\r]*|)/,\n    NEWLINE: /\\r?\\n/,\n    WHITESPACE: /[\\t ]+/,\n    YAML: /---[\\t ]*\\r?\\n(?:[\\S\\s]*?\\r?\\n)?---(?:\\r?\\n|(?!\\S))/,\n    DIRECTIVE: /[\\t ]*%%{[\\S\\s]*?}%%(?:\\r?\\n|(?!\\S))/,\n    SINGLE_LINE_COMMENT: /[\\t ]*%%[^\\n\\r]*/,\n    INT: /0|[1-9][0-9]*(?!\\.)/,\n    STRING: /\"([^\"\\\\]|\\\\.)*\"|'([^'\\\\]|\\\\.)*'/,\n    NUMBER: /(([0-9]+\\.[0-9]+(?!\\.))|(0|[1-9][0-9]*(?!\\.)))/,\n    ARROW_DIRECTION: /(((L|R)|T)|B)/,\n    ARROW_GROUP: /\\{group\\}/,\n    ARROW_INTO: /<|>/,\n    ID: /[\\w]([-\\w]*\\w)?/,\n    ARCH_ICON: /\\([\\w-:]+\\)/,\n    ARCH_TITLE: /\\[[\\w ]+\\]/,\n    REFERENCE: /\\w([-\\./\\w]*[-\\w])?/,\n    GRATICULE: /(circle|polygon)/,\n    BOOLEAN: /(true|false)/,\n};\n\nexport type MermaidTerminalNames = keyof typeof MermaidTerminals;\n\nexport type MermaidKeywordNames = \n    | \"+\"\n    | \",\"\n    | \"-\"\n    | \"--\"\n    | \":\"\n    | \"BT\"\n    | \"HIGHLIGHT\"\n    | \"LR\"\n    | \"NORMAL\"\n    | \"REVERSE\"\n    | \"TB\"\n    | \"[\"\n    | \"]\"\n    | \"architecture-beta\"\n    | \"axis\"\n    | \"branch\"\n    | \"checkout\"\n    | \"cherry-pick\"\n    | \"commit\"\n    | \"curve\"\n    | \"gitGraph\"\n    | \"gitGraph:\"\n    | \"graticule\"\n    | \"group\"\n    | \"id:\"\n    | \"in\"\n    | \"info\"\n    | \"junction\"\n    | \"max\"\n    | \"merge\"\n    | \"min\"\n    | \"msg:\"\n    | \"order:\"\n    | \"packet-beta\"\n    | \"parent:\"\n    | \"pie\"\n    | \"radar-beta\"\n    | \"radar-beta:\"\n    | \"service\"\n    | \"showData\"\n    | \"showInfo\"\n    | \"showLegend\"\n    | \"switch\"\n    | \"tag:\"\n    | \"ticks\"\n    | \"type:\"\n    | \"{\"\n    | \"}\";\n\nexport type MermaidTokenNames = MermaidTerminalNames | MermaidKeywordNames;\n\nexport type Statement = Branch | Checkout | CherryPicking | Commit | Merge;\n\nexport const Statement = 'Statement';\n\nexport function isStatement(item: unknown): item is Statement {\n    return reflection.isInstance(item, Statement);\n}\n\nexport interface Architecture extends AstNode {\n    readonly $type: 'Architecture';\n    accDescr?: string;\n    accTitle?: string;\n    edges: Array<Edge>;\n    groups: Array<Group>;\n    junctions: Array<Junction>;\n    services: Array<Service>;\n    title?: string;\n}\n\nexport const Architecture = 'Architecture';\n\nexport function isArchitecture(item: unknown): item is Architecture {\n    return reflection.isInstance(item, Architecture);\n}\n\nexport interface Axis extends AstNode {\n    readonly $container: Radar;\n    readonly $type: 'Axis';\n    label?: string;\n    name: string;\n}\n\nexport const Axis = 'Axis';\n\nexport function isAxis(item: unknown): item is Axis {\n    return reflection.isInstance(item, Axis);\n}\n\nexport interface Branch extends AstNode {\n    readonly $container: GitGraph;\n    readonly $type: 'Branch';\n    name: string;\n    order?: number;\n}\n\nexport const Branch = 'Branch';\n\nexport function isBranch(item: unknown): item is Branch {\n    return reflection.isInstance(item, Branch);\n}\n\nexport interface Checkout extends AstNode {\n    readonly $container: GitGraph;\n    readonly $type: 'Checkout';\n    branch: string;\n}\n\nexport const Checkout = 'Checkout';\n\nexport function isCheckout(item: unknown): item is Checkout {\n    return reflection.isInstance(item, Checkout);\n}\n\nexport interface CherryPicking extends AstNode {\n    readonly $container: GitGraph;\n    readonly $type: 'CherryPicking';\n    id?: string;\n    parent?: string;\n    tags: Array<string>;\n}\n\nexport const CherryPicking = 'CherryPicking';\n\nexport function isCherryPicking(item: unknown): item is CherryPicking {\n    return reflection.isInstance(item, CherryPicking);\n}\n\nexport interface Commit extends AstNode {\n    readonly $container: GitGraph;\n    readonly $type: 'Commit';\n    id?: string;\n    message?: string;\n    tags: Array<string>;\n    type?: 'HIGHLIGHT' | 'NORMAL' | 'REVERSE';\n}\n\nexport const Commit = 'Commit';\n\nexport function isCommit(item: unknown): item is Commit {\n    return reflection.isInstance(item, Commit);\n}\n\nexport interface Curve extends AstNode {\n    readonly $container: Radar;\n    readonly $type: 'Curve';\n    entries: Array<Entry>;\n    label?: string;\n    name: string;\n}\n\nexport const Curve = 'Curve';\n\nexport function isCurve(item: unknown): item is Curve {\n    return reflection.isInstance(item, Curve);\n}\n\nexport interface Edge extends AstNode {\n    readonly $container: Architecture;\n    readonly $type: 'Edge';\n    lhsDir: string;\n    lhsGroup: boolean;\n    lhsId: string;\n    lhsInto: boolean;\n    rhsDir: string;\n    rhsGroup: boolean;\n    rhsId: string;\n    rhsInto: boolean;\n    title?: string;\n}\n\nexport const Edge = 'Edge';\n\nexport function isEdge(item: unknown): item is Edge {\n    return reflection.isInstance(item, Edge);\n}\n\nexport interface Entry extends AstNode {\n    readonly $container: Curve;\n    readonly $type: 'Entry';\n    axis?: Reference<Axis>;\n    value: number;\n}\n\nexport const Entry = 'Entry';\n\nexport function isEntry(item: unknown): item is Entry {\n    return reflection.isInstance(item, Entry);\n}\n\nexport interface GitGraph extends AstNode {\n    readonly $type: 'Direction' | 'GitGraph';\n    accDescr?: string;\n    accTitle?: string;\n    statements: Array<Statement>;\n    title?: string;\n}\n\nexport const GitGraph = 'GitGraph';\n\nexport function isGitGraph(item: unknown): item is GitGraph {\n    return reflection.isInstance(item, GitGraph);\n}\n\nexport interface Group extends AstNode {\n    readonly $container: Architecture;\n    readonly $type: 'Group';\n    icon?: string;\n    id: string;\n    in?: string;\n    title?: string;\n}\n\nexport const Group = 'Group';\n\nexport function isGroup(item: unknown): item is Group {\n    return reflection.isInstance(item, Group);\n}\n\nexport interface Info extends AstNode {\n    readonly $type: 'Info';\n    accDescr?: string;\n    accTitle?: string;\n    title?: string;\n}\n\nexport const Info = 'Info';\n\nexport function isInfo(item: unknown): item is Info {\n    return reflection.isInstance(item, Info);\n}\n\nexport interface Junction extends AstNode {\n    readonly $container: Architecture;\n    readonly $type: 'Junction';\n    id: string;\n    in?: string;\n}\n\nexport const Junction = 'Junction';\n\nexport function isJunction(item: unknown): item is Junction {\n    return reflection.isInstance(item, Junction);\n}\n\nexport interface Merge extends AstNode {\n    readonly $container: GitGraph;\n    readonly $type: 'Merge';\n    branch: string;\n    id?: string;\n    tags: Array<string>;\n    type?: 'HIGHLIGHT' | 'NORMAL' | 'REVERSE';\n}\n\nexport const Merge = 'Merge';\n\nexport function isMerge(item: unknown): item is Merge {\n    return reflection.isInstance(item, Merge);\n}\n\nexport interface Option extends AstNode {\n    readonly $container: Radar;\n    readonly $type: 'Option';\n    name: 'graticule' | 'max' | 'min' | 'showLegend' | 'ticks';\n    value: boolean | number | string;\n}\n\nexport const Option = 'Option';\n\nexport function isOption(item: unknown): item is Option {\n    return reflection.isInstance(item, Option);\n}\n\nexport interface Packet extends AstNode {\n    readonly $type: 'Packet';\n    accDescr?: string;\n    accTitle?: string;\n    blocks: Array<PacketBlock>;\n    title?: string;\n}\n\nexport const Packet = 'Packet';\n\nexport function isPacket(item: unknown): item is Packet {\n    return reflection.isInstance(item, Packet);\n}\n\nexport interface PacketBlock extends AstNode {\n    readonly $container: Packet;\n    readonly $type: 'PacketBlock';\n    bits?: number;\n    end?: number;\n    label: string;\n    start?: number;\n}\n\nexport const PacketBlock = 'PacketBlock';\n\nexport function isPacketBlock(item: unknown): item is PacketBlock {\n    return reflection.isInstance(item, PacketBlock);\n}\n\nexport interface Pie extends AstNode {\n    readonly $type: 'Pie';\n    accDescr?: string;\n    accTitle?: string;\n    sections: Array<PieSection>;\n    showData: boolean;\n    title?: string;\n}\n\nexport const Pie = 'Pie';\n\nexport function isPie(item: unknown): item is Pie {\n    return reflection.isInstance(item, Pie);\n}\n\nexport interface PieSection extends AstNode {\n    readonly $container: Pie;\n    readonly $type: 'PieSection';\n    label: string;\n    value: number;\n}\n\nexport const PieSection = 'PieSection';\n\nexport function isPieSection(item: unknown): item is PieSection {\n    return reflection.isInstance(item, PieSection);\n}\n\nexport interface Radar extends AstNode {\n    readonly $type: 'Radar';\n    accDescr?: string;\n    accTitle?: string;\n    axes: Array<Axis>;\n    curves: Array<Curve>;\n    options: Array<Option>;\n    title?: string;\n}\n\nexport const Radar = 'Radar';\n\nexport function isRadar(item: unknown): item is Radar {\n    return reflection.isInstance(item, Radar);\n}\n\nexport interface Service extends AstNode {\n    readonly $container: Architecture;\n    readonly $type: 'Service';\n    icon?: string;\n    iconText?: string;\n    id: string;\n    in?: string;\n    title?: string;\n}\n\nexport const Service = 'Service';\n\nexport function isService(item: unknown): item is Service {\n    return reflection.isInstance(item, Service);\n}\n\nexport interface Direction extends GitGraph {\n    readonly $type: 'Direction';\n    dir: 'BT' | 'LR' | 'TB';\n}\n\nexport const Direction = 'Direction';\n\nexport function isDirection(item: unknown): item is Direction {\n    return reflection.isInstance(item, Direction);\n}\n\nexport type MermaidAstType = {\n    Architecture: Architecture\n    Axis: Axis\n    Branch: Branch\n    Checkout: Checkout\n    CherryPicking: CherryPicking\n    Commit: Commit\n    Curve: Curve\n    Direction: Direction\n    Edge: Edge\n    Entry: Entry\n    GitGraph: GitGraph\n    Group: Group\n    Info: Info\n    Junction: Junction\n    Merge: Merge\n    Option: Option\n    Packet: Packet\n    PacketBlock: PacketBlock\n    Pie: Pie\n    PieSection: PieSection\n    Radar: Radar\n    Service: Service\n    Statement: Statement\n}\n\nexport class MermaidAstReflection extends AbstractAstReflection {\n\n    getAllTypes(): string[] {\n        return [Architecture, Axis, Branch, Checkout, CherryPicking, Commit, Curve, Direction, Edge, Entry, GitGraph, Group, Info, Junction, Merge, Option, Packet, PacketBlock, Pie, PieSection, Radar, Service, Statement];\n    }\n\n    protected override computeIsSubtype(subtype: string, supertype: string): boolean {\n        switch (subtype) {\n            case Branch:\n            case Checkout:\n            case CherryPicking:\n            case Commit:\n            case Merge: {\n                return this.isSubtype(Statement, supertype);\n            }\n            case Direction: {\n                return this.isSubtype(GitGraph, supertype);\n            }\n            default: {\n                return false;\n            }\n        }\n    }\n\n    getReferenceType(refInfo: ReferenceInfo): string {\n        const referenceId = `${refInfo.container.$type}:${refInfo.property}`;\n        switch (referenceId) {\n            case 'Entry:axis': {\n                return Axis;\n            }\n            default: {\n                throw new Error(`${referenceId} is not a valid reference id.`);\n            }\n        }\n    }\n\n    getTypeMetaData(type: string): TypeMetaData {\n        switch (type) {\n            case Architecture: {\n                return {\n                    name: Architecture,\n                    properties: [\n                        { name: 'accDescr' },\n                        { name: 'accTitle' },\n                        { name: 'edges', defaultValue: [] },\n                        { name: 'groups', defaultValue: [] },\n                        { name: 'junctions', defaultValue: [] },\n                        { name: 'services', defaultValue: [] },\n                        { name: 'title' }\n                    ]\n                };\n            }\n            case Axis: {\n                return {\n                    name: Axis,\n                    properties: [\n                        { name: 'label' },\n                        { name: 'name' }\n                    ]\n                };\n            }\n            case Branch: {\n                return {\n                    name: Branch,\n                    properties: [\n                        { name: 'name' },\n                        { name: 'order' }\n                    ]\n                };\n            }\n            case Checkout: {\n                return {\n                    name: Checkout,\n                    properties: [\n                        { name: 'branch' }\n                    ]\n                };\n            }\n            case CherryPicking: {\n                return {\n                    name: CherryPicking,\n                    properties: [\n                        { name: 'id' },\n                        { name: 'parent' },\n                        { name: 'tags', defaultValue: [] }\n                    ]\n                };\n            }\n            case Commit: {\n                return {\n                    name: Commit,\n                    properties: [\n                        { name: 'id' },\n                        { name: 'message' },\n                        { name: 'tags', defaultValue: [] },\n                        { name: 'type' }\n                    ]\n                };\n            }\n            case Curve: {\n                return {\n                    name: Curve,\n                    properties: [\n                        { name: 'entries', defaultValue: [] },\n                        { name: 'label' },\n                        { name: 'name' }\n                    ]\n                };\n            }\n            case Edge: {\n                return {\n                    name: Edge,\n                    properties: [\n                        { name: 'lhsDir' },\n                        { name: 'lhsGroup', defaultValue: false },\n                        { name: 'lhsId' },\n                        { name: 'lhsInto', defaultValue: false },\n                        { name: 'rhsDir' },\n                        { name: 'rhsGroup', defaultValue: false },\n                        { name: 'rhsId' },\n                        { name: 'rhsInto', defaultValue: false },\n                        { name: 'title' }\n                    ]\n                };\n            }\n            case Entry: {\n                return {\n                    name: Entry,\n                    properties: [\n                        { name: 'axis' },\n                        { name: 'value' }\n                    ]\n                };\n            }\n            case GitGraph: {\n                return {\n                    name: GitGraph,\n                    properties: [\n                        { name: 'accDescr' },\n                        { name: 'accTitle' },\n                        { name: 'statements', defaultValue: [] },\n                        { name: 'title' }\n                    ]\n                };\n            }\n            case Group: {\n                return {\n                    name: Group,\n                    properties: [\n                        { name: 'icon' },\n                        { name: 'id' },\n                        { name: 'in' },\n                        { name: 'title' }\n                    ]\n                };\n            }\n            case Info: {\n                return {\n                    name: Info,\n                    properties: [\n                        { name: 'accDescr' },\n                        { name: 'accTitle' },\n                        { name: 'title' }\n                    ]\n                };\n            }\n            case Junction: {\n                return {\n                    name: Junction,\n                    properties: [\n                        { name: 'id' },\n                        { name: 'in' }\n                    ]\n                };\n            }\n            case Merge: {\n                return {\n                    name: Merge,\n                    properties: [\n                        { name: 'branch' },\n                        { name: 'id' },\n                        { name: 'tags', defaultValue: [] },\n                        { name: 'type' }\n                    ]\n                };\n            }\n            case Option: {\n                return {\n                    name: Option,\n                    properties: [\n                        { name: 'name' },\n                        { name: 'value', defaultValue: false }\n                    ]\n                };\n            }\n            case Packet: {\n                return {\n                    name: Packet,\n                    properties: [\n                        { name: 'accDescr' },\n                        { name: 'accTitle' },\n                        { name: 'blocks', defaultValue: [] },\n                        { name: 'title' }\n                    ]\n                };\n            }\n            case PacketBlock: {\n                return {\n                    name: PacketBlock,\n                    properties: [\n                        { name: 'bits' },\n                        { name: 'end' },\n                        { name: 'label' },\n                        { name: 'start' }\n                    ]\n                };\n            }\n            case Pie: {\n                return {\n                    name: Pie,\n                    properties: [\n                        { name: 'accDescr' },\n                        { name: 'accTitle' },\n                        { name: 'sections', defaultValue: [] },\n                        { name: 'showData', defaultValue: false },\n                        { name: 'title' }\n                    ]\n                };\n            }\n            case PieSection: {\n                return {\n                    name: PieSection,\n                    properties: [\n                        { name: 'label' },\n                        { name: 'value' }\n                    ]\n                };\n            }\n            case Radar: {\n                return {\n                    name: Radar,\n                    properties: [\n                        { name: 'accDescr' },\n                        { name: 'accTitle' },\n                        { name: 'axes', defaultValue: [] },\n                        { name: 'curves', defaultValue: [] },\n                        { name: 'options', defaultValue: [] },\n                        { name: 'title' }\n                    ]\n                };\n            }\n            case Service: {\n                return {\n                    name: Service,\n                    properties: [\n                        { name: 'icon' },\n                        { name: 'iconText' },\n                        { name: 'id' },\n                        { name: 'in' },\n                        { name: 'title' }\n                    ]\n                };\n            }\n            case Direction: {\n                return {\n                    name: Direction,\n                    properties: [\n                        { name: 'accDescr' },\n                        { name: 'accTitle' },\n                        { name: 'dir' },\n                        { name: 'statements', defaultValue: [] },\n                        { name: 'title' }\n                    ]\n                };\n            }\n            default: {\n                return {\n                    name: type,\n                    properties: []\n                };\n            }\n        }\n    }\n}\n\nexport const reflection = new MermaidAstReflection();\n", "/******************************************************************************\n * This file was generated by langium-cli 3.3.0.\n * DO NOT EDIT MANUALLY!\n ******************************************************************************/\n\nimport type { Grammar } from 'langium';\nimport { loadGrammarFromJson } from 'langium';\n\nlet loadedInfoGrammar: Grammar | undefined;\nexport const InfoGrammar = (): Grammar => loadedInfoGrammar ?? (loadedInfoGrammar = loadGrammarFromJson('{\"$type\":\"Grammar\",\"isDeclared\":true,\"name\":\"Info\",\"imports\":[],\"rules\":[{\"$type\":\"ParserRule\",\"entry\":true,\"name\":\"Info\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@12\"},\"arguments\":[],\"cardinality\":\"*\"},{\"$type\":\"Keyword\",\"value\":\"info\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@12\"},\"arguments\":[],\"cardinality\":\"*\"},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"showInfo\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@12\"},\"arguments\":[],\"cardinality\":\"*\"}],\"cardinality\":\"?\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@2\"},\"arguments\":[],\"cardinality\":\"?\"}]},\"definesHiddenTokens\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"EOL\",\"dataType\":\"string\",\"definition\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@12\"},\"arguments\":[],\"cardinality\":\"+\"},{\"$type\":\"EndOfFile\"}]},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"TitleAndAccessibilities\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"accDescr\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@4\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"accTitle\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@5\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"title\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@6\"},\"arguments\":[]}}]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@1\"},\"arguments\":[]}],\"cardinality\":\"+\"},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"TerminalRule\",\"name\":\"BOOLEAN\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"boolean\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"true\"}},{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"false\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ACC_DESCR\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*accDescr(?:[\\\\\\\\t ]*:([^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[^\\\\\\\\n\\\\\\\\r]*)|\\\\\\\\s*{([^}]*)})/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ACC_TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*accTitle[\\\\\\\\t ]*:(?:[^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[^\\\\\\\\n\\\\\\\\r]*)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*title(?:[\\\\\\\\t ][^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[\\\\\\\\t ][^\\\\\\\\n\\\\\\\\r]*|)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"FLOAT\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[0-9]+\\\\\\\\.[0-9]+(?!\\\\\\\\.)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"INT\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/0|[1-9][0-9]*(?!\\\\\\\\.)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"NUMBER\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"TerminalRuleCall\",\"rule\":{\"$ref\":\"#/rules@7\"}},{\"$type\":\"TerminalRuleCall\",\"rule\":{\"$ref\":\"#/rules@8\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"STRING\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\"([^\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\"|\\'([^\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\'/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ID\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\w]([-\\\\\\\\w]*\\\\\\\\w)?/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"NEWLINE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\\\\r?\\\\\\\\n/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"WHITESPACE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]+/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"YAML\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/---[\\\\\\\\t ]*\\\\\\\\r?\\\\\\\\n(?:[\\\\\\\\S\\\\\\\\s]*?\\\\\\\\r?\\\\\\\\n)?---(?:\\\\\\\\r?\\\\\\\\n|(?!\\\\\\\\S))/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"DIRECTIVE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*%%{[\\\\\\\\S\\\\\\\\s]*?}%%(?:\\\\\\\\r?\\\\\\\\n|(?!\\\\\\\\S))/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"SINGLE_LINE_COMMENT\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*%%[^\\\\\\\\n\\\\\\\\r]*/\"},\"fragment\":false}],\"definesHiddenTokens\":false,\"hiddenTokens\":[],\"interfaces\":[],\"types\":[],\"usedGrammars\":[]}'));\n\nlet loadedPacketGrammar: Grammar | undefined;\nexport const PacketGrammar = (): Grammar => loadedPacketGrammar ?? (loadedPacketGrammar = loadGrammarFromJson('{\"$type\":\"Grammar\",\"isDeclared\":true,\"name\":\"Packet\",\"imports\":[],\"rules\":[{\"$type\":\"ParserRule\",\"entry\":true,\"name\":\"Packet\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@13\"},\"arguments\":[],\"cardinality\":\"*\"},{\"$type\":\"Keyword\",\"value\":\"packet-beta\"},{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@3\"},\"arguments\":[]},{\"$type\":\"Assignment\",\"feature\":\"blocks\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@1\"},\"arguments\":[]}},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@13\"},\"arguments\":[]}],\"cardinality\":\"*\"}]},\"definesHiddenTokens\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"PacketBlock\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"start\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@9\"},\"arguments\":[]}},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"-\"},{\"$type\":\"Assignment\",\"feature\":\"end\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@9\"},\"arguments\":[]}}],\"cardinality\":\"?\"}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"+\"},{\"$type\":\"Assignment\",\"feature\":\"bits\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@9\"},\"arguments\":[]}}]}]},{\"$type\":\"Keyword\",\"value\":\":\"},{\"$type\":\"Assignment\",\"feature\":\"label\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@11\"},\"arguments\":[]}},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@2\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"EOL\",\"dataType\":\"string\",\"definition\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@13\"},\"arguments\":[],\"cardinality\":\"+\"},{\"$type\":\"EndOfFile\"}]},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"TitleAndAccessibilities\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"accDescr\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@5\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"accTitle\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@6\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"title\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@7\"},\"arguments\":[]}}]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@2\"},\"arguments\":[]}],\"cardinality\":\"+\"},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"TerminalRule\",\"name\":\"BOOLEAN\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"boolean\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"true\"}},{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"false\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ACC_DESCR\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*accDescr(?:[\\\\\\\\t ]*:([^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[^\\\\\\\\n\\\\\\\\r]*)|\\\\\\\\s*{([^}]*)})/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ACC_TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*accTitle[\\\\\\\\t ]*:(?:[^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[^\\\\\\\\n\\\\\\\\r]*)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*title(?:[\\\\\\\\t ][^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[\\\\\\\\t ][^\\\\\\\\n\\\\\\\\r]*|)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"FLOAT\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[0-9]+\\\\\\\\.[0-9]+(?!\\\\\\\\.)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"INT\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/0|[1-9][0-9]*(?!\\\\\\\\.)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"NUMBER\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"TerminalRuleCall\",\"rule\":{\"$ref\":\"#/rules@8\"}},{\"$type\":\"TerminalRuleCall\",\"rule\":{\"$ref\":\"#/rules@9\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"STRING\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\"([^\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\"|\\'([^\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\'/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ID\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\w]([-\\\\\\\\w]*\\\\\\\\w)?/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"NEWLINE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\\\\r?\\\\\\\\n/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"WHITESPACE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]+/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"YAML\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/---[\\\\\\\\t ]*\\\\\\\\r?\\\\\\\\n(?:[\\\\\\\\S\\\\\\\\s]*?\\\\\\\\r?\\\\\\\\n)?---(?:\\\\\\\\r?\\\\\\\\n|(?!\\\\\\\\S))/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"DIRECTIVE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*%%{[\\\\\\\\S\\\\\\\\s]*?}%%(?:\\\\\\\\r?\\\\\\\\n|(?!\\\\\\\\S))/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"SINGLE_LINE_COMMENT\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*%%[^\\\\\\\\n\\\\\\\\r]*/\"},\"fragment\":false}],\"definesHiddenTokens\":false,\"hiddenTokens\":[],\"interfaces\":[],\"types\":[],\"usedGrammars\":[]}'));\n\nlet loadedPieGrammar: Grammar | undefined;\nexport const PieGrammar = (): Grammar => loadedPieGrammar ?? (loadedPieGrammar = loadGrammarFromJson('{\"$type\":\"Grammar\",\"isDeclared\":true,\"name\":\"Pie\",\"imports\":[],\"rules\":[{\"$type\":\"ParserRule\",\"entry\":true,\"name\":\"Pie\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@13\"},\"arguments\":[],\"cardinality\":\"*\"},{\"$type\":\"Keyword\",\"value\":\"pie\"},{\"$type\":\"Assignment\",\"feature\":\"showData\",\"operator\":\"?=\",\"terminal\":{\"$type\":\"Keyword\",\"value\":\"showData\"},\"cardinality\":\"?\"},{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@3\"},\"arguments\":[]},{\"$type\":\"Assignment\",\"feature\":\"sections\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@1\"},\"arguments\":[]}},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@13\"},\"arguments\":[]}],\"cardinality\":\"*\"}]},\"definesHiddenTokens\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"PieSection\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"label\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@11\"},\"arguments\":[]}},{\"$type\":\"Keyword\",\"value\":\":\"},{\"$type\":\"Assignment\",\"feature\":\"value\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@10\"},\"arguments\":[]}},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@2\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"EOL\",\"dataType\":\"string\",\"definition\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@13\"},\"arguments\":[],\"cardinality\":\"+\"},{\"$type\":\"EndOfFile\"}]},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"TitleAndAccessibilities\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"accDescr\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@5\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"accTitle\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@6\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"title\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@7\"},\"arguments\":[]}}]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@2\"},\"arguments\":[]}],\"cardinality\":\"+\"},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"TerminalRule\",\"name\":\"BOOLEAN\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"boolean\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"true\"}},{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"false\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ACC_DESCR\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*accDescr(?:[\\\\\\\\t ]*:([^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[^\\\\\\\\n\\\\\\\\r]*)|\\\\\\\\s*{([^}]*)})/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ACC_TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*accTitle[\\\\\\\\t ]*:(?:[^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[^\\\\\\\\n\\\\\\\\r]*)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*title(?:[\\\\\\\\t ][^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[\\\\\\\\t ][^\\\\\\\\n\\\\\\\\r]*|)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"FLOAT\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[0-9]+\\\\\\\\.[0-9]+(?!\\\\\\\\.)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"INT\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/0|[1-9][0-9]*(?!\\\\\\\\.)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"NUMBER\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"TerminalRuleCall\",\"rule\":{\"$ref\":\"#/rules@8\"}},{\"$type\":\"TerminalRuleCall\",\"rule\":{\"$ref\":\"#/rules@9\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"STRING\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\"([^\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\"|\\'([^\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\'/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ID\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\w]([-\\\\\\\\w]*\\\\\\\\w)?/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"NEWLINE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\\\\r?\\\\\\\\n/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"WHITESPACE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]+/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"YAML\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/---[\\\\\\\\t ]*\\\\\\\\r?\\\\\\\\n(?:[\\\\\\\\S\\\\\\\\s]*?\\\\\\\\r?\\\\\\\\n)?---(?:\\\\\\\\r?\\\\\\\\n|(?!\\\\\\\\S))/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"DIRECTIVE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*%%{[\\\\\\\\S\\\\\\\\s]*?}%%(?:\\\\\\\\r?\\\\\\\\n|(?!\\\\\\\\S))/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"SINGLE_LINE_COMMENT\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*%%[^\\\\\\\\n\\\\\\\\r]*/\"},\"fragment\":false}],\"definesHiddenTokens\":false,\"hiddenTokens\":[],\"interfaces\":[],\"types\":[],\"usedGrammars\":[]}'));\n\nlet loadedArchitectureGrammar: Grammar | undefined;\nexport const ArchitectureGrammar = (): Grammar => loadedArchitectureGrammar ?? (loadedArchitectureGrammar = loadGrammarFromJson('{\"$type\":\"Grammar\",\"isDeclared\":true,\"name\":\"Architecture\",\"imports\":[],\"rules\":[{\"$type\":\"ParserRule\",\"entry\":true,\"name\":\"Architecture\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@23\"},\"arguments\":[],\"cardinality\":\"*\"},{\"$type\":\"Keyword\",\"value\":\"architecture-beta\"},{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@23\"},\"arguments\":[]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@13\"},\"arguments\":[]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@1\"},\"arguments\":[]}],\"cardinality\":\"*\"}]},\"definesHiddenTokens\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"Statement\",\"definition\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"groups\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@5\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"services\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@6\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"junctions\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@7\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"edges\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@8\"},\"arguments\":[]}}]},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"LeftPort\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\":\"},{\"$type\":\"Assignment\",\"feature\":\"lhsDir\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@9\"},\"arguments\":[]}}]},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"RightPort\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"rhsDir\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@9\"},\"arguments\":[]}},{\"$type\":\"Keyword\",\"value\":\":\"}]},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"Arrow\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@2\"},\"arguments\":[]},{\"$type\":\"Assignment\",\"feature\":\"lhsInto\",\"operator\":\"?=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@11\"},\"arguments\":[]},\"cardinality\":\"?\"},{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"--\"},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"-\"},{\"$type\":\"Assignment\",\"feature\":\"title\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@29\"},\"arguments\":[]}},{\"$type\":\"Keyword\",\"value\":\"-\"}]}]},{\"$type\":\"Assignment\",\"feature\":\"rhsInto\",\"operator\":\"?=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@11\"},\"arguments\":[]},\"cardinality\":\"?\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@3\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Group\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"group\"},{\"$type\":\"Assignment\",\"feature\":\"id\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@22\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"icon\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@28\"},\"arguments\":[]},\"cardinality\":\"?\"},{\"$type\":\"Assignment\",\"feature\":\"title\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@29\"},\"arguments\":[]},\"cardinality\":\"?\"},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"in\"},{\"$type\":\"Assignment\",\"feature\":\"in\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@22\"},\"arguments\":[]}}],\"cardinality\":\"?\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@12\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Service\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"service\"},{\"$type\":\"Assignment\",\"feature\":\"id\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@22\"},\"arguments\":[]}},{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"iconText\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@21\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"icon\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@28\"},\"arguments\":[]}}],\"cardinality\":\"?\"},{\"$type\":\"Assignment\",\"feature\":\"title\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@29\"},\"arguments\":[]},\"cardinality\":\"?\"},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"in\"},{\"$type\":\"Assignment\",\"feature\":\"in\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@22\"},\"arguments\":[]}}],\"cardinality\":\"?\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@12\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Junction\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"junction\"},{\"$type\":\"Assignment\",\"feature\":\"id\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@22\"},\"arguments\":[]}},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"in\"},{\"$type\":\"Assignment\",\"feature\":\"in\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@22\"},\"arguments\":[]}}],\"cardinality\":\"?\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@12\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Edge\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"lhsId\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@22\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"lhsGroup\",\"operator\":\"?=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@10\"},\"arguments\":[]},\"cardinality\":\"?\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@4\"},\"arguments\":[]},{\"$type\":\"Assignment\",\"feature\":\"rhsId\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@22\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"rhsGroup\",\"operator\":\"?=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@10\"},\"arguments\":[]},\"cardinality\":\"?\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@12\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"TerminalRule\",\"name\":\"ARROW_DIRECTION\",\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"L\"}},{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"R\"}}]},{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"T\"}}]},{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"B\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ARROW_GROUP\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\\\\{group\\\\\\\\}/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ARROW_INTO\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/<|>/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"EOL\",\"dataType\":\"string\",\"definition\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@23\"},\"arguments\":[],\"cardinality\":\"+\"},{\"$type\":\"EndOfFile\"}]},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"TitleAndAccessibilities\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"accDescr\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@15\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"accTitle\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@16\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"title\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@12\"},\"arguments\":[]}],\"cardinality\":\"+\"},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"TerminalRule\",\"name\":\"BOOLEAN\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"boolean\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"true\"}},{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"false\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ACC_DESCR\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*accDescr(?:[\\\\\\\\t ]*:([^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[^\\\\\\\\n\\\\\\\\r]*)|\\\\\\\\s*{([^}]*)})/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ACC_TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*accTitle[\\\\\\\\t ]*:(?:[^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[^\\\\\\\\n\\\\\\\\r]*)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*title(?:[\\\\\\\\t ][^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[\\\\\\\\t ][^\\\\\\\\n\\\\\\\\r]*|)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"FLOAT\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[0-9]+\\\\\\\\.[0-9]+(?!\\\\\\\\.)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"INT\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/0|[1-9][0-9]*(?!\\\\\\\\.)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"NUMBER\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"TerminalRuleCall\",\"rule\":{\"$ref\":\"#/rules@18\"}},{\"$type\":\"TerminalRuleCall\",\"rule\":{\"$ref\":\"#/rules@19\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"STRING\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\"([^\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\"|\\'([^\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\'/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ID\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\w]([-\\\\\\\\w]*\\\\\\\\w)?/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"NEWLINE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\\\\r?\\\\\\\\n/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"WHITESPACE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]+/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"YAML\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/---[\\\\\\\\t ]*\\\\\\\\r?\\\\\\\\n(?:[\\\\\\\\S\\\\\\\\s]*?\\\\\\\\r?\\\\\\\\n)?---(?:\\\\\\\\r?\\\\\\\\n|(?!\\\\\\\\S))/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"DIRECTIVE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*%%{[\\\\\\\\S\\\\\\\\s]*?}%%(?:\\\\\\\\r?\\\\\\\\n|(?!\\\\\\\\S))/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"SINGLE_LINE_COMMENT\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*%%[^\\\\\\\\n\\\\\\\\r]*/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"name\":\"ARCH_ICON\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\\\\([\\\\\\\\w-:]+\\\\\\\\)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ARCH_TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\\\\[[\\\\\\\\w ]+\\\\\\\\]/\"},\"fragment\":false,\"hidden\":false}],\"definesHiddenTokens\":false,\"hiddenTokens\":[],\"interfaces\":[],\"types\":[],\"usedGrammars\":[]}'));\n\nlet loadedGitGraphGrammar: Grammar | undefined;\nexport const GitGraphGrammar = (): Grammar => loadedGitGraphGrammar ?? (loadedGitGraphGrammar = loadGrammarFromJson('{\"$type\":\"Grammar\",\"isDeclared\":true,\"name\":\"GitGraph\",\"imports\":[],\"rules\":[{\"$type\":\"ParserRule\",\"entry\":true,\"name\":\"GitGraph\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@19\"},\"arguments\":[],\"cardinality\":\"*\"},{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"gitGraph\"},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"gitGraph\"},{\"$type\":\"Keyword\",\"value\":\":\"}]},{\"$type\":\"Keyword\",\"value\":\"gitGraph:\"},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"gitGraph\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@2\"},\"arguments\":[]},{\"$type\":\"Keyword\",\"value\":\":\"}]}]},{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@19\"},\"arguments\":[]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@9\"},\"arguments\":[]},{\"$type\":\"Assignment\",\"feature\":\"statements\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@1\"},\"arguments\":[]}}],\"cardinality\":\"*\"}]},\"definesHiddenTokens\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Statement\",\"definition\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@3\"},\"arguments\":[]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@4\"},\"arguments\":[]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@5\"},\"arguments\":[]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@6\"},\"arguments\":[]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@7\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Direction\",\"definition\":{\"$type\":\"Assignment\",\"feature\":\"dir\",\"operator\":\"=\",\"terminal\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"LR\"},{\"$type\":\"Keyword\",\"value\":\"TB\"},{\"$type\":\"Keyword\",\"value\":\"BT\"}]}},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Commit\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"commit\"},{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"id:\"},{\"$type\":\"Assignment\",\"feature\":\"id\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"msg:\",\"cardinality\":\"?\"},{\"$type\":\"Assignment\",\"feature\":\"message\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"tag:\"},{\"$type\":\"Assignment\",\"feature\":\"tags\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"type:\"},{\"$type\":\"Assignment\",\"feature\":\"type\",\"operator\":\"=\",\"terminal\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"NORMAL\"},{\"$type\":\"Keyword\",\"value\":\"REVERSE\"},{\"$type\":\"Keyword\",\"value\":\"HIGHLIGHT\"}]}}]}],\"cardinality\":\"*\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@8\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Branch\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"branch\"},{\"$type\":\"Assignment\",\"feature\":\"name\",\"operator\":\"=\",\"terminal\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@24\"},\"arguments\":[]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}]}},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"order:\"},{\"$type\":\"Assignment\",\"feature\":\"order\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@15\"},\"arguments\":[]}}],\"cardinality\":\"?\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@8\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Merge\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"merge\"},{\"$type\":\"Assignment\",\"feature\":\"branch\",\"operator\":\"=\",\"terminal\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@24\"},\"arguments\":[]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}]}},{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"id:\"},{\"$type\":\"Assignment\",\"feature\":\"id\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"tag:\"},{\"$type\":\"Assignment\",\"feature\":\"tags\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"type:\"},{\"$type\":\"Assignment\",\"feature\":\"type\",\"operator\":\"=\",\"terminal\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"NORMAL\"},{\"$type\":\"Keyword\",\"value\":\"REVERSE\"},{\"$type\":\"Keyword\",\"value\":\"HIGHLIGHT\"}]}}]}],\"cardinality\":\"*\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@8\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Checkout\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"checkout\"},{\"$type\":\"Keyword\",\"value\":\"switch\"}]},{\"$type\":\"Assignment\",\"feature\":\"branch\",\"operator\":\"=\",\"terminal\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@24\"},\"arguments\":[]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}]}},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@8\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"CherryPicking\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"cherry-pick\"},{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"id:\"},{\"$type\":\"Assignment\",\"feature\":\"id\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"tag:\"},{\"$type\":\"Assignment\",\"feature\":\"tags\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"parent:\"},{\"$type\":\"Assignment\",\"feature\":\"parent\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]}],\"cardinality\":\"*\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@8\"},\"arguments\":[]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"EOL\",\"dataType\":\"string\",\"definition\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@19\"},\"arguments\":[],\"cardinality\":\"+\"},{\"$type\":\"EndOfFile\"}]},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"TitleAndAccessibilities\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"accDescr\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@11\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"accTitle\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@12\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"title\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@13\"},\"arguments\":[]}}]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@8\"},\"arguments\":[]}],\"cardinality\":\"+\"},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"TerminalRule\",\"name\":\"BOOLEAN\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"boolean\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"true\"}},{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"false\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ACC_DESCR\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*accDescr(?:[\\\\\\\\t ]*:([^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[^\\\\\\\\n\\\\\\\\r]*)|\\\\\\\\s*{([^}]*)})/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ACC_TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*accTitle[\\\\\\\\t ]*:(?:[^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[^\\\\\\\\n\\\\\\\\r]*)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*title(?:[\\\\\\\\t ][^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[\\\\\\\\t ][^\\\\\\\\n\\\\\\\\r]*|)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"FLOAT\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[0-9]+\\\\\\\\.[0-9]+(?!\\\\\\\\.)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"INT\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/0|[1-9][0-9]*(?!\\\\\\\\.)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"NUMBER\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"TerminalRuleCall\",\"rule\":{\"$ref\":\"#/rules@14\"}},{\"$type\":\"TerminalRuleCall\",\"rule\":{\"$ref\":\"#/rules@15\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"STRING\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\"([^\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\"|\\'([^\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\'/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ID\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\w]([-\\\\\\\\w]*\\\\\\\\w)?/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"NEWLINE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\\\\r?\\\\\\\\n/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"WHITESPACE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]+/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"YAML\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/---[\\\\\\\\t ]*\\\\\\\\r?\\\\\\\\n(?:[\\\\\\\\S\\\\\\\\s]*?\\\\\\\\r?\\\\\\\\n)?---(?:\\\\\\\\r?\\\\\\\\n|(?!\\\\\\\\S))/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"DIRECTIVE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*%%{[\\\\\\\\S\\\\\\\\s]*?}%%(?:\\\\\\\\r?\\\\\\\\n|(?!\\\\\\\\S))/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"SINGLE_LINE_COMMENT\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*%%[^\\\\\\\\n\\\\\\\\r]*/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"name\":\"REFERENCE\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\\\\w([-\\\\\\\\./\\\\\\\\w]*[-\\\\\\\\w])?/\"},\"fragment\":false,\"hidden\":false}],\"definesHiddenTokens\":false,\"hiddenTokens\":[],\"interfaces\":[],\"types\":[],\"usedGrammars\":[]}'));\n\nlet loadedRadarGrammar: Grammar | undefined;\nexport const RadarGrammar = (): Grammar => loadedRadarGrammar ?? (loadedRadarGrammar = loadGrammarFromJson('{\"$type\":\"Grammar\",\"isDeclared\":true,\"name\":\"Radar\",\"imports\":[],\"rules\":[{\"$type\":\"ParserRule\",\"entry\":true,\"name\":\"Radar\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@20\"},\"arguments\":[],\"cardinality\":\"*\"},{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"radar-beta\"},{\"$type\":\"Keyword\",\"value\":\"radar-beta:\"},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"radar-beta\"},{\"$type\":\"Keyword\",\"value\":\":\"}]}]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@20\"},\"arguments\":[],\"cardinality\":\"*\"},{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@10\"},\"arguments\":[]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"axis\"},{\"$type\":\"Assignment\",\"feature\":\"axes\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@2\"},\"arguments\":[]}},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\",\"},{\"$type\":\"Assignment\",\"feature\":\"axes\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@2\"},\"arguments\":[]}}],\"cardinality\":\"*\"}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"curve\"},{\"$type\":\"Assignment\",\"feature\":\"curves\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@3\"},\"arguments\":[]}},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\",\"},{\"$type\":\"Assignment\",\"feature\":\"curves\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@3\"},\"arguments\":[]}}],\"cardinality\":\"*\"}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"options\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@7\"},\"arguments\":[]}},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\",\"},{\"$type\":\"Assignment\",\"feature\":\"options\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@7\"},\"arguments\":[]}}],\"cardinality\":\"*\"}]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@20\"},\"arguments\":[]}],\"cardinality\":\"*\"}]},\"definesHiddenTokens\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"Label\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\"[\"},{\"$type\":\"Assignment\",\"feature\":\"label\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@18\"},\"arguments\":[]}},{\"$type\":\"Keyword\",\"value\":\"]\"}]},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Axis\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"name\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@19\"},\"arguments\":[]}},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@1\"},\"arguments\":[],\"cardinality\":\"?\"}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Curve\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"name\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@19\"},\"arguments\":[]}},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@1\"},\"arguments\":[],\"cardinality\":\"?\"},{\"$type\":\"Keyword\",\"value\":\"{\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@4\"},\"arguments\":[]},{\"$type\":\"Keyword\",\"value\":\"}\"}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"Entries\",\"definition\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Group\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@20\"},\"arguments\":[],\"cardinality\":\"*\"},{\"$type\":\"Assignment\",\"feature\":\"entries\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@6\"},\"arguments\":[]}},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\",\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@20\"},\"arguments\":[],\"cardinality\":\"*\"},{\"$type\":\"Assignment\",\"feature\":\"entries\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@6\"},\"arguments\":[]}}],\"cardinality\":\"*\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@20\"},\"arguments\":[],\"cardinality\":\"*\"}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@20\"},\"arguments\":[],\"cardinality\":\"*\"},{\"$type\":\"Assignment\",\"feature\":\"entries\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@5\"},\"arguments\":[]}},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Keyword\",\"value\":\",\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@20\"},\"arguments\":[],\"cardinality\":\"*\"},{\"$type\":\"Assignment\",\"feature\":\"entries\",\"operator\":\"+=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@5\"},\"arguments\":[]}}],\"cardinality\":\"*\"},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@20\"},\"arguments\":[],\"cardinality\":\"*\"}]}]},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"DetailedEntry\",\"returnType\":{\"$ref\":\"#/interfaces@0\"},\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"axis\",\"operator\":\"=\",\"terminal\":{\"$type\":\"CrossReference\",\"type\":{\"$ref\":\"#/rules@2\"},\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@19\"},\"arguments\":[]},\"deprecatedSyntax\":false}},{\"$type\":\"Keyword\",\"value\":\":\",\"cardinality\":\"?\"},{\"$type\":\"Assignment\",\"feature\":\"value\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"NumberEntry\",\"returnType\":{\"$ref\":\"#/interfaces@0\"},\"definition\":{\"$type\":\"Assignment\",\"feature\":\"value\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"name\":\"Option\",\"definition\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"name\",\"operator\":\"=\",\"terminal\":{\"$type\":\"Keyword\",\"value\":\"showLegend\"}},{\"$type\":\"Assignment\",\"feature\":\"value\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@11\"},\"arguments\":[]}}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"name\",\"operator\":\"=\",\"terminal\":{\"$type\":\"Keyword\",\"value\":\"ticks\"}},{\"$type\":\"Assignment\",\"feature\":\"value\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"name\",\"operator\":\"=\",\"terminal\":{\"$type\":\"Keyword\",\"value\":\"max\"}},{\"$type\":\"Assignment\",\"feature\":\"value\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"name\",\"operator\":\"=\",\"terminal\":{\"$type\":\"Keyword\",\"value\":\"min\"}},{\"$type\":\"Assignment\",\"feature\":\"value\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@17\"},\"arguments\":[]}}]},{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"name\",\"operator\":\"=\",\"terminal\":{\"$type\":\"Keyword\",\"value\":\"graticule\"}},{\"$type\":\"Assignment\",\"feature\":\"value\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@8\"},\"arguments\":[]}}]}]},\"definesHiddenTokens\":false,\"entry\":false,\"fragment\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"TerminalRule\",\"name\":\"GRATICULE\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"circle\"}},{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"polygon\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"EOL\",\"dataType\":\"string\",\"definition\":{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@20\"},\"arguments\":[],\"cardinality\":\"+\"},{\"$type\":\"EndOfFile\"}]},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"ParserRule\",\"fragment\":true,\"name\":\"TitleAndAccessibilities\",\"definition\":{\"$type\":\"Group\",\"elements\":[{\"$type\":\"Alternatives\",\"elements\":[{\"$type\":\"Assignment\",\"feature\":\"accDescr\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@12\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"accTitle\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@13\"},\"arguments\":[]}},{\"$type\":\"Assignment\",\"feature\":\"title\",\"operator\":\"=\",\"terminal\":{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@14\"},\"arguments\":[]}}]},{\"$type\":\"RuleCall\",\"rule\":{\"$ref\":\"#/rules@9\"},\"arguments\":[]}],\"cardinality\":\"+\"},\"definesHiddenTokens\":false,\"entry\":false,\"hiddenTokens\":[],\"parameters\":[],\"wildcard\":false},{\"$type\":\"TerminalRule\",\"name\":\"BOOLEAN\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"boolean\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"true\"}},{\"$type\":\"CharacterRange\",\"left\":{\"$type\":\"Keyword\",\"value\":\"false\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ACC_DESCR\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*accDescr(?:[\\\\\\\\t ]*:([^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[^\\\\\\\\n\\\\\\\\r]*)|\\\\\\\\s*{([^}]*)})/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ACC_TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*accTitle[\\\\\\\\t ]*:(?:[^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[^\\\\\\\\n\\\\\\\\r]*)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"TITLE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*title(?:[\\\\\\\\t ][^\\\\\\\\n\\\\\\\\r]*?(?=%%)|[\\\\\\\\t ][^\\\\\\\\n\\\\\\\\r]*|)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"FLOAT\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[0-9]+\\\\\\\\.[0-9]+(?!\\\\\\\\.)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"INT\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/0|[1-9][0-9]*(?!\\\\\\\\.)/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"NUMBER\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"number\"},\"definition\":{\"$type\":\"TerminalAlternatives\",\"elements\":[{\"$type\":\"TerminalRuleCall\",\"rule\":{\"$ref\":\"#/rules@15\"}},{\"$type\":\"TerminalRuleCall\",\"rule\":{\"$ref\":\"#/rules@16\"}}]},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"STRING\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\"([^\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\"|\\'([^\\'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\'/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"ID\",\"type\":{\"$type\":\"ReturnType\",\"name\":\"string\"},\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\w]([-\\\\\\\\w]*\\\\\\\\w)?/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"name\":\"NEWLINE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/\\\\\\\\r?\\\\\\\\n/\"},\"fragment\":false,\"hidden\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"WHITESPACE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]+/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"YAML\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/---[\\\\\\\\t ]*\\\\\\\\r?\\\\\\\\n(?:[\\\\\\\\S\\\\\\\\s]*?\\\\\\\\r?\\\\\\\\n)?---(?:\\\\\\\\r?\\\\\\\\n|(?!\\\\\\\\S))/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"DIRECTIVE\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*%%{[\\\\\\\\S\\\\\\\\s]*?}%%(?:\\\\\\\\r?\\\\\\\\n|(?!\\\\\\\\S))/\"},\"fragment\":false},{\"$type\":\"TerminalRule\",\"hidden\":true,\"name\":\"SINGLE_LINE_COMMENT\",\"definition\":{\"$type\":\"RegexToken\",\"regex\":\"/[\\\\\\\\t ]*%%[^\\\\\\\\n\\\\\\\\r]*/\"},\"fragment\":false}],\"interfaces\":[{\"$type\":\"Interface\",\"name\":\"Entry\",\"attributes\":[{\"$type\":\"TypeAttribute\",\"name\":\"axis\",\"isOptional\":true,\"type\":{\"$type\":\"ReferenceType\",\"referenceType\":{\"$type\":\"SimpleType\",\"typeRef\":{\"$ref\":\"#/rules@2\"}}}},{\"$type\":\"TypeAttribute\",\"name\":\"value\",\"type\":{\"$type\":\"SimpleType\",\"primitiveType\":\"number\"},\"isOptional\":false}],\"superTypes\":[]}],\"definesHiddenTokens\":false,\"hiddenTokens\":[],\"types\":[],\"usedGrammars\":[]}'));\n", "/******************************************************************************\n * This file was generated by langium-cli 3.3.0.\n * DO NOT EDIT MANUALLY!\n ******************************************************************************/\n\nimport type { LangiumSharedCoreServices, LangiumCoreServices, LangiumGeneratedCoreServices, LangiumGeneratedSharedCoreServices, LanguageMetaData, Module } from 'langium';\nimport { MermaidAstReflection } from './ast.js';\nimport { InfoGrammar, PacketGrammar, PieGrammar, ArchitectureGrammar, GitGraphGrammar, RadarGrammar } from './grammar.js';\n\nexport const InfoLanguageMetaData = {\n    languageId: 'info',\n    fileExtensions: ['.mmd', '.mermaid'],\n    caseInsensitive: false,\n    mode: 'production'\n} as const satisfies LanguageMetaData;\n\nexport const PacketLanguageMetaData = {\n    languageId: 'packet',\n    fileExtensions: ['.mmd', '.mermaid'],\n    caseInsensitive: false,\n    mode: 'production'\n} as const satisfies LanguageMetaData;\n\nexport const PieLanguageMetaData = {\n    languageId: 'pie',\n    fileExtensions: ['.mmd', '.mermaid'],\n    caseInsensitive: false,\n    mode: 'production'\n} as const satisfies LanguageMetaData;\n\nexport const ArchitectureLanguageMetaData = {\n    languageId: 'architecture',\n    fileExtensions: ['.mmd', '.mermaid'],\n    caseInsensitive: false,\n    mode: 'production'\n} as const satisfies LanguageMetaData;\n\nexport const GitGraphLanguageMetaData = {\n    languageId: 'gitGraph',\n    fileExtensions: ['.mmd', '.mermaid'],\n    caseInsensitive: false,\n    mode: 'production'\n} as const satisfies LanguageMetaData;\n\nexport const RadarLanguageMetaData = {\n    languageId: 'radar',\n    fileExtensions: ['.mmd', '.mermaid'],\n    caseInsensitive: false,\n    mode: 'production'\n} as const satisfies LanguageMetaData;\n\nexport const MermaidGeneratedSharedModule: Module<LangiumSharedCoreServices, LangiumGeneratedSharedCoreServices> = {\n    AstReflection: () => new MermaidAstReflection()\n};\n\nexport const InfoGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices> = {\n    Grammar: () => InfoGrammar(),\n    LanguageMetaData: () => InfoLanguageMetaData,\n    parser: {}\n};\n\nexport const PacketGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices> = {\n    Grammar: () => PacketGrammar(),\n    LanguageMetaData: () => PacketLanguageMetaData,\n    parser: {}\n};\n\nexport const PieGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices> = {\n    Grammar: () => PieGrammar(),\n    LanguageMetaData: () => PieLanguageMetaData,\n    parser: {}\n};\n\nexport const ArchitectureGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices> = {\n    Grammar: () => ArchitectureGrammar(),\n    LanguageMetaData: () => ArchitectureLanguageMetaData,\n    parser: {}\n};\n\nexport const GitGraphGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices> = {\n    Grammar: () => GitGraphGrammar(),\n    LanguageMetaData: () => GitGraphLanguageMetaData,\n    parser: {}\n};\n\nexport const RadarGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices> = {\n    Grammar: () => RadarGrammar(),\n    LanguageMetaData: () => RadarLanguageMetaData,\n    parser: {}\n};\n", "import type { CstNode, GrammarAST, ValueType } from 'langium';\nimport { DefaultValueConverter } from 'langium';\n\nimport { accessibilityDescrRegex, accessibilityTitleRegex, titleRegex } from './matcher.js';\n\nconst rulesRegexes: Record<string, RegExp> = {\n  ACC_DESCR: accessibilityDescrRegex,\n  ACC_TITLE: accessibilityTitleRegex,\n  TITLE: titleRegex,\n};\n\nexport abstract class AbstractMermaidValueConverter extends DefaultValueConverter {\n  /**\n   * A method contains convert logic to be used by class.\n   *\n   * @param rule - Parsed rule.\n   * @param input - Matched string.\n   * @param cstNode - Node in the Concrete Syntax Tree (CST).\n   * @returns converted the value if it's available or `undefined` if it's not.\n   */\n  protected abstract runCustomConverter(\n    rule: GrammarAST.AbstractRule,\n    input: string,\n    cstNode: CstNode\n  ): ValueType | undefined;\n\n  protected override runConverter(\n    rule: GrammarAST.AbstractRule,\n    input: string,\n    cstNode: CstNode\n  ): ValueType {\n    let value: ValueType | undefined = this.runCommonConverter(rule, input, cstNode);\n\n    if (value === undefined) {\n      value = this.runCustomConverter(rule, input, cstNode);\n    }\n    if (value === undefined) {\n      return super.runConverter(rule, input, cstNode);\n    }\n\n    return value;\n  }\n\n  private runCommonConverter(\n    rule: GrammarAST.AbstractRule,\n    input: string,\n    _cstNode: CstNode\n  ): ValueType | undefined {\n    const regex: RegExp | undefined = rulesRegexes[rule.name];\n    if (regex === undefined) {\n      return undefined;\n    }\n    const match = regex.exec(input);\n    if (match === null) {\n      return undefined;\n    }\n    // single line title, accTitle, accDescr\n    if (match[1] !== undefined) {\n      return match[1].trim().replace(/[\\t ]{2,}/gm, ' ');\n    }\n    // multi line accDescr\n    if (match[2] !== undefined) {\n      return match[2]\n        .replace(/^\\s*/gm, '')\n        .replace(/\\s+$/gm, '')\n        .replace(/[\\t ]{2,}/gm, ' ')\n        .replace(/[\\n\\r]{2,}/gm, '\\n');\n    }\n    return undefined;\n  }\n}\n\nexport class CommonValueConverter extends AbstractMermaidValueConverter {\n  protected override runCustomConverter(\n    _rule: GrammarAST.AbstractRule,\n    _input: string,\n    _cstNode: CstNode\n  ): ValueType | undefined {\n    return undefined;\n  }\n}\n", "/**\n * Matches single and multi line accessible description\n */\nexport const accessibilityDescrRegex = /accDescr(?:[\\t ]*:([^\\n\\r]*)|\\s*{([^}]*)})/;\n\n/**\n * Matches single line accessible title\n */\nexport const accessibilityTitleRegex = /accTitle[\\t ]*:([^\\n\\r]*)/;\n\n/**\n * Matches a single line title\n */\nexport const titleRegex = /title([\\t ][^\\n\\r]*|)/;\n", "import type { GrammarAST, Stream, TokenBuilderOptions } from 'langium';\nimport type { TokenType } from 'chevrotain';\n\nimport { DefaultTokenBuilder } from 'langium';\n\nexport abstract class AbstractMermaidTokenBuilder extends DefaultTokenBuilder {\n  private keywords: Set<string>;\n\n  public constructor(keywords: string[]) {\n    super();\n    this.keywords = new Set<string>(keywords);\n  }\n\n  protected override buildKeywordTokens(\n    rules: Stream<GrammarAST.AbstractRule>,\n    terminalTokens: TokenType[],\n    options?: TokenBuilderOptions\n  ): TokenType[] {\n    const tokenTypes: TokenType[] = super.buildKeywordTokens(rules, terminalTokens, options);\n    // to restrict users, they mustn't have any non-whitespace characters after the keyword.\n    tokenTypes.forEach((tokenType: TokenType): void => {\n      if (this.keywords.has(tokenType.name) && tokenType.PATTERN !== undefined) {\n        // eslint-disable-next-line @typescript-eslint/no-base-to-string\n        tokenType.PATTERN = new RegExp(tokenType.PATTERN.toString() + '(?:(?=%%)|(?!\\\\S))');\n      }\n    });\n    return tokenTypes;\n  }\n}\n\nexport class CommonTokenBuilder extends AbstractMermaidTokenBuilder {}\n"], "mappings": ";;;;AAOA,SAAS,6BAA6B;AAiF/B,IAAM,YAAY;AAiBlB,IAAM,eAAe;AAErB,SAAS,eAAe,MAAqC;AAChE,SAAO,WAAW,WAAW,MAAM,YAAY;AACnD;AAFgB;AAWT,IAAM,OAAO;AAab,IAAM,SAAS;AAEf,SAAS,SAAS,MAA+B;AACpD,SAAO,WAAW,WAAW,MAAM,MAAM;AAC7C;AAFgB;AAUT,IAAM,WAAW;AAcjB,IAAM,gBAAgB;AAetB,IAAM,SAAS;AAEf,SAAS,SAAS,MAA+B;AACpD,SAAO,WAAW,WAAW,MAAM,MAAM;AAC7C;AAFgB;AAYT,IAAM,QAAQ;AAoBd,IAAM,OAAO;AAab,IAAM,QAAQ;AAcd,IAAM,WAAW;AAEjB,SAAS,WAAW,MAAiC;AACxD,SAAO,WAAW,WAAW,MAAM,QAAQ;AAC/C;AAFgB;AAaT,IAAM,QAAQ;AAad,IAAM,OAAO;AAEb,SAAS,OAAO,MAA6B;AAChD,SAAO,WAAW,WAAW,MAAM,IAAI;AAC3C;AAFgB;AAWT,IAAM,WAAW;AAejB,IAAM,QAAQ;AAEd,SAAS,QAAQ,MAA8B;AAClD,SAAO,WAAW,WAAW,MAAM,KAAK;AAC5C;AAFgB;AAWT,IAAM,SAAS;AAcf,IAAM,SAAS;AAEf,SAAS,SAAS,MAA+B;AACpD,SAAO,WAAW,WAAW,MAAM,MAAM;AAC7C;AAFgB;AAaT,IAAM,cAAc;AAEpB,SAAS,cAAc,MAAoC;AAC9D,SAAO,WAAW,WAAW,MAAM,WAAW;AAClD;AAFgB;AAaT,IAAM,MAAM;AAEZ,SAAS,MAAM,MAA4B;AAC9C,SAAO,WAAW,WAAW,MAAM,GAAG;AAC1C;AAFgB;AAWT,IAAM,aAAa;AAEnB,SAAS,aAAa,MAAmC;AAC5D,SAAO,WAAW,WAAW,MAAM,UAAU;AACjD;AAFgB;AAcT,IAAM,QAAQ;AAgBd,IAAM,UAAU;AAWhB,IAAM,YAAY;AAgClB,IAAM,uBAAN,cAAmC,sBAAsB;AAAA,EAlbhE,OAkbgE;AAAA;AAAA;AAAA,EAE5D,cAAwB;AACpB,WAAO,CAAC,cAAc,MAAM,QAAQ,UAAU,eAAe,QAAQ,OAAO,WAAW,MAAM,OAAO,UAAU,OAAO,MAAM,UAAU,OAAO,QAAQ,QAAQ,aAAa,KAAK,YAAY,OAAO,SAAS,SAAS;AAAA,EACvN;AAAA,EAEmB,iBAAiB,SAAiB,WAA4B;AAC7E,YAAQ,SAAS;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,OAAO;AACR,eAAO,KAAK,UAAU,WAAW,SAAS;AAAA,MAC9C;AAAA,MACA,KAAK,WAAW;AACZ,eAAO,KAAK,UAAU,UAAU,SAAS;AAAA,MAC7C;AAAA,MACA,SAAS;AACL,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,iBAAiB,SAAgC;AAC7C,UAAM,cAAc,GAAG,QAAQ,UAAU,KAAK,IAAI,QAAQ,QAAQ;AAClE,YAAQ,aAAa;AAAA,MACjB,KAAK,cAAc;AACf,eAAO;AAAA,MACX;AAAA,MACA,SAAS;AACL,cAAM,IAAI,MAAM,GAAG,WAAW,+BAA+B;AAAA,MACjE;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,gBAAgB,MAA4B;AACxC,YAAQ,MAAM;AAAA,MACV,KAAK,cAAc;AACf,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,SAAS,cAAc,CAAC,EAAE;AAAA,YAClC,EAAE,MAAM,UAAU,cAAc,CAAC,EAAE;AAAA,YACnC,EAAE,MAAM,aAAa,cAAc,CAAC,EAAE;AAAA,YACtC,EAAE,MAAM,YAAY,cAAc,CAAC,EAAE;AAAA,YACrC,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,MAAM;AACP,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,QAAQ;AAAA,YAChB,EAAE,MAAM,OAAO;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,QAAQ;AACT,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,OAAO;AAAA,YACf,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,UAAU;AACX,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,SAAS;AAAA,UACrB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,eAAe;AAChB,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,KAAK;AAAA,YACb,EAAE,MAAM,SAAS;AAAA,YACjB,EAAE,MAAM,QAAQ,cAAc,CAAC,EAAE;AAAA,UACrC;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,QAAQ;AACT,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,KAAK;AAAA,YACb,EAAE,MAAM,UAAU;AAAA,YAClB,EAAE,MAAM,QAAQ,cAAc,CAAC,EAAE;AAAA,YACjC,EAAE,MAAM,OAAO;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,OAAO;AACR,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,WAAW,cAAc,CAAC,EAAE;AAAA,YACpC,EAAE,MAAM,QAAQ;AAAA,YAChB,EAAE,MAAM,OAAO;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,MAAM;AACP,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,SAAS;AAAA,YACjB,EAAE,MAAM,YAAY,cAAc,MAAM;AAAA,YACxC,EAAE,MAAM,QAAQ;AAAA,YAChB,EAAE,MAAM,WAAW,cAAc,MAAM;AAAA,YACvC,EAAE,MAAM,SAAS;AAAA,YACjB,EAAE,MAAM,YAAY,cAAc,MAAM;AAAA,YACxC,EAAE,MAAM,QAAQ;AAAA,YAChB,EAAE,MAAM,WAAW,cAAc,MAAM;AAAA,YACvC,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,OAAO;AACR,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,OAAO;AAAA,YACf,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,UAAU;AACX,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,cAAc,cAAc,CAAC,EAAE;AAAA,YACvC,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,OAAO;AACR,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,OAAO;AAAA,YACf,EAAE,MAAM,KAAK;AAAA,YACb,EAAE,MAAM,KAAK;AAAA,YACb,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,MAAM;AACP,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,UAAU;AACX,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,KAAK;AAAA,YACb,EAAE,MAAM,KAAK;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,OAAO;AACR,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,SAAS;AAAA,YACjB,EAAE,MAAM,KAAK;AAAA,YACb,EAAE,MAAM,QAAQ,cAAc,CAAC,EAAE;AAAA,YACjC,EAAE,MAAM,OAAO;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,QAAQ;AACT,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,OAAO;AAAA,YACf,EAAE,MAAM,SAAS,cAAc,MAAM;AAAA,UACzC;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,QAAQ;AACT,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,UAAU,cAAc,CAAC,EAAE;AAAA,YACnC,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,aAAa;AACd,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,OAAO;AAAA,YACf,EAAE,MAAM,MAAM;AAAA,YACd,EAAE,MAAM,QAAQ;AAAA,YAChB,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,KAAK;AACN,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,YAAY,cAAc,CAAC,EAAE;AAAA,YACrC,EAAE,MAAM,YAAY,cAAc,MAAM;AAAA,YACxC,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,YAAY;AACb,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,QAAQ;AAAA,YAChB,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,OAAO;AACR,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,QAAQ,cAAc,CAAC,EAAE;AAAA,YACjC,EAAE,MAAM,UAAU,cAAc,CAAC,EAAE;AAAA,YACnC,EAAE,MAAM,WAAW,cAAc,CAAC,EAAE;AAAA,YACpC,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,SAAS;AACV,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,OAAO;AAAA,YACf,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,KAAK;AAAA,YACb,EAAE,MAAM,KAAK;AAAA,YACb,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,KAAK,WAAW;AACZ,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY;AAAA,YACR,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,WAAW;AAAA,YACnB,EAAE,MAAM,MAAM;AAAA,YACd,EAAE,MAAM,cAAc,cAAc,CAAC,EAAE;AAAA,YACvC,EAAE,MAAM,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,SAAS;AACL,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY,CAAC;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAEO,IAAM,aAAa,IAAI,qBAAqB;;;ACzsBnD,SAAS,2BAA2B;AAEpC,IAAI;AACG,IAAM,cAAc,6BAAe,sBAAsB,oBAAoB,oBAAoB,2jJAA8jJ,IAA3oJ;AAE3B,IAAI;AACG,IAAM,gBAAgB,6BAAe,wBAAwB,sBAAsB,oBAAoB,yjLAA4jL,IAA7oL;AAE7B,IAAI;AACG,IAAM,aAAa,6BAAe,qBAAqB,mBAAmB,oBAAoB,qtKAAwtK,IAAnyK;AAE1B,IAAI;AACG,IAAM,sBAAsB,6BAAe,8BAA8B,4BAA4B,oBAAoB,w2WAA22W,IAAx8W;AAEnC,IAAI;AACG,IAAM,kBAAkB,6BAAe,0BAA0B,wBAAwB,oBAAoB,+qVAAkrV,IAAvwV;AAE/B,IAAI;AACG,IAAM,eAAe,6BAAe,uBAAuB,qBAAqB,oBAAoB,urXAA0rX,IAAzwX;;;ACfrB,IAAM,uBAAuB;AAAA,EAChC,YAAY;AAAA,EACZ,gBAAgB,CAAC,QAAQ,UAAU;AAAA,EACnC,iBAAiB;AAAA,EACjB,MAAM;AACV;AAEO,IAAM,yBAAyB;AAAA,EAClC,YAAY;AAAA,EACZ,gBAAgB,CAAC,QAAQ,UAAU;AAAA,EACnC,iBAAiB;AAAA,EACjB,MAAM;AACV;AAEO,IAAM,sBAAsB;AAAA,EAC/B,YAAY;AAAA,EACZ,gBAAgB,CAAC,QAAQ,UAAU;AAAA,EACnC,iBAAiB;AAAA,EACjB,MAAM;AACV;AAEO,IAAM,+BAA+B;AAAA,EACxC,YAAY;AAAA,EACZ,gBAAgB,CAAC,QAAQ,UAAU;AAAA,EACnC,iBAAiB;AAAA,EACjB,MAAM;AACV;AAEO,IAAM,2BAA2B;AAAA,EACpC,YAAY;AAAA,EACZ,gBAAgB,CAAC,QAAQ,UAAU;AAAA,EACnC,iBAAiB;AAAA,EACjB,MAAM;AACV;AAEO,IAAM,wBAAwB;AAAA,EACjC,YAAY;AAAA,EACZ,gBAAgB,CAAC,QAAQ,UAAU;AAAA,EACnC,iBAAiB;AAAA,EACjB,MAAM;AACV;AAEO,IAAM,+BAAsG;AAAA,EAC/G,eAAe,6BAAM,IAAI,qBAAqB,GAA/B;AACnB;AAEO,IAAM,sBAAiF;AAAA,EAC1F,SAAS,6BAAM,YAAY,GAAlB;AAAA,EACT,kBAAkB,6BAAM,sBAAN;AAAA,EAClB,QAAQ,CAAC;AACb;AAEO,IAAM,wBAAmF;AAAA,EAC5F,SAAS,6BAAM,cAAc,GAApB;AAAA,EACT,kBAAkB,6BAAM,wBAAN;AAAA,EAClB,QAAQ,CAAC;AACb;AAEO,IAAM,qBAAgF;AAAA,EACzF,SAAS,6BAAM,WAAW,GAAjB;AAAA,EACT,kBAAkB,6BAAM,qBAAN;AAAA,EAClB,QAAQ,CAAC;AACb;AAEO,IAAM,8BAAyF;AAAA,EAClG,SAAS,6BAAM,oBAAoB,GAA1B;AAAA,EACT,kBAAkB,6BAAM,8BAAN;AAAA,EAClB,QAAQ,CAAC;AACb;AAEO,IAAM,0BAAqF;AAAA,EAC9F,SAAS,6BAAM,gBAAgB,GAAtB;AAAA,EACT,kBAAkB,6BAAM,0BAAN;AAAA,EAClB,QAAQ,CAAC;AACb;AAEO,IAAM,uBAAkF;AAAA,EAC3F,SAAS,6BAAM,aAAa,GAAnB;AAAA,EACT,kBAAkB,6BAAM,uBAAN;AAAA,EAClB,QAAQ,CAAC;AACb;;;ACxFA,SAAS,6BAA6B;;;ACE/B,IAAM,0BAA0B;AAKhC,IAAM,0BAA0B;AAKhC,IAAM,aAAa;;;ADR1B,IAAM,eAAuC;AAAA,EAC3C,WAAW;AAAA,EACX,WAAW;AAAA,EACX,OAAO;AACT;AAEO,IAAe,gCAAf,cAAqD,sBAAsB;AAAA,EAXlF,OAWkF;AAAA;AAAA;AAAA,EAe7D,aACjB,MACA,OACA,SACW;AACX,QAAI,QAA+B,KAAK,mBAAmB,MAAM,OAAO,OAAO;AAE/E,QAAI,UAAU,QAAW;AACvB,cAAQ,KAAK,mBAAmB,MAAM,OAAO,OAAO;AAAA,IACtD;AACA,QAAI,UAAU,QAAW;AACvB,aAAO,MAAM,aAAa,MAAM,OAAO,OAAO;AAAA,IAChD;AAEA,WAAO;AAAA,EACT;AAAA,EAEQ,mBACN,MACA,OACA,UACuB;AACvB,UAAM,QAA4B,aAAa,KAAK,IAAI;AACxD,QAAI,UAAU,QAAW;AACvB,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,MAAM,KAAK,KAAK;AAC9B,QAAI,UAAU,MAAM;AAClB,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,CAAC,MAAM,QAAW;AAC1B,aAAO,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,eAAe,GAAG;AAAA,IACnD;AAEA,QAAI,MAAM,CAAC,MAAM,QAAW;AAC1B,aAAO,MAAM,CAAC,EACX,QAAQ,UAAU,EAAE,EACpB,QAAQ,UAAU,EAAE,EACpB,QAAQ,eAAe,GAAG,EAC1B,QAAQ,gBAAgB,IAAI;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AACF;AAEO,IAAM,uBAAN,cAAmC,8BAA8B;AAAA,EAxExE,OAwEwE;AAAA;AAAA;AAAA,EACnD,mBACjB,OACA,QACA,UACuB;AACvB,WAAO;AAAA,EACT;AACF;;;AE7EA,SAAS,2BAA2B;AAE7B,IAAe,8BAAf,cAAmD,oBAAoB;AAAA,EAL9E,OAK8E;AAAA;AAAA;AAAA,EAGrE,YAAY,UAAoB;AACrC,UAAM;AACN,SAAK,WAAW,IAAI,IAAY,QAAQ;AAAA,EAC1C;AAAA,EAEmB,mBACjB,OACA,gBACA,SACa;AACb,UAAM,aAA0B,MAAM,mBAAmB,OAAO,gBAAgB,OAAO;AAEvF,eAAW,QAAQ,CAAC,cAA+B;AACjD,UAAI,KAAK,SAAS,IAAI,UAAU,IAAI,KAAK,UAAU,YAAY,QAAW;AAExE,kBAAU,UAAU,IAAI,OAAO,UAAU,QAAQ,SAAS,IAAI,oBAAoB;AAAA,MACpF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAEO,IAAM,qBAAN,cAAiC,4BAA4B;AAAA,EA9BpE,OA8BoE;AAAA;AAAA;AAAC;", "names": []}